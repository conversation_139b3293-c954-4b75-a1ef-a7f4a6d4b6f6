#!/usr/bin/env python3

import logging
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from sqlalchemy import text, inspect

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_tutor_profile_fields():
    """Add missing fields to tutorprofile table"""
    db = SessionLocal()

    try:
        # Test database connection first
        print("Testing database connection...")
        logger.info("Testing database connection...")
        result = db.execute(text("SELECT 1")).fetchone()
        print(f"Database connection successful: {result[0]}")
        logger.info(f"Database connection successful: {result[0]}")

        # Check current table structure
        inspector = inspect(db.bind)
        columns = inspector.get_columns('tutorprofile')
        existing_columns = [col['name'] for col in columns]
        logger.info(f"Existing columns: {existing_columns}")

        # Fields to add
        fields_to_add = [
            ('location', 'VARCHAR'),
            ('gender', 'VARCHAR'),
            ('languages', 'VARCHAR')
        ]

        # Add missing fields
        for field_name, field_type in fields_to_add:
            if field_name not in existing_columns:
                logger.info(f"Adding column: {field_name}")
                try:
                    db.execute(text(f"ALTER TABLE tutorprofile ADD COLUMN {field_name} {field_type}"))
                    db.commit()
                    logger.info(f"Successfully added column: {field_name}")
                except Exception as e:
                    logger.error(f"Error adding column {field_name}: {e}")
                    db.rollback()
            else:
                logger.info(f"Column {field_name} already exists")

        # Verify the changes
        columns = inspector.get_columns('tutorprofile')
        new_columns = [col['name'] for col in columns]
        logger.info(f"Updated columns: {new_columns}")

        logger.info("Successfully updated tutorprofile table!")

    except Exception as e:
        logger.error(f"Error during migration: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    add_tutor_profile_fields()
