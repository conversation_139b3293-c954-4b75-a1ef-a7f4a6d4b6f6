#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app import models
from app.services import department_service

def debug_departments():
    """Debug departments API"""
    db = SessionLocal()
    
    try:
        print("=== DEBUGGING DEPARTMENTS API ===")
        
        # Check database directly
        print("\n1. Direct database query:")
        departments = db.query(models.Department).all()
        print(f"Total departments in DB: {len(departments)}")
        for dept in departments[:5]:  # Show first 5
            print(f"  - {dept.name} (ID: {dept.id}, School ID: {dept.school_id})")
        
        # Check departments for school 1
        print("\n2. Departments for school 1:")
        school_1_depts = db.query(models.Department).filter(models.Department.school_id == 1).all()
        print(f"Departments for school 1: {len(school_1_depts)}")
        for dept in school_1_depts:
            print(f"  - {dept.name} (ID: {dept.id})")
        
        # Test service function
        print("\n3. Testing department service:")
        service_depts = department_service.get_by_school(db, school_id=1)
        print(f"Service returned: {len(service_depts)} departments")
        for dept in service_depts:
            print(f"  - {dept.name} (ID: {dept.id})")
        
        # Check schools
        print("\n4. Available schools:")
        schools = db.query(models.School).all()
        for school in schools:
            print(f"  - {school.name} (ID: {school.id})")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    debug_departments()
