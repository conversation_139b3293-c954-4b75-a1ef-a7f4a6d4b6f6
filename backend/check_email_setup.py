#!/usr/bin/env python3
"""
Email Setup Checker for CampusPQ
This script helps diagnose email configuration issues
"""

import os
import sys
import socket
import smtplib
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def check_network_connectivity():
    """Check if we can reach Gmail SMTP server"""
    print("🌐 Checking network connectivity to Gmail SMTP...")
    try:
        # Try to connect to Gmail SMTP server
        sock = socket.create_connection(("smtp.gmail.com", 587), timeout=10)
        sock.close()
        print("✅ Network connection to smtp.gmail.com:587 is working")
        return True
    except Exception as e:
        print(f"❌ Cannot connect to smtp.gmail.com:587: {e}")
        print("   This could be due to:")
        print("   - Firewall blocking outbound connections")
        print("   - Network restrictions")
        print("   - ISP blocking SMTP ports")
        return False

def check_smtp_auth():
    """Check SMTP authentication"""
    print("\n🔐 Checking SMTP authentication...")
    try:
        from app.core.config import settings
        
        if not settings.SMTP_USER or not settings.SMTP_PASSWORD:
            print("❌ SMTP credentials not configured")
            return False
        
        print(f"📧 Using SMTP user: {settings.SMTP_USER}")
        print(f"🔑 Password length: {len(settings.SMTP_PASSWORD)} characters")
        
        # Try to authenticate
        with smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT) as server:
            server.starttls()
            server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
            print("✅ SMTP authentication successful")
            return True
            
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ SMTP authentication failed: {e}")
        print("   Solutions:")
        print("   1. Enable 2-Factor Authentication on your Gmail account")
        print("   2. Generate an App Password (16 characters)")
        print("   3. Use the App Password, not your regular Gmail password")
        return False
    except Exception as e:
        print(f"❌ SMTP connection failed: {e}")
        return False

def check_email_config():
    """Check email configuration"""
    print("\n⚙️  Checking email configuration...")
    try:
        from app.core.config import settings
        
        print(f"SMTP Host: {settings.SMTP_HOST}")
        print(f"SMTP Port: {settings.SMTP_PORT}")
        print(f"SMTP User: {settings.SMTP_USER}")
        print(f"From Email: {settings.EMAILS_FROM_EMAIL}")
        print(f"From Name: {settings.EMAILS_FROM_NAME}")
        print(f"Frontend URL: {settings.FRONTEND_URL}")
        
        # Check if all required settings are present
        required_settings = [
            settings.SMTP_HOST,
            settings.SMTP_PORT,
            settings.SMTP_USER,
            settings.SMTP_PASSWORD,
        ]
        
        if all(required_settings):
            print("✅ All required email settings are configured")
            return True
        else:
            print("❌ Some required email settings are missing")
            return False
            
    except Exception as e:
        print(f"❌ Error checking configuration: {e}")
        return False

def check_development_fallback():
    """Check if development email fallback is working"""
    print("\n📁 Checking development email fallback...")
    try:
        from app.services.email_service import _save_email_to_file
        
        test_result = _save_email_to_file(
            email_to="<EMAIL>",
            subject="Test Email",
            html_content="<p>This is a test email</p>",
            text_content="This is a test email"
        )
        
        if test_result:
            print("✅ Development email fallback is working")
            
            # Check if development_emails directory exists
            dev_emails_dir = backend_dir / "development_emails"
            if dev_emails_dir.exists():
                email_files = list(dev_emails_dir.glob("*.html"))
                print(f"📧 Found {len(email_files)} development email files")
            
            return True
        else:
            print("❌ Development email fallback failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing development fallback: {e}")
        return False

def main():
    """Main diagnostic function"""
    print("🔧 CampusPQ Email Setup Checker")
    print("=" * 50)
    
    # Run all checks
    checks = [
        ("Configuration", check_email_config),
        ("Network Connectivity", check_network_connectivity),
        ("SMTP Authentication", check_smtp_auth),
        ("Development Fallback", check_development_fallback),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ Error during {check_name} check: {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n📊 Summary:")
    print("-" * 30)
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{check_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All checks passed! Email system is fully functional.")
    elif any(name == "Development Fallback" and result for name, result in results):
        print("⚠️  SMTP email sending is not working, but development fallback is functional.")
        print("📧 Emails will be saved as HTML files in the 'development_emails' folder.")
        print("💡 To enable actual email sending, fix the SMTP configuration issues above.")
    else:
        print("❌ Email system has issues. Please fix the failed checks above.")

if __name__ == "__main__":
    main()
