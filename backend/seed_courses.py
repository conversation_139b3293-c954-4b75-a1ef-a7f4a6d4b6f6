#!/usr/bin/env python3

import logging
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app import models
from sqlalchemy import text

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def seed_courses():
    """Seed database with sample courses"""
    db = SessionLocal()

    try:
        # Test database connection first
        logger.info("Testing database connection...")
        result = db.execute(text("SELECT 1")).fetchone()
        logger.info(f"Database connection successful: {result[0]}")

        # Check if we already have courses
        existing_courses = db.query(models.Course).count()
        logger.info(f"Found {existing_courses} existing courses")

        if existing_courses > 0:
            logger.info("Courses already exist, skipping...")
            return
        
        # Get schools and departments
        schools = db.query(models.School).all()
        if not schools:
            logger.error("No schools found. Please run seed_data.py first.")
            return
            
        logger.info(f"Found {len(schools)} schools")
        
        # Sample courses for each department
        course_templates = {
            "Computer Science": [
                {"name": "Introduction to Programming", "code": "CSC101", "description": "Basic programming concepts"},
                {"name": "Data Structures", "code": "CSC201", "description": "Fundamental data structures"},
                {"name": "Algorithms", "code": "CSC301", "description": "Algorithm design and analysis"},
                {"name": "Database Systems", "code": "CSC401", "description": "Database design and management"},
            ],
            "Mathematics": [
                {"name": "Calculus I", "code": "MTH101", "description": "Differential calculus"},
                {"name": "Linear Algebra", "code": "MTH201", "description": "Vector spaces and matrices"},
                {"name": "Statistics", "code": "MTH301", "description": "Statistical methods"},
                {"name": "Discrete Mathematics", "code": "MTH401", "description": "Mathematical logic and structures"},
            ],
            "Physics": [
                {"name": "General Physics I", "code": "PHY101", "description": "Mechanics and thermodynamics"},
                {"name": "Electromagnetism", "code": "PHY201", "description": "Electric and magnetic fields"},
                {"name": "Quantum Physics", "code": "PHY301", "description": "Quantum mechanics principles"},
                {"name": "Modern Physics", "code": "PHY401", "description": "Relativity and atomic physics"},
            ],
            "Chemistry": [
                {"name": "General Chemistry", "code": "CHM101", "description": "Basic chemical principles"},
                {"name": "Organic Chemistry", "code": "CHM201", "description": "Carbon-based compounds"},
                {"name": "Physical Chemistry", "code": "CHM301", "description": "Chemical thermodynamics"},
                {"name": "Analytical Chemistry", "code": "CHM401", "description": "Chemical analysis methods"},
            ],
            "Economics": [
                {"name": "Microeconomics", "code": "ECO101", "description": "Individual economic behavior"},
                {"name": "Macroeconomics", "code": "ECO201", "description": "National economic systems"},
                {"name": "Development Economics", "code": "ECO301", "description": "Economic development theories"},
                {"name": "International Trade", "code": "ECO401", "description": "Global trade and economics"},
            ]
        }
        
        courses_created = 0
        
        # Create courses for each school and department
        for school in schools:
            logger.info(f"Processing school: {school.name}")
            departments = db.query(models.Department).filter(models.Department.school_id == school.id).all()
            
            for department in departments:
                if department.name in course_templates:
                    logger.info(f"  Processing department: {department.name}")
                    
                    for course_data in course_templates[department.name]:
                        course = models.Course(
                            name=course_data["name"],
                            code=f"{course_data['code']}-{school.id}",  # Make code unique per school
                            description=course_data["description"],
                            school_id=school.id,
                            department_id=department.id,
                            is_active=True
                        )
                        db.add(course)
                        courses_created += 1
                        logger.info(f"    Created course: {course.name} ({course.code})")
        
        db.commit()
        logger.info(f"Successfully seeded {courses_created} courses!")
        
        # Print summary
        total_courses = db.query(models.Course).count()
        logger.info(f"Total courses in database: {total_courses}")
        
    except Exception as e:
        logger.error(f"Error during seeding: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    seed_courses()
