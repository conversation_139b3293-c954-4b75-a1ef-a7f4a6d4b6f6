# Gmail SMTP Setup Guide for CampusPQ

## Current Issue
The email sending functionality may fail due to Gmail SMTP authentication issues. This guide helps you set up Gmail app passwords correctly.

## Gmail App Password Setup

### Step 1: Enable 2-Factor Authentication
1. Go to your Google Account settings: https://myaccount.google.com/
2. Navigate to "Security" → "2-Step Verification"
3. Enable 2-Step Verification if not already enabled

### Step 2: Generate App Password
1. Go to "Security" → "App passwords"
2. Select "Mail" as the app
3. Select "Other (custom name)" as the device
4. Enter "CampusPQ Development" as the name
5. Click "Generate"
6. Copy the 16-character app password (format: xxxx xxxx xxxx xxxx)

### Step 3: Update .env File
Replace the current SMTP_PASSWORD in your `.env` file:

```env
SMTP_PASSWORD=your_16_character_app_password_here
```

**Important**: Use the app password, NOT your regular Gmail password!

## Current Configuration
Check your current `.env` file has these settings:

```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password_here
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=CampusPQ Development
```

## Testing Email Functionality

### Method 1: Using the Test Script
```bash
cd backend
python test_email.py
```

### Method 2: Using API Endpoints
```bash
# Test SMTP connection
curl -X POST "http://localhost:8001/api/v1/auth/test-smtp" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Test verification email
curl -X POST "http://localhost:8001/api/v1/auth/test-email" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

## Troubleshooting

### Common Issues:

1. **Authentication Error (535)**
   - Solution: Use Gmail app password, not regular password
   - Ensure 2FA is enabled on your Google account

2. **Connection Timeout**
   - Check firewall settings
   - Verify network connectivity to smtp.gmail.com:587

3. **Recipient Refused**
   - Verify the recipient email address is valid
   - Check if Gmail is blocking the recipient

### Development Fallback
If SMTP fails, emails are automatically saved to the `development_emails/` folder as HTML files for testing purposes.

## Security Notes
- Never commit real Gmail credentials to version control
- Use environment variables for sensitive configuration
- Consider using a dedicated Gmail account for development
- In production, use a proper email service like SendGrid, Mailgun, or AWS SES
