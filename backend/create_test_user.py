#!/usr/bin/env python3

import sqlite3
import os
from datetime import datetime
import hashlib
import secrets

def create_test_user():
    """Create a test user directly in the database"""
    db_path = os.path.join(os.path.dirname(__file__), 'campuspq.db')
    print(f"Database path: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if test user already exists
        cursor.execute("SELECT id, email, email_verified, profile_completed FROM user WHERE email = ?", ("<EMAIL>",))
        existing_user = cursor.fetchone()
        
        if existing_user:
            user_id, email, email_verified, profile_completed = existing_user
            print(f"Test user already exists:")
            print(f"  ID: {user_id}")
            print(f"  Email: {email}")
            print(f"  Email verified: {bool(email_verified)}")
            print(f"  Profile completed: {bool(profile_completed)}")
            
            # Update to ensure email is verified and profile is not completed
            cursor.execute("""
                UPDATE user 
                SET email_verified = 1, 
                    profile_completed = 0,
                    email_verification_token = NULL,
                    email_verification_token_expires = NULL
                WHERE email = ?
            """, ("<EMAIL>",))
            print("Updated test user: email verified = True, profile completed = False")
        else:
            # Create new test user
            # Hash password (simple bcrypt-like hash for testing)
            password = "testpassword123"
            # Simple hash for testing - in production this would use proper bcrypt
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            now = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT INTO user (
                    email, hashed_password, full_name, role, is_active, 
                    email_verified, profile_completed, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                "<EMAIL>",
                hashed_password,
                "Test User",
                "student",
                1,  # is_active = True
                1,  # email_verified = True
                0,  # profile_completed = False
                now,
                now
            ))
            
            user_id = cursor.lastrowid
            print(f"Created new test user with ID: {user_id}")
        
        conn.commit()
        
        # Get the final user data
        cursor.execute("SELECT * FROM user WHERE email = ?", ("<EMAIL>",))
        user_data = cursor.fetchone()
        
        print(f"\n=== TEST USER READY ===")
        print(f"Email: <EMAIL>")
        print(f"Password: testpassword123")
        print(f"Email verified: {bool(user_data[7])}")  # email_verified column
        print(f"Profile completed: {bool(user_data[8])}")  # profile_completed column
        
        print(f"\n=== LOGIN TEST ===")
        print(f"Try logging in with:")
        print(f"curl -X POST 'http://localhost:8000/api/v1/auth/login' \\")
        print(f"  -H 'Content-Type: application/x-www-form-urlencoded' \\")
        print(f"  -d 'username=<EMAIL>&password=testpassword123'")
        
    except Exception as e:
        print(f"Error: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    create_test_user()
