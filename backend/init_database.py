#!/usr/bin/env python3

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.session import engine
from app.db.base import Base

def init_database():
    """Initialize the database with all tables"""
    try:
        print("Creating all database tables...")
        Base.metadata.create_all(bind=engine)
        print("Database tables created successfully!")
        
        # List created tables
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        print(f"Created tables: {tables}")
        
        # Check tutorprofile table structure
        if 'tutorprofile' in tables:
            columns = inspector.get_columns('tutorprofile')
            column_names = [col['name'] for col in columns]
            print(f"TutorProfile columns: {column_names}")
        
    except Exception as e:
        print(f"Error initializing database: {e}")
        raise

if __name__ == "__main__":
    init_database()
