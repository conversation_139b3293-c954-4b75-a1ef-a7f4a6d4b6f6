-- Add sample courses to the database
-- First, let's check if we have schools and departments

-- Insert sample courses for Computer Science departments
INSERT OR IGNORE INTO course (name, code, description, school_id, department_id, is_active, created_at, updated_at)
SELECT 
    'Introduction to Programming' as name,
    'CSC101' as code,
    'Basic programming concepts and fundamentals' as description,
    s.id as school_id,
    d.id as department_id,
    1 as is_active,
    datetime('now') as created_at,
    datetime('now') as updated_at
FROM school s
JOIN department d ON s.id = d.school_id
WHERE d.name = 'Computer Science'
LIMIT 5;

INSERT OR IGNORE INTO course (name, code, description, school_id, department_id, is_active, created_at, updated_at)
SELECT 
    'Data Structures' as name,
    'CSC201' as code,
    'Fundamental data structures and algorithms' as description,
    s.id as school_id,
    d.id as department_id,
    1 as is_active,
    datetime('now') as created_at,
    datetime('now') as updated_at
FROM school s
JOIN department d ON s.id = d.school_id
WHERE d.name = 'Computer Science'
LIMIT 5;

INSERT OR IGNORE INTO course (name, code, description, school_id, department_id, is_active, created_at, updated_at)
SELECT 
    'Database Systems' as name,
    'CSC301' as code,
    'Database design and management' as description,
    s.id as school_id,
    d.id as department_id,
    1 as is_active,
    datetime('now') as created_at,
    datetime('now') as updated_at
FROM school s
JOIN department d ON s.id = d.school_id
WHERE d.name = 'Computer Science'
LIMIT 5;

-- Insert sample courses for Mathematics departments
INSERT OR IGNORE INTO course (name, code, description, school_id, department_id, is_active, created_at, updated_at)
SELECT 
    'Calculus I' as name,
    'MTH101' as code,
    'Differential and integral calculus' as description,
    s.id as school_id,
    d.id as department_id,
    1 as is_active,
    datetime('now') as created_at,
    datetime('now') as updated_at
FROM school s
JOIN department d ON s.id = d.school_id
WHERE d.name = 'Mathematics'
LIMIT 5;

INSERT OR IGNORE INTO course (name, code, description, school_id, department_id, is_active, created_at, updated_at)
SELECT 
    'Linear Algebra' as name,
    'MTH201' as code,
    'Vector spaces and linear transformations' as description,
    s.id as school_id,
    d.id as department_id,
    1 as is_active,
    datetime('now') as created_at,
    datetime('now') as updated_at
FROM school s
JOIN department d ON s.id = d.school_id
WHERE d.name = 'Mathematics'
LIMIT 5;

-- Insert sample courses for Physics departments
INSERT OR IGNORE INTO course (name, code, description, school_id, department_id, is_active, created_at, updated_at)
SELECT 
    'General Physics I' as name,
    'PHY101' as code,
    'Mechanics and thermodynamics' as description,
    s.id as school_id,
    d.id as department_id,
    1 as is_active,
    datetime('now') as created_at,
    datetime('now') as updated_at
FROM school s
JOIN department d ON s.id = d.school_id
WHERE d.name = 'Physics'
LIMIT 5;

-- Insert sample courses for Chemistry departments
INSERT OR IGNORE INTO course (name, code, description, school_id, department_id, is_active, created_at, updated_at)
SELECT 
    'General Chemistry' as name,
    'CHM101' as code,
    'Basic chemical principles and reactions' as description,
    s.id as school_id,
    d.id as department_id,
    1 as is_active,
    datetime('now') as created_at,
    datetime('now') as updated_at
FROM school s
JOIN department d ON s.id = d.school_id
WHERE d.name = 'Chemistry'
LIMIT 5;

-- Insert sample courses for Economics departments
INSERT OR IGNORE INTO course (name, code, description, school_id, department_id, is_active, created_at, updated_at)
SELECT 
    'Microeconomics' as name,
    'ECO101' as code,
    'Individual economic behavior and market theory' as description,
    s.id as school_id,
    d.id as department_id,
    1 as is_active,
    datetime('now') as created_at,
    datetime('now') as updated_at
FROM school s
JOIN department d ON s.id = d.school_id
WHERE d.name = 'Economics'
LIMIT 5;

-- Check the results
SELECT COUNT(*) as total_courses FROM course;
SELECT s.name as school, d.name as department, c.name as course, c.code 
FROM course c 
JOIN school s ON c.school_id = s.id 
JOIN department d ON c.department_id = d.id 
ORDER BY s.name, d.name, c.name;
