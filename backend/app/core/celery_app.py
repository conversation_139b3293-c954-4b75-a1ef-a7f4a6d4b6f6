from celery import Celery

from app.core.config import settings

celery_app = Celery(
    "worker",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=["app.tasks"],
)

# Use default queue for all tasks to ensure they are processed
# celery_app.conf.task_routes = {
#     "app.tasks.process_pdf_for_questions": "main-queue",
#     "app.tasks.generate_ai_questions": "ai-queue",
#     "app.tasks.process_note": "note-queue",
#     "app.tasks.generate_mcqs": "mcq-queue",
#     "app.tasks.generate_flash_cards": "flash-card-queue",
# }

celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
)
