from datetime import datetime
from typing import Dict, List, Optional, Any

from pydantic import BaseModel, ConfigDict

from app.models.student_tools import ProcessingStatus


# StudentNote schemas
class StudentNoteBase(BaseModel):
    file_name: str
    file_size: int
    content_type: str


class StudentNoteCreate(StudentNoteBase):
    student_id: int
    file_path: str


class StudentNoteUpdate(BaseModel):
    status: Optional[ProcessingStatus] = None
    embedding_id: Optional[str] = None
    course_name: Optional[str] = None
    detected_topics: Optional[List[str]] = None
    processing_metadata: Optional[Dict[str, Any]] = None


class StudentNoteInDBBase(StudentNoteBase):
    id: int
    student_id: int
    file_path: str
    status: ProcessingStatus
    embedding_id: Optional[str] = None
    course_name: Optional[str] = None
    detected_topics: Optional[List[str]] = None
    processing_metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class StudentNote(StudentNoteInDBBase):
    pass


# NoteGeneratedMCQ schemas
class NoteGeneratedMCQBase(BaseModel):
    note_id: int
    question_id: int


class NoteGeneratedMCQCreate(NoteGeneratedMCQBase):
    pass


class NoteGeneratedMCQInDBBase(NoteGeneratedMCQBase):
    id: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class NoteGeneratedMCQ(NoteGeneratedMCQInDBBase):
    pass


# MCQGenerationJob schemas
class MCQGenerationJobBase(BaseModel):
    student_id: int
    note_ids: List[int]
    title: Optional[str] = None
    course_name: Optional[str] = None
    question_count: int = 60


class MCQGenerationJobCreate(MCQGenerationJobBase):
    pass


class MCQGenerationJobUpdate(BaseModel):
    status: Optional[ProcessingStatus] = None
    title: Optional[str] = None
    generated_question_ids: Optional[List[int]] = None
    progress_percentage: Optional[int] = None
    error_message: Optional[str] = None


class MCQGenerationJobInDBBase(MCQGenerationJobBase):
    id: int
    status: ProcessingStatus
    generated_question_ids: Optional[List[int]] = None
    progress_percentage: int
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class MCQGenerationJob(MCQGenerationJobInDBBase):
    pass


# Additional schemas for API requests and responses
class StudentNoteUploadResponse(BaseModel):
    note_id: int
    file_name: str
    status: ProcessingStatus
    message: str


class MCQGenerationRequest(BaseModel):
    note_ids: List[int]
    course_name: Optional[str] = None
    question_count: int = 60


class MCQGenerationResponse(BaseModel):
    job_id: int
    status: ProcessingStatus
    progress_percentage: int
    message: str


class MCQGenerationStatusResponse(BaseModel):
    job_id: int
    status: ProcessingStatus
    progress_percentage: int
    note_ids: List[int]
    title: Optional[str] = None
    course_name: Optional[str] = None
    question_count: int
    generated_question_ids: Optional[List[int]] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class ExamConfigRequest(BaseModel):
    time_limit: int = 60  # Default: 60 minutes
    question_count: int = 60  # Default: 60 questions


class ExamConfigResponse(BaseModel):
    time_limit: int
    question_count: int
    message: str = "Exam configuration saved successfully"


# FlashCard schemas
class FlashCardBase(BaseModel):
    front_content: str
    back_content: str
    topic: Optional[str] = None


class FlashCardCreate(FlashCardBase):
    student_id: int
    course_name: Optional[str] = None
    job_id: Optional[int] = None


class FlashCardUpdate(BaseModel):
    front_content: Optional[str] = None
    back_content: Optional[str] = None
    topic: Optional[str] = None
    course_name: Optional[str] = None
    job_id: Optional[int] = None


class FlashCardInDBBase(FlashCardBase):
    id: int
    student_id: int
    course_name: Optional[str] = None
    job_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class FlashCard(FlashCardInDBBase):
    pass


# NoteGeneratedFlashCard schemas
class NoteGeneratedFlashCardBase(BaseModel):
    note_id: int
    flash_card_id: int


class NoteGeneratedFlashCardCreate(NoteGeneratedFlashCardBase):
    pass


class NoteGeneratedFlashCardInDBBase(NoteGeneratedFlashCardBase):
    id: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class NoteGeneratedFlashCard(NoteGeneratedFlashCardInDBBase):
    pass


# FlashCardGenerationJob schemas
class FlashCardGenerationJobBase(BaseModel):
    student_id: int
    note_ids: List[int]
    title: Optional[str] = None
    course_name: Optional[str] = None
    card_count: int = 30


class FlashCardGenerationJobCreate(FlashCardGenerationJobBase):
    pass


class FlashCardGenerationJobUpdate(BaseModel):
    status: Optional[ProcessingStatus] = None
    title: Optional[str] = None
    generated_card_ids: Optional[List[int]] = None
    progress_percentage: Optional[int] = None
    error_message: Optional[str] = None


class FlashCardGenerationJobInDBBase(FlashCardGenerationJobBase):
    id: int
    status: ProcessingStatus
    generated_card_ids: Optional[List[int]] = None
    progress_percentage: int
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class FlashCardGenerationJob(FlashCardGenerationJobInDBBase):
    pass


# Additional schemas for flash card API requests and responses
class FlashCardGenerationRequest(BaseModel):
    note_ids: List[int]
    course_name: Optional[str] = None
    card_count: int = 30


class FlashCardGenerationResponse(BaseModel):
    job_id: int
    status: ProcessingStatus
    progress_percentage: int
    message: str


class FlashCardGenerationStatusResponse(BaseModel):
    job_id: int
    status: ProcessingStatus
    progress_percentage: int
    note_ids: List[int]
    title: Optional[str] = None
    course_name: Optional[str] = None
    card_count: int
    generated_card_ids: Optional[List[int]] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime


# Summary schemas
class NoteSummaryBase(BaseModel):
    title: str
    content: str
    key_concepts: Optional[List[str]] = None
    structure: Optional[dict] = None
    topics: Optional[List[str]] = None


class NoteSummaryCreate(NoteSummaryBase):
    student_id: int
    course_name: Optional[str] = None
    job_id: Optional[int] = None


class NoteSummaryUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    key_concepts: Optional[List[str]] = None
    structure: Optional[dict] = None
    course_name: Optional[str] = None
    topics: Optional[List[str]] = None
    job_id: Optional[int] = None


class NoteSummaryInDBBase(NoteSummaryBase):
    id: int
    student_id: int
    course_name: Optional[str] = None
    job_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class NoteSummary(NoteSummaryInDBBase):
    pass


# NoteGeneratedSummary schemas
class NoteGeneratedSummaryBase(BaseModel):
    note_id: int
    summary_id: int


class NoteGeneratedSummaryCreate(NoteGeneratedSummaryBase):
    pass


class NoteGeneratedSummaryInDBBase(NoteGeneratedSummaryBase):
    id: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class NoteGeneratedSummary(NoteGeneratedSummaryInDBBase):
    pass


# SummaryGenerationJob schemas
class SummaryGenerationJobBase(BaseModel):
    student_id: int
    note_ids: List[int]
    course_name: Optional[str] = None


class SummaryGenerationJobCreate(SummaryGenerationJobBase):
    pass


class SummaryGenerationJobUpdate(BaseModel):
    status: Optional[ProcessingStatus] = None
    generated_summary_ids: Optional[List[int]] = None
    progress_percentage: Optional[int] = None
    error_message: Optional[str] = None
    processing_metadata: Optional[dict] = None


class SummaryGenerationJobInDBBase(SummaryGenerationJobBase):
    id: int
    status: ProcessingStatus
    generated_summary_ids: Optional[List[int]] = None
    progress_percentage: int
    error_message: Optional[str] = None
    processing_metadata: Optional[dict] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class SummaryGenerationJob(SummaryGenerationJobInDBBase):
    pass


# Additional schemas for summary API requests and responses
class SummaryGenerationRequest(BaseModel):
    note_ids: List[int]
    course_name: Optional[str] = None


class SummaryGenerationResponse(BaseModel):
    job_id: int
    status: ProcessingStatus
    progress_percentage: int
    message: str


class SummaryGenerationStatusResponse(BaseModel):
    job_id: int
    status: ProcessingStatus
    progress_percentage: int
    note_ids: List[int]
    course_name: Optional[str] = None
    generated_summary_ids: Optional[List[int]] = None
    error_message: Optional[str] = None
    processing_metadata: Optional[dict] = None
    created_at: datetime
    updated_at: datetime
