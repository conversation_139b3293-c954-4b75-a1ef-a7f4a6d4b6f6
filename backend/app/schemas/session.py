from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict

from app.models.session import SessionStatus, SessionType


# Shared properties
class SessionBase(BaseModel):
    title: str
    description: str
    session_type: SessionType
    status: SessionStatus = SessionStatus.SCHEDULED
    start_time: datetime
    end_time: datetime
    location: Optional[str] = None
    video_link: Optional[str] = None
    max_students: int = 1
    is_recurring: bool = False
    recurrence_pattern: Optional[str] = None
    course_id: int
    tutor_id: int


# Properties to receive via API on creation
class SessionCreate(SessionBase):
    pass


# Properties to receive via API on update
class SessionUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    session_type: Optional[SessionType] = None
    status: Optional[SessionStatus] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    location: Optional[str] = None
    video_link: Optional[str] = None
    max_students: Optional[int] = None
    is_recurring: Optional[bool] = None
    recurrence_pattern: Optional[str] = None
    course_id: Optional[int] = None


# Properties shared by models stored in DB
class SessionInDBBase(SessionBase):
    id: int
    student_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Properties to return via API
class Session(SessionInDBBase):
    pass


# Session booking request
class SessionBookingRequest(BaseModel):
    session_id: int
    student_id: int
