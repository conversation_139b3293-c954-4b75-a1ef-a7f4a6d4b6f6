from datetime import datetime, date
from typing import List, Optional

from pydantic import BaseModel, EmailStr, Field, ConfigDict

from app.models.user import UserRole, Gender
from app.schemas.course import Course


# Shared properties
class UserBase(BaseModel):
    email: EmailStr
    full_name: str
    role: UserRole
    is_active: bool = True
    email_verified: bool = False


# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str


# Properties for profile completion
class ProfileComplete(BaseModel):
    other_name: Optional[str] = None
    gender: Optional[Gender] = None
    phone_number: Optional[str] = None
    department: Optional[str] = None  # Legacy field, kept for backward compatibility
    department_id: Optional[int] = None
    level: Optional[str] = None
    date_of_birth: Optional[date] = None
    state_of_origin: Optional[str] = None
    institution_id: Optional[int] = None


# Properties to receive via API on update
class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    other_name: Optional[str] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None
    gender: Optional[Gender] = None
    phone_number: Optional[str] = None
    department: Optional[str] = None  # Legacy field, kept for backward compatibility
    department_id: Optional[int] = None
    level: Optional[str] = None
    date_of_birth: Optional[date] = None
    state_of_origin: Optional[str] = None
    profile_picture_url: Optional[str] = None
    institution_id: Optional[int] = None
    profile_completed: Optional[bool] = None


# Properties shared by models stored in DB
class UserInDBBase(UserBase):
    id: int
    other_name: Optional[str] = None
    gender: Optional[Gender] = None
    phone_number: Optional[str] = None
    department: Optional[str] = None  # Legacy field, kept for backward compatibility
    department_id: Optional[int] = None
    level: Optional[str] = None
    date_of_birth: Optional[date] = None
    state_of_origin: Optional[str] = None
    profile_picture_url: Optional[str] = None
    profile_completed: bool = False
    institution_id: Optional[int] = None
    email_verification_token: Optional[str] = None
    email_verification_token_expires: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Properties to return via API
class User(UserInDBBase):
    courses: Optional[List[Course]] = None


# Email verification schemas
class EmailVerificationRequest(BaseModel):
    email: EmailStr


class EmailVerificationConfirm(BaseModel):
    token: str


class EmailVerificationResponse(BaseModel):
    message: str
    success: bool


# Password reset schemas
class PasswordResetRequest(BaseModel):
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str


class PasswordResetResponse(BaseModel):
    message: str
    success: bool


# Properties stored in DB but not returned by API
class UserInDB(UserInDBBase):
    hashed_password: str
