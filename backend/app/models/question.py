from datetime import datetime, timezone
from enum import Enum
from typing import List, Optional, TYPE_CHECKING

from sqlalchemy import Enum as SQLAlchemyEnum, <PERSON><PERSON><PERSON>, <PERSON>SO<PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base

# Use timezone-aware UTC now function to replace deprecated datetime.utcnow
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.student_progress import StudentQuestionAttempt, StudentBookmark, StudentExamAttempt
    from app.models.ai_analytics import AIAssistantInteraction
    from app.models.student_tools import NoteGeneratedMCQ


class QuestionType(str, Enum):
    MULTIPLE_CHOICE = "multiple_choice"
    FLASHCARD = "flashcard"
    OPEN_ENDED = "open_ended"


class DifficultyLevel(str, Enum):
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"


class Question(Base):
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    content: Mapped[str]
    question_type: Mapped[QuestionType] = mapped_column(SQLAlchemyEnum(QuestionType))
    difficulty: Mapped[DifficultyLevel] = mapped_column(SQLAlchemyEnum(DifficultyLevel))
    topic: Mapped[Optional[str]] = mapped_column(index=True, nullable=True)  # Topic the question belongs to
    options: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)  # For multiple choice
    answer: Mapped[str]
    explanation: Mapped[Optional[str]] = mapped_column(nullable=True)
    media_url: Mapped[Optional[str]] = mapped_column(nullable=True)  # For images, diagrams, videos
    is_active: Mapped[bool] = mapped_column(default=True)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)

    # Course information - for note-generated questions, course_name is used instead of course_id
    course_name: Mapped[Optional[str]] = mapped_column(index=True, nullable=True)  # Course name from PDF upload

    # Relationships
    course_id: Mapped[Optional[int]] = mapped_column(ForeignKey("course.id"), nullable=True)
    course = relationship("Course", back_populates="questions")

    created_by_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    created_by = relationship("User", back_populates="questions")

    # For note-generated questions - temporarily commented out to fix import issues
    # mcq_job_id: Mapped[Optional[int]] = mapped_column(ForeignKey("mcqgenerationjob.id"), nullable=True)
    # mcq_job = relationship("MCQGenerationJob")

    # Student progress tracking relationships - temporarily commented out to fix import issues
    # student_attempts: Mapped[List["StudentQuestionAttempt"]] = relationship("StudentQuestionAttempt", back_populates="question")
    # bookmarks: Mapped[List["StudentBookmark"]] = relationship("StudentBookmark", back_populates="question")
    # exam_attempts: Mapped[List["StudentExamAttempt"]] = relationship("StudentExamAttempt", back_populates="question")

    # AI assistant interactions removed to fix circular dependency

    # Student tools relationships - temporarily commented out to fix import issues
    # note_generated: Mapped[List["NoteGeneratedMCQ"]] = relationship("NoteGeneratedMCQ", back_populates="question")
