from datetime import datetime, timezone
from enum import Enum
from typing import List, Optional, TYPE_CHECKING, Dict

from sqlalchemy import Enum as SQLAlchemyEnum, ForeignKey, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base

# Use timezone-aware UTC now function to replace deprecated datetime.utcnow
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.user import User
    from app.models.course import Course
    from app.models.question import Question


class ProcessingStatus(str, Enum):
    PENDING = "pending"
    UPLOADING = "uploading"
    PROCESSING = "processing"
    EMBEDDING = "embedding"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"


class StudentNote(Base):
    """Model to store student uploaded notes"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    file_name: Mapped[str]
    file_path: Mapped[str]
    file_size: Mapped[int]  # Size in bytes
    content_type: Mapped[str]
    status: Mapped[ProcessingStatus] = mapped_column(SQLAlchemyEnum(ProcessingStatus), default=ProcessingStatus.PENDING)
    embedding_id: Mapped[Optional[str]] = mapped_column(nullable=True)  # ID in vector store
    course_name: Mapped[Optional[str]] = mapped_column(nullable=True)  # Custom course name entered by student
    detected_topics: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True)
    processing_metadata: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)  # For tracking progress details
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)

    # Relationships - temporarily commented out to fix import issues
    # student = relationship("User", back_populates="notes")
    generated_mcqs = relationship("NoteGeneratedMCQ", back_populates="source_note")
    generated_flash_cards = relationship("NoteGeneratedFlashCard", back_populates="source_note")
    generated_summaries = relationship("NoteGeneratedSummary", back_populates="source_note")


class NoteGeneratedMCQ(Base):
    """Model to store MCQs generated from student notes"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    note_id: Mapped[int] = mapped_column(ForeignKey("studentnote.id"))
    question_id: Mapped[int] = mapped_column(ForeignKey("question.id"))
    created_at: Mapped[datetime] = mapped_column(default=utc_now)

    # Relationships - temporarily commented out to fix import issues
    source_note = relationship("StudentNote", back_populates="generated_mcqs")
    # question = relationship("Question", back_populates="note_generated")  # This was commented out in question.py


class MCQGenerationJob(Base):
    """Model to track MCQ generation jobs"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    status: Mapped[ProcessingStatus] = mapped_column(SQLAlchemyEnum(ProcessingStatus), default=ProcessingStatus.PENDING)
    note_ids: Mapped[List[int]] = mapped_column(JSON)  # List of note IDs used for generation
    title: Mapped[Optional[str]] = mapped_column(nullable=True)  # Descriptive title for the MCQ set
    course_name: Mapped[Optional[str]] = mapped_column(nullable=True)  # Custom course name
    question_count: Mapped[int] = mapped_column(default=60)  # Default to 60 questions
    generated_question_ids: Mapped[Optional[List[int]]] = mapped_column(JSON, nullable=True)  # List of generated question IDs
    progress_percentage: Mapped[int] = mapped_column(default=0)
    error_message: Mapped[Optional[str]] = mapped_column(nullable=True)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)

    # Relationships - temporarily commented out to fix import issues
    # student = relationship("User", back_populates="mcq_generation_jobs")
    # questions = relationship("Question", back_populates="mcq_job")  # This was already commented out in question.py


class FlashCard(Base):
    """Model to store flash cards generated from student notes"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    front_content: Mapped[str]
    back_content: Mapped[str]
    course_name: Mapped[Optional[str]] = mapped_column(nullable=True)  # Custom course name
    topic: Mapped[Optional[str]] = mapped_column(nullable=True)
    job_id: Mapped[Optional[int]] = mapped_column(ForeignKey("flashcardgenerationjob.id"), nullable=True)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)

    # Relationships - temporarily commented out to fix import issues
    # student = relationship("User", back_populates="flash_cards")
    job = relationship("FlashCardGenerationJob")
    note_generated = relationship("NoteGeneratedFlashCard", back_populates="flash_card")


class NoteGeneratedFlashCard(Base):
    """Model to store flash cards generated from student notes"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    note_id: Mapped[int] = mapped_column(ForeignKey("studentnote.id"))
    flash_card_id: Mapped[int] = mapped_column(ForeignKey("flashcard.id"))
    created_at: Mapped[datetime] = mapped_column(default=utc_now)

    # Relationships
    source_note = relationship("StudentNote", back_populates="generated_flash_cards")
    flash_card = relationship("FlashCard", back_populates="note_generated")


class FlashCardGenerationJob(Base):
    """Model to track flash card generation jobs"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    status: Mapped[ProcessingStatus] = mapped_column(SQLAlchemyEnum(ProcessingStatus), default=ProcessingStatus.PENDING)
    note_ids: Mapped[List[int]] = mapped_column(JSON)  # List of note IDs used for generation
    title: Mapped[Optional[str]] = mapped_column(nullable=True)  # Descriptive title for the flash card set
    course_name: Mapped[Optional[str]] = mapped_column(nullable=True)  # Custom course name
    card_count: Mapped[int] = mapped_column(default=30)  # Default to 30 flash cards
    generated_card_ids: Mapped[Optional[List[int]]] = mapped_column(JSON, nullable=True)  # List of generated flash card IDs
    progress_percentage: Mapped[int] = mapped_column(default=0)
    error_message: Mapped[Optional[str]] = mapped_column(nullable=True)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)

    # Relationships - temporarily commented out to fix import issues
    # student = relationship("User", back_populates="flash_card_generation_jobs")


class NoteSummary(Base):
    """Model to store summaries generated from student notes"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    title: Mapped[str]  # Summary title/heading
    content: Mapped[str]  # Main summary content
    key_concepts: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True)  # Extracted key concepts
    structure: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)  # Hierarchical structure
    course_name: Mapped[Optional[str]] = mapped_column(nullable=True)  # Custom course name
    topics: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True)  # Detected topics
    job_id: Mapped[Optional[int]] = mapped_column(ForeignKey("summarygenerationjob.id"), nullable=True)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)

    # Relationships - temporarily commented out to fix import issues
    # student = relationship("User", back_populates="summaries")
    job = relationship("SummaryGenerationJob")
    note_generated = relationship("NoteGeneratedSummary", back_populates="summary")


class NoteGeneratedSummary(Base):
    """Model to store summaries generated from student notes"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    note_id: Mapped[int] = mapped_column(ForeignKey("studentnote.id"))
    summary_id: Mapped[int] = mapped_column(ForeignKey("notesummary.id"))
    created_at: Mapped[datetime] = mapped_column(default=utc_now)

    # Relationships
    source_note = relationship("StudentNote", back_populates="generated_summaries")
    summary = relationship("NoteSummary", back_populates="note_generated")


class SummaryGenerationJob(Base):
    """Model to track summary generation jobs"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    status: Mapped[ProcessingStatus] = mapped_column(SQLAlchemyEnum(ProcessingStatus), default=ProcessingStatus.PENDING)
    note_ids: Mapped[List[int]] = mapped_column(JSON)  # List of note IDs used for generation
    course_name: Mapped[Optional[str]] = mapped_column(nullable=True)  # Custom course name
    generated_summary_ids: Mapped[Optional[List[int]]] = mapped_column(JSON, nullable=True)  # List of generated summary IDs
    progress_percentage: Mapped[int] = mapped_column(default=0)
    error_message: Mapped[Optional[str]] = mapped_column(nullable=True)
    processing_metadata: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)  # Agent execution details
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)

    # Relationships - temporarily commented out to fix import issues
    # student = relationship("User", back_populates="summary_generation_jobs")
