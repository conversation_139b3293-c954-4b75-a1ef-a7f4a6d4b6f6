from datetime import datetime
from typing import List, Optional
from decimal import Decimal

from sqlalchemy import Foreign<PERSON>ey, Text, Numeric, Table, Column, Integer
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base


# Association table for tutor specializations
tutor_specialization = Table(
    'tutor_specialization',
    Base.metadata,
    Column('tutor_profile_id', Integer, ForeignKey('tutorprofile.id'), primary_key=True),
    Column('course_id', Integer, Foreign<PERSON>ey('course.id'), primary_key=True)
)


class TutorProfile(Base):
    """Extended profile information for tutors"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Basic tutor information
    bio: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    experience_years: Mapped[Optional[int]] = mapped_column(nullable=True)
    hourly_rate: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 2), nullable=True)
    
    # Availability and preferences
    is_available: Mapped[bool] = mapped_column(default=True)
    preferred_session_type: Mapped[Optional[str]] = mapped_column(nullable=True)  # 'online', 'in_person', 'both'
    max_students_per_session: Mapped[int] = mapped_column(default=1)
    
    # Contact and social information
    linkedin_url: Mapped[Optional[str]] = mapped_column(nullable=True)
    website_url: Mapped[Optional[str]] = mapped_column(nullable=True)

    # Additional profile information (with default values for backward compatibility)
    location: Mapped[Optional[str]] = mapped_column(nullable=True, default=None)  # City, State/Country
    gender: Mapped[Optional[str]] = mapped_column(nullable=True, default=None)  # 'male', 'female', 'other', 'prefer_not_to_say'
    languages: Mapped[Optional[str]] = mapped_column(nullable=True, default=None)  # JSON string of languages
    
    # Rating and reviews
    average_rating: Mapped[Optional[Decimal]] = mapped_column(Numeric(3, 2), nullable=True, default=0.0)
    total_reviews: Mapped[int] = mapped_column(default=0)
    total_sessions_completed: Mapped[int] = mapped_column(default=0)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), unique=True)
    user: Mapped["User"] = relationship("User", back_populates="tutor_profile")
    
    # Specializations (courses the tutor can teach)
    specializations: Mapped[List["Course"]] = relationship(
        "Course", 
        secondary=tutor_specialization, 
        back_populates="tutors"
    )
    
    # Reviews relationship (to be implemented later)
    # reviews: Mapped[List["TutorReview"]] = relationship("TutorReview", back_populates="tutor_profile")


class TutorReview(Base):
    """Reviews and ratings for tutors"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    rating: Mapped[int] = mapped_column()  # 1-5 stars
    comment: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    tutor_profile_id: Mapped[int] = mapped_column(ForeignKey("tutorprofile.id"))
    tutor_profile: Mapped["TutorProfile"] = relationship("TutorProfile")
    
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    student: Mapped["User"] = relationship("User")
    
    session_id: Mapped[Optional[int]] = mapped_column(ForeignKey("session.id"), nullable=True)
    session: Mapped[Optional["Session"]] = relationship("Session")
