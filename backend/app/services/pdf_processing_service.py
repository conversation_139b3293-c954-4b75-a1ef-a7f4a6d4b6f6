import logging
import os
import tempfile
import uuid
import asyncio
import io
from typing import Dict, List, Optional, Tuple

import fitz  # PyMuPDF
from fastapi import UploadFile

from app.core.config import settings
from app.models.student_tools import ProcessingStatus
from app.utils.file_handler import file_handler
from app.utils.vector_store import vector_store
from app.services.gemini_service import gemini_service, RateLimitError
from app.services.enhanced_pdf_service import enhanced_pdf_service
from app.services.ocr_service import ocr_service
from app.services.chunking_service import chunking_service
from app.services.enhanced_vector_store import enhanced_vector_store
# from app.services.langchain_service import langchain_service

logger = logging.getLogger(__name__)

# Maximum file size (30MB)
MAX_FILE_SIZE = 30 * 1024 * 1024

# Maximum number of notes per upload
MAX_NOTES_PER_UPLOAD = 3

# Allowed file types
ALLOWED_EXTENSIONS = ["pdf"]


class PDFProcessingService:
    """Service for processing PDF files and extracting text for vector embeddings."""

    async def validate_pdf_upload(self, files: List[UploadFile]) -> Tuple[bool, str]:
        """
        Validate PDF uploads.

        Args:
            files: List of uploaded files

        Returns:
            Tuple of (is_valid, error_message)
        """
        if not files:
            return False, "No files provided"

        if len(files) > MAX_NOTES_PER_UPLOAD:
            return False, f"Maximum {MAX_NOTES_PER_UPLOAD} files allowed per upload"

        for file in files:
            # Check file extension
            if not file_handler.is_allowed_file(file.filename, ALLOWED_EXTENSIONS):
                return False, f"File {file.filename} has an invalid extension. Only PDF files are allowed."

            # Check file size
            # We need to read the file to check its size, but then reset the position
            file.file.seek(0, os.SEEK_END)
            file_size = file.file.tell()
            file.file.seek(0)  # Reset file position

            if file_size > MAX_FILE_SIZE:
                return False, f"File {file.filename} exceeds the maximum size of 30MB"

        return True, ""

    async def save_pdf_file(self, file: UploadFile, student_id: int) -> Dict:
        """
        Save an uploaded PDF file.

        Args:
            file: The uploaded file
            student_id: ID of the student

        Returns:
            Dictionary with file information
        """
        # Create a subdirectory for the student
        subdir = f"student_{student_id}"

        # Save the file
        file_path = await file_handler.save_upload(file, subdir)

        # Get file size
        file_size = os.path.getsize(file_path)

        return {
            "file_name": file.filename,
            "file_path": file_path,
            "file_size": file_size,
            "content_type": file.content_type or "application/pdf"
        }

    async def extract_text_from_pdf(self, file_path: str) -> str:
        """
        Extract text from a PDF file using Gemini's document understanding.

        Args:
            file_path: Path to the PDF file

        Returns:
            Extracted text
        """
        try:
            # First try using Gemini's document understanding
            try:
                # Use Gemini to extract text with better understanding of document structure
                prompt = "Extract all the text content from this document, preserving the structure and formatting as much as possible."
                text = await gemini_service.process_pdf_content(file_path, prompt)

                # If we got a reasonable amount of text, return it
                if text and len(text) > 100:
                    return text
            except RateLimitError as rate_error:
                logger.warning(f"Gemini rate limit exceeded, falling back to OCR: {str(rate_error)}")
                # Try OCR as fallback when rate limited
                try:
                    text, metadata = await ocr_service.extract_text_from_pdf_enhanced(file_path, fallback_to_gemini=False)
                    if text and len(text) > 100:
                        logger.info("Successfully extracted text using OCR fallback")
                        return text
                except Exception as ocr_error:
                    logger.warning(f"OCR fallback also failed: {str(ocr_error)}")
            except Exception as gemini_error:
                logger.warning(f"Error using Gemini for PDF extraction, falling back to PyMuPDF: {str(gemini_error)}")

            # Fallback to PyMuPDF if Gemini fails
            text = ""
            # Open the PDF file
            with fitz.open(file_path) as pdf:
                # Iterate through each page
                for page in pdf:
                    # Extract text from the page
                    text += page.get_text()

            return text
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {str(e)}")
            raise

    async def chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """
        Split text into chunks for embedding.

        Args:
            text: Text to chunk
            chunk_size: Maximum size of each chunk
            overlap: Overlap between chunks

        Returns:
            List of text chunks
        """
        chunks = []

        # If text is shorter than chunk_size, return it as a single chunk
        if len(text) <= chunk_size:
            return [text]

        # Split text into paragraphs
        paragraphs = text.split("\n\n")

        current_chunk = ""
        for paragraph in paragraphs:
            # If adding this paragraph would exceed chunk_size
            if len(current_chunk) + len(paragraph) > chunk_size:
                # Add current chunk to chunks list
                chunks.append(current_chunk)
                # Start a new chunk with overlap
                words = current_chunk.split()
                overlap_words = words[-min(len(words), overlap // 5):]  # Approximate words for overlap
                current_chunk = " ".join(overlap_words) + "\n\n" + paragraph
            else:
                # Add paragraph to current chunk
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph

        # Add the last chunk if it's not empty
        if current_chunk:
            chunks.append(current_chunk)

        return chunks

    async def create_embeddings(self, text_chunks: List[str], note_id: int) -> List[str]:
        """
        Create embeddings for text chunks and store them in the vector store.
        Note: We're using the existing vector store, not Gemini for embeddings.

        Args:
            text_chunks: List of text chunks
            note_id: ID of the note

        Returns:
            List of embedding IDs
        """
        embedding_ids = []

        for i, chunk in enumerate(text_chunks):
            # Create a UUID for this chunk
            document_id = str(uuid.uuid4())

            # Add metadata
            metadata = {
                "note_id": str(note_id),
                "chunk_index": i,
                "source_type": "student_note"
            }

            # Add to vector store using the existing implementation
            success = vector_store.add_document(document_id, chunk, metadata)

            if success:
                embedding_ids.append(document_id)

        return embedding_ids

    async def process_pdf_enhanced(
        self,
        file_path: str,
        document_id: str,
        question_count: int,
        course_name: Optional[str] = None,
        use_enhanced_pipeline: bool = True
    ) -> Dict:
        """
        Process PDF using enhanced pipeline with OCR, chunking, and vector indexing.

        Args:
            file_path: Path to PDF file
            document_id: Unique identifier for the document
            question_count: Number of questions to generate
            course_name: Course context
            use_enhanced_pipeline: Whether to use the enhanced pipeline

        Returns:
            Processing results
        """
        try:
            if use_enhanced_pipeline:
                # Use the enhanced PDF service
                return await enhanced_pdf_service.process_pdf_complete(
                    file_path=file_path,
                    document_id=document_id,
                    question_count=question_count,
                    course_name=course_name
                )
            else:
                # Use the original pipeline
                text = await self.extract_text_from_pdf(file_path)
                chunks = await self.chunk_text(text)
                embedding_ids = await self.create_embeddings(chunks, int(document_id))

                return {
                    'document_id': document_id,
                    'text': text,
                    'chunks': chunks,
                    'embedding_ids': embedding_ids,
                    'enhanced_pipeline_used': False
                }

        except Exception as e:
            logger.error(f"Error in enhanced PDF processing: {str(e)}")
            raise

    async def extract_text_with_ocr(self, file_path: str) -> Tuple[str, Dict]:
        """
        Extract text using enhanced OCR with confidence scoring.

        Args:
            file_path: Path to PDF file

        Returns:
            Tuple of (extracted_text, metadata)
        """
        try:
            return await ocr_service.extract_text_from_pdf_enhanced(file_path)
        except Exception as e:
            logger.error(f"Error in OCR text extraction: {str(e)}")
            # Fallback to original method
            text = await self.extract_text_from_pdf(file_path)
            return text, {'fallback_used': True, 'error': str(e)}

    async def chunk_text_smart(
        self,
        text: str,
        question_count: int,
        preserve_headings: bool = True
    ) -> List[Dict]:
        """
        Smart chunking with quota assignment.

        Args:
            text: Text to chunk
            question_count: Total questions to generate
            preserve_headings: Whether to preserve heading structure

        Returns:
            List of chunks with assigned quotas
        """
        try:
            return chunking_service.chunk_text_with_quota(
                text,
                question_count,
                preserve_headings=preserve_headings
            )
        except Exception as e:
            logger.error(f"Error in smart chunking: {str(e)}")
            # Fallback to original chunking
            chunks = await self.chunk_text(text)
            quota_per_chunk = max(1, question_count // len(chunks))
            return [
                {
                    'content': chunk,
                    'question_quota': quota_per_chunk,
                    'chunk_index': i,
                    'token_count': len(chunk.split()) * 1.3  # Rough estimation
                }
                for i, chunk in enumerate(chunks)
            ]

    async def search_document_content(
        self,
        document_id: str,
        query: str,
        limit: int = 5
    ) -> List[Dict]:
        """
        Search within document content using vector similarity.

        Args:
            document_id: Document identifier
            query: Search query
            limit: Maximum results

        Returns:
            Relevant content chunks
        """
        try:
            return await enhanced_vector_store.search_relevant_chunks(
                query=query,
                limit=limit,
                document_id=document_id
            )
        except Exception as e:
            logger.error(f"Error searching document content: {str(e)}")
            return []

    # Synchronous versions of async methods for Celery tasks

    def extract_text_from_pdf_sync(self, file_path: str) -> str:
        """
        Synchronous version of extract_text_from_pdf for Celery tasks.
        """
        try:
            # First try using Gemini's document understanding
            try:
                # Use Gemini to extract text with better understanding of document structure
                prompt = "Extract all the text content from this document, preserving the structure and formatting as much as possible."
                text = gemini_service.process_pdf_content_sync(file_path, prompt)

                # If we got a reasonable amount of text, return it
                if text and len(text) > 100:
                    return text
            except RateLimitError as rate_error:
                logger.warning(f"Gemini rate limit exceeded in sync mode, falling back to OCR: {str(rate_error)}")
                # Try OCR as fallback when rate limited
                try:
                    # Use synchronous OCR processing
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        text, metadata = loop.run_until_complete(
                            ocr_service.extract_text_from_pdf_enhanced(file_path, fallback_to_gemini=False)
                        )
                        if text and len(text) > 100:
                            logger.info("Successfully extracted text using OCR fallback in sync mode")
                            return text
                    finally:
                        loop.close()
                except Exception as ocr_error:
                    logger.warning(f"OCR fallback also failed in sync mode: {str(ocr_error)}")
            except Exception as gemini_error:
                logger.warning(f"Error using Gemini for PDF extraction in sync mode, falling back to PyMuPDF: {str(gemini_error)}")

            # Fallback to PyMuPDF if Gemini fails
            text = ""
            # Open the PDF file
            with fitz.open(file_path) as pdf:
                # Iterate through each page
                for page in pdf:
                    # Extract text from the page
                    text += page.get_text()

            return text
        except Exception as e:
            logger.error(f"Error extracting text from PDF in sync mode: {str(e)}")
            raise

    def chunk_text_sync(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """
        Synchronous version of chunk_text for Celery tasks.
        """
        return asyncio.run(self.chunk_text(text, chunk_size, overlap))

    def create_embeddings_sync(self, text_chunks: List[str], note_id: int) -> List[str]:
        """
        Synchronous version of create_embeddings for Celery tasks.
        """
        return asyncio.run(self.create_embeddings(text_chunks, note_id))


# Create a singleton instance
pdf_processing_service = PDFProcessingService()
