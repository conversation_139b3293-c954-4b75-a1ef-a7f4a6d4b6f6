import logging
import asyncio
import os
import tempfile
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone
from pathlib import Path

from sqlalchemy.orm import Session
# from diskcache import Cache

from app.services.ocr_service import ocr_service
from app.services.chunking_service import chunking_service
from app.services.enhanced_vector_store import enhanced_vector_store
from app.services.prompting_service import prompting_service
from app.core.config import settings

logger = logging.getLogger(__name__)

# Initialize cache for processing results (simplified)
# cache = Cache(directory=os.path.join(tempfile.gettempdir(), "campuspq_pdf_cache"))
cache = {}


class EnhancedPDFService:
    """Enhanced PDF processing service with OCR, chunking, and vector indexing."""
    
    def __init__(self):
        self.processing_cache = cache
        
    async def process_pdf_complete(
        self,
        file_path: str,
        document_id: str,
        question_count: int,
        course_name: Optional[str] = None,
        use_handwriting_ocr: bool = True,
        preserve_headings: bool = True
    ) -> Dict[str, Any]:
        """
        Complete PDF processing pipeline with all enhancements.
        
        Args:
            file_path: Path to PDF file
            document_id: Unique identifier for the document
            question_count: Total number of questions to generate
            course_name: Course context
            use_handwriting_ocr: Whether to use handwriting-capable OCR
            preserve_headings: Whether to preserve heading structure in chunking
            
        Returns:
            Complete processing results with questions and metadata
        """
        try:
            # Check cache first (simplified)
            cache_key = f"enhanced_pdf_{document_id}_{question_count}_{os.path.getmtime(file_path)}"
            cached_result = self.processing_cache.get(cache_key)
            if cached_result:
                logger.info(f"Using cached result for document {document_id}")
                return cached_result
            
            processing_start = datetime.now(timezone.utc)
            
            # Step 1: Enhanced OCR with confidence scoring
            logger.info(f"Starting OCR processing for {document_id}")
            ocr_result = await ocr_service.extract_text_from_pdf_enhanced(
                file_path, 
                fallback_to_gemini=True
            )
            
            extracted_text = ocr_result[0]
            ocr_metadata = ocr_result[1]
            
            if not extracted_text or len(extracted_text.strip()) < 100:
                raise Exception("Insufficient text extracted from PDF")
            
            logger.info(f"OCR completed. Confidence: {ocr_metadata.get('overall_confidence', 0):.2f}")
            
            # Step 2: Smart chunking with quota assignment
            logger.info(f"Starting chunking for {document_id}")
            chunks = chunking_service.chunk_text_with_quota(
                extracted_text,
                question_count,
                preserve_headings=preserve_headings
            )
            
            logger.info(f"Created {len(chunks)} chunks with assigned quotas")
            
            # Step 3: Vector indexing
            logger.info(f"Starting vector indexing for {document_id}")
            document_metadata = {
                'course_name': course_name,
                'file_path': file_path,
                'processing_timestamp': processing_start.isoformat(),
                'ocr_confidence': ocr_metadata.get('overall_confidence', 0),
                'total_chunks': len(chunks),
                'total_questions': question_count
            }
            
            chunk_ids = await enhanced_vector_store.add_document_chunks(
                document_id,
                chunks,
                document_metadata
            )
            
            logger.info(f"Indexed {len(chunk_ids)} chunks in vector store")
            
            # Step 4: Parallel question generation
            logger.info(f"Starting question generation for {document_id}")
            question_results = await prompting_service.generate_questions_batch(
                chunks,
                course_name
            )
            
            # Step 5: Aggregate and validate results
            all_mcqs = []
            all_flashcards = []
            generation_metadata = []
            
            for i, result in enumerate(question_results):
                chunk_mcqs = result.get('mcqs', [])
                chunk_flashcards = result.get('flashcards', [])
                
                # Add chunk context to questions
                for mcq in chunk_mcqs:
                    mcq['chunk_index'] = i
                    mcq['chunk_id'] = chunk_ids[i] if i < len(chunk_ids) else None
                
                for flashcard in chunk_flashcards:
                    flashcard['chunk_index'] = i
                    flashcard['chunk_id'] = chunk_ids[i] if i < len(chunk_ids) else None
                
                all_mcqs.extend(chunk_mcqs)
                all_flashcards.extend(chunk_flashcards)
                generation_metadata.append(result.get('metadata', {}))
            
            # Step 6: Quality control and quota enforcement
            final_mcqs = await self._enforce_quota_and_quality(
                all_mcqs, 
                question_count, 
                chunks
            )
            
            # Calculate processing statistics
            processing_end = datetime.now(timezone.utc)
            processing_time = (processing_end - processing_start).total_seconds()
            
            # Compile final result
            result = {
                'document_id': document_id,
                'mcqs': final_mcqs,
                'flashcards': all_flashcards,
                'chunks': chunks,
                'chunk_ids': chunk_ids,
                'processing_metadata': {
                    'processing_time_seconds': processing_time,
                    'ocr_metadata': ocr_metadata,
                    'chunking_stats': {
                        'total_chunks': len(chunks),
                        'avg_tokens_per_chunk': sum(c['token_count'] for c in chunks) / len(chunks),
                        'total_tokens': sum(c['token_count'] for c in chunks)
                    },
                    'generation_stats': {
                        'total_mcqs_generated': len(all_mcqs),
                        'total_flashcards_generated': len(all_flashcards),
                        'final_mcqs_count': len(final_mcqs),
                        'quota_enforcement_applied': len(final_mcqs) != len(all_mcqs)
                    },
                    'generation_metadata': generation_metadata
                }
            }
            
            # Cache the result (simplified)
            self.processing_cache[cache_key] = result
            
            logger.info(f"PDF processing completed for {document_id} in {processing_time:.2f} seconds")
            return result
            
        except Exception as e:
            logger.error(f"Error in enhanced PDF processing: {str(e)}")
            raise
    
    async def _enforce_quota_and_quality(
        self,
        mcqs: List[Dict[str, Any]],
        target_count: int,
        chunks: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Enforce question quota and apply quality filtering.
        
        Args:
            mcqs: Generated MCQs
            target_count: Target number of questions
            chunks: Original chunks with quotas
            
        Returns:
            Filtered and quota-enforced MCQs
        """
        try:
            # If we have exactly the right number, return as-is
            if len(mcqs) == target_count:
                return mcqs
            
            # If we have too many, select the best ones
            if len(mcqs) > target_count:
                # Score questions based on quality indicators
                scored_mcqs = []
                for mcq in mcqs:
                    score = self._calculate_question_quality_score(mcq)
                    scored_mcqs.append((mcq, score))
                
                # Sort by score and take top questions
                scored_mcqs.sort(key=lambda x: x[1], reverse=True)
                return [mcq for mcq, _ in scored_mcqs[:target_count]]
            
            # If we have too few, try to generate more from high-quota chunks
            if len(mcqs) < target_count:
                deficit = target_count - len(mcqs)
                logger.warning(f"Question deficit of {deficit}, attempting to generate more")
                
                # Find chunks with high quotas that might have failed
                high_quota_chunks = [c for c in chunks if c.get('question_quota', 0) > 2]
                
                if high_quota_chunks and deficit <= 5:  # Only try for small deficits
                    # Try to generate additional questions from the best chunks
                    additional_mcqs = await self._generate_additional_questions(
                        high_quota_chunks[:2],  # Use top 2 chunks
                        deficit
                    )
                    mcqs.extend(additional_mcqs)
                
                return mcqs[:target_count]  # Ensure we don't exceed target
            
            return mcqs
            
        except Exception as e:
            logger.error(f"Error enforcing quota and quality: {str(e)}")
            return mcqs[:target_count]  # Fallback to simple truncation
    
    def _calculate_question_quality_score(self, mcq: Dict[str, Any]) -> float:
        """Calculate a quality score for an MCQ question."""
        score = 0.0
        
        # Check question length (not too short, not too long)
        question_length = len(mcq.get('question', ''))
        if 20 <= question_length <= 200:
            score += 1.0
        
        # Check if all options are provided and reasonable length
        options = mcq.get('options', [])
        if len(options) == 4:
            score += 1.0
            avg_option_length = sum(len(opt) for opt in options) / 4
            if 5 <= avg_option_length <= 50:
                score += 0.5
        
        # Check if explanation exists and is reasonable
        explanation = mcq.get('explanation', '')
        if explanation and 10 <= len(explanation) <= 300:
            score += 1.0
        
        # Check if topic is provided
        if mcq.get('topic'):
            score += 0.5
        
        # Check if difficulty is set
        if mcq.get('difficulty') in ['easy', 'medium', 'hard']:
            score += 0.5
        
        return score
    
    async def _generate_additional_questions(
        self,
        chunks: List[Dict[str, Any]],
        count: int
    ) -> List[Dict[str, Any]]:
        """Generate additional questions from specific chunks."""
        try:
            additional_mcqs = []
            questions_per_chunk = max(1, count // len(chunks))
            
            for chunk in chunks:
                if len(additional_mcqs) >= count:
                    break
                
                result = await prompting_service.generate_questions_from_chunk(
                    chunk['content'],
                    questions_per_chunk,
                    0,  # No flashcards for additional generation
                    use_summary=True
                )
                
                chunk_mcqs = result.get('mcqs', [])
                additional_mcqs.extend(chunk_mcqs[:questions_per_chunk])
            
            return additional_mcqs[:count]
            
        except Exception as e:
            logger.error(f"Error generating additional questions: {str(e)}")
            return []
    
    async def search_document_content(
        self,
        document_id: str,
        query: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Search within a specific document's content.
        
        Args:
            document_id: Document identifier
            query: Search query
            limit: Maximum results
            
        Returns:
            Relevant chunks from the document
        """
        try:
            return await enhanced_vector_store.search_relevant_chunks(
                query=query,
                limit=limit,
                document_id=document_id
            )
        except Exception as e:
            logger.error(f"Error searching document content: {str(e)}")
            return []


# Create singleton instance
enhanced_pdf_service = EnhancedPDFService()
