import logging
import async<PERSON>
import j<PERSON>
from typing import Dict, List, Optional, <PERSON>ple, Any

from sqlalchemy.orm import Session

from app.models.student_tools import ProcessingStatus
import app.services.course_service as course_service
from app.schemas.course import CourseCreate
from app.services.ai_service import ai_service
from app.services.pdf_processing_service import pdf_processing_service
from app.crud.crud_student_tools import flash_card, note_generated_flash_card
from app.schemas.student_tools import FlashCardCreate
from app.services.gemini_service import gemini_service, RateLimitError
from app.services.langchain_multiagent_service import (
    langchain_multiagent_service,
    MultiAgentConfig
)

logger = logging.getLogger(__name__)


class FlashCardGenerationService:
    """Service for generating flash cards from student notes."""

    async def detect_course(
        self,
        note_content: str,
        db: Session
    ) -> Tuple[Optional[int], List[str]]:
        """
        Detect the course and topics from note content.

        Args:
            note_content: Content of the note
            db: Database session

        Returns:
            Tuple of (course_id, topics)
        """
        try:
            # Use Gemini to detect the course and topics
            # Extract a sample of the content for course detection
            sample_content = note_content[:5000]  # Use first 5000 chars for detection

            # Prepare the prompt for course detection
            prompt = f"""
            Analyze the following text from a student's notes and determine:
            1. The most likely course/subject this content belongs to
            2. A list of 3-5 key topics covered in the content

            Text from student notes:
            {sample_content}
            """

            # Call Gemini API
            response = await gemini_service.chat_completion(
                messages=[
                    {"role": "system", "content": "You are a helpful academic assistant that specializes in identifying academic subjects and topics from student notes."},
                    {"role": "user", "content": prompt}
                ],
                model="gemini-2.0-flash"
            )

            # Parse the response
            content = response["choices"][0]["message"]["content"]

            # Extract course name and topics
            course_name = None
            topics = []

            # Simple parsing logic - can be improved
            lines = content.split("\n")
            for line in lines:
                line = line.strip()
                if "course" in line.lower() or "subject" in line.lower():
                    parts = line.split(":")
                    if len(parts) > 1:
                        course_name = parts[1].strip()
                elif "topic" in line.lower() or "key topics" in line.lower():
                    parts = line.split(":")
                    if len(parts) > 1:
                        topics_text = parts[1].strip()
                        # Split topics by commas or bullet points
                        if "," in topics_text:
                            topics = [t.strip() for t in topics_text.split(",")]
                        elif "-" in topics_text:
                            topics = [t.strip() for t in topics_text.split("-") if t.strip()]
                        else:
                            topics = [topics_text]
                elif line.startswith("-") or line.startswith("*"):
                    # This might be a topic in a bullet list
                    topic = line.lstrip("-* ").strip()
                    if topic and len(topic) > 3:  # Avoid very short topics
                        topics.append(topic)

            # If we couldn't extract topics from the structure, use a fallback
            if not topics:
                topics = ["General"]

            # Try to match the course name to a course in the database
            course_id = None
            if course_name:
                # Get all courses
                courses = course_service.get_multi(db)

                # Try to find a match
                for course in courses:
                    if course_name.lower() in course.name.lower() or course.name.lower() in course_name.lower():
                        course_id = course.id
                        break

            # Verify course_id exists
            if course_id is not None:
                try:
                    course = course_service.get(db, id=course_id)
                    if not course:
                        course_id = None
                except Exception as e:
                    logger.error(f"Error verifying course: {str(e)}")
                    course_id = None

            return course_id, topics

        except Exception as e:
            logger.error(f"Error detecting course: {str(e)}")
            return None, []

    async def generate_flash_cards(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        card_count: int,
        db: Session
    ) -> List[Dict]:
        """
        Generate flash cards from note contents.

        Args:
            note_contents: List of note contents
            course_name: Name of the course (optional)
            card_count: Number of cards to generate
            db: Database session

        Returns:
            List of generated flash cards
        """
        try:
            # Combine note contents
            combined_content = "\n\n".join(note_contents)

            # Truncate if too long
            max_length = 15000  # Adjust based on token limits
            if len(combined_content) > max_length:
                combined_content = combined_content[:max_length]
                logger.warning(f"Note content truncated from {len(combined_content)} to {max_length} characters")

            # Use provided course name or default
            if not course_name:
                course_name = "Unknown"

            # Prepare the prompt for flash card generation
            from app.utils.vector_store import vector_store

            # Use the vector store to get relevant content if available
            relevant_content = combined_content
            try:
                # Search for relevant content in the vector store
                search_results = vector_store.search(
                    query=f"Important concepts and facts about {course_name}",
                    limit=10,
                    filter_condition=None  # No longer filtering by course_id
                )

                if search_results:
                    # Extract text from search results
                    relevant_texts = [result["text"] for result in search_results]
                    relevant_content = "\n\n".join(relevant_texts)
                    logger.info(f"Using {len(search_results)} relevant documents from vector store")
                else:
                    logger.info("No relevant documents found in vector store, using original content")
            except Exception as e:
                logger.warning(f"Error searching vector store: {str(e)}. Using original content.")

            # Create example flash cards for the model to follow
            import json
            example_cards = [
                {
                    "front_content": "What is the law of conservation of energy?",
                    "back_content": "The law of conservation of energy states that energy cannot be created or destroyed, only transformed from one form to another.",
                    "topic": "Physics"
                },
                {
                    "front_content": "Define photosynthesis",
                    "back_content": "Photosynthesis is the process by which green plants and some other organisms use sunlight to synthesize foods with carbon dioxide and water, generating oxygen as a byproduct.",
                    "topic": "Biology"
                }
            ]

            # Create a system prompt with document understanding emphasis
            system_prompt = """
            You are a helpful academic assistant that creates high-quality flash cards from student notes using document understanding.

            DOCUMENT UNDERSTANDING INSTRUCTIONS:
            1. Carefully analyze the structure and content of the student notes
            2. Identify key concepts, facts, definitions, formulas, and relationships in the document
            3. Create flash cards that cover the most important information from different sections
            4. Ensure the front side poses a clear question or presents a concept
            5. Ensure the back side provides a comprehensive and accurate answer or explanation
            6. Organize flash cards by topics found in the document

            Each flash card should have a front side with a question or concept, and a back side with the answer or explanation.
            """

            # Create a user prompt
            user_prompt = f"""
            Create EXACTLY {card_count} flash cards based on the student notes provided below.
            The course is: {course_name}

            Each flash card must have:
            1. A front_content field with a clear question or concept
            2. A back_content field with a comprehensive answer or explanation
            3. A topic field indicating the specific topic from the notes

            Format your response as a JSON object with a 'flash_cards' array containing EXACTLY {card_count} flash card objects.

            Here is an example of the exact JSON structure to return:
            {{
                "flash_cards": {json.dumps(example_cards, indent=2)}
            }}

            Here are the student notes:
            {relevant_content}
            """

            # Call Gemini API with enhanced retry logic
            max_retries = 5  # Increased retries
            retry_count = 0
            flash_cards = []

            while retry_count < max_retries:
                try:
                    # Use Gemini for flash card generation with document understanding
                    # Call Gemini API with JSON response format
                    response = await gemini_service.chat_completion(
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_prompt}
                        ],
                        model="gemini-2.0-flash",
                        temperature=0.3,  # Lower temperature for more consistent formatting
                        max_tokens=4000,  # Increased token limit
                        response_format={"type": "json_object"}
                    )

                    # Parse the response
                    content = response["choices"][0]["message"]["content"]
                    logger.info(f"Received response from Gemini: {content[:100]}...")

                    # Parse JSON response
                    try:
                        response_data = json.loads(content)

                        # Extract flash cards from the response
                        if "flash_cards" in response_data and isinstance(response_data["flash_cards"], list):
                            flash_cards = response_data["flash_cards"]
                        else:
                            # Try to use the entire response as the flash cards array
                            flash_cards = response_data

                        # Validate flash cards
                        valid_cards = []
                        for card in flash_cards:
                            if isinstance(card, dict) and "front_content" in card and "back_content" in card:
                                # Ensure topic exists
                                if "topic" not in card:
                                    card["topic"] = "General"
                                valid_cards.append(card)

                        if valid_cards:
                            logger.info(f"Successfully generated {len(valid_cards)} valid flash cards")
                            return valid_cards
                        else:
                            logger.error("No valid flash cards found in response")
                            retry_count += 1
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse JSON from response: {e}")
                        logger.error(f"Response content: {content[:500]}...")
                        retry_count += 1

                except RateLimitError as rate_error:
                    logger.warning(f"Rate limit hit during flash card generation: {str(rate_error)}")
                    # Use exponential backoff for rate limit errors
                    wait_time = min(60, 2 ** retry_count)  # Cap at 60 seconds
                    logger.info(f"Waiting {wait_time} seconds before retry due to rate limit")
                    await asyncio.sleep(wait_time)
                    retry_count += 1

                except Exception as e:
                    logger.error(f"Error generating flash cards (attempt {retry_count + 1}): {str(e)}")
                    retry_count += 1

                # Wait before retrying (shorter for non-rate-limit errors)
                if retry_count < max_retries:
                    await asyncio.sleep(3)  # Slightly longer wait

            # If we get here, all retries failed
            raise ValueError("Failed to generate valid flash cards after multiple attempts")

        except Exception as e:
            logger.error(f"Error generating flash cards: {str(e)}")
            raise



    async def create_flash_card_objects(
        self,
        flash_cards: List[Dict],
        course_name: Optional[str],
        student_id: int,
        db: Session,
        job_id: Optional[int] = None
    ) -> List[int]:
        """
        Create flash card objects in the database.

        Args:
            flash_cards: List of flash card data
            course_name: Name of the course
            student_id: ID of the student
            db: Database session

        Returns:
            List of created flash card IDs
        """
        card_ids = []

        for card_data in flash_cards:
            try:
                # Extract data
                front_content = card_data.get("front_content", "")
                back_content = card_data.get("back_content", "")
                topic = card_data.get("topic", "General")

                # Create flash card object
                card_data = FlashCardCreate(
                    student_id=student_id,
                    front_content=front_content,
                    back_content=back_content,
                    topic=topic,
                    course_name=course_name,
                    job_id=job_id  # Set the job_id to link the card to the job
                )

                # Create flash card in database
                card = flash_card.create(db, obj_in=card_data)
                card_ids.append(card.id)
            except Exception as e:
                logger.error(f"Error creating flash card in database: {str(e)}")
                # Skip this card on error

        # If no cards were created, log a warning
        if not card_ids:
            logger.warning("No flash cards were created")

        return card_ids

    async def generate_flash_cards_multiagent(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        card_count: int,
        db: Session,
        use_multiagent: bool = True
    ) -> List[Dict]:
        """
        Generate flashcards using LangChain multiagent system.

        This method uses the multiagent system for ALL card counts to ensure
        high quality generation without fallbacks.

        Args:
            note_contents: List of note contents
            course_name: Name of the course (optional)
            card_count: Number of cards to generate
            db: Database session
            use_multiagent: Whether to use multiagent approach

        Returns:
            List of generated flashcards
        """
        logger.info(f"Starting multiagent flashcard generation for {card_count} cards")

        # Always use multiagent approach - no fallbacks
        # Create multiagent configuration
        config = MultiAgentConfig(
            total_questions=card_count,
            max_questions_per_agent=10,  # Smaller batches for reliability
            min_questions_per_agent=3,
            max_agents=8,
            timeout_per_agent=90,  # Increased timeout
            retry_attempts=3  # More retries
        )

        logger.info(f"Using multiagent approach with config: {config}")

        # Use the LangChain multiagent service
        flashcards = await langchain_multiagent_service.generate_flashcards_multiagent(
            note_contents=note_contents,
            course_name=course_name,
            card_count=card_count,
            config=config
        )

        if not flashcards:
            raise ValueError(f"Multiagent system failed to generate any flashcards for {card_count} requested cards")

        logger.info(f"Multiagent system successfully generated {len(flashcards)} flashcards")
        return flashcards

    def generate_flash_cards_multiagent_sync(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        card_count: int,
        db: Session,
        use_multiagent: bool = True
    ) -> List[Dict]:
        """
        Synchronous version of generate_flash_cards_multiagent for Celery tasks.
        """
        return asyncio.run(self.generate_flash_cards_multiagent(
            note_contents, course_name, card_count, db, use_multiagent
        ))

    # Synchronous versions of async methods for Celery tasks
    def detect_course_sync(self, note_content: str, db: Session) -> Tuple[Optional[int], List[str]]:
        """
        Synchronous version of detect_course for Celery tasks.
        """
        return asyncio.run(self.detect_course(note_content, db))

    def generate_flash_cards_sync(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        card_count: int,
        db: Session
    ) -> List[Dict]:
        """
        Synchronous version of generate_flash_cards for Celery tasks.
        """
        try:
            # Combine note contents
            combined_content = "\n\n".join(note_contents)

            # Truncate if too long
            max_length = 15000  # Adjust based on token limits
            if len(combined_content) > max_length:
                combined_content = combined_content[:max_length]
                logger.warning(f"Note content truncated from {len(combined_content)} to {max_length} characters")

            # Use provided course name or default
            if not course_name:
                course_name = "Unknown"

            # Prepare the prompt for flash card generation
            from app.utils.vector_store import vector_store

            # Use the vector store to get relevant content if available
            relevant_content = combined_content
            try:
                # Search for relevant content in the vector store
                search_results = vector_store.search(
                    query=f"Important concepts and facts about {course_name}",
                    limit=10,
                    filter_condition=None  # No longer filtering by course_id
                )

                if search_results:
                    # Extract text from search results
                    relevant_texts = [result["text"] for result in search_results]
                    relevant_content = "\n\n".join(relevant_texts)
                    logger.info(f"Using {len(search_results)} relevant documents from vector store")
                else:
                    logger.info("No relevant documents found in vector store, using original content")
            except Exception as e:
                logger.warning(f"Error searching vector store: {str(e)}. Using original content.")

            # Create example flash cards for the model to follow
            import json
            example_cards = [
                {
                    "front_content": "What is the law of conservation of energy?",
                    "back_content": "The law of conservation of energy states that energy cannot be created or destroyed, only transformed from one form to another.",
                    "topic": "Physics"
                },
                {
                    "front_content": "Define photosynthesis",
                    "back_content": "Photosynthesis is the process by which green plants and some other organisms use sunlight to synthesize foods with carbon dioxide and water, generating oxygen as a byproduct.",
                    "topic": "Biology"
                }
            ]

            # Create a system prompt with document understanding emphasis
            system_prompt = """
            You are a helpful academic assistant that creates high-quality flash cards from student notes using document understanding.

            DOCUMENT UNDERSTANDING INSTRUCTIONS:
            1. Carefully analyze the structure and content of the student notes
            2. Identify key concepts, facts, definitions, formulas, and relationships in the document
            3. Create flash cards that cover the most important information from different sections
            4. Ensure the front side poses a clear question or presents a concept
            5. Ensure the back side provides a comprehensive and accurate answer or explanation
            6. Organize flash cards by topics found in the document

            Each flash card should have a front side with a question or concept, and a back side with the answer or explanation.
            """

            # Create a user prompt
            user_prompt = f"""
            Create EXACTLY {card_count} flash cards based on the student notes provided below.
            The course is: {course_name}

            Each flash card must have:
            1. A front_content field with a clear question or concept
            2. A back_content field with a comprehensive answer or explanation
            3. A topic field indicating the specific topic from the notes

            Format your response as a JSON object with a 'flash_cards' array containing EXACTLY {card_count} flash card objects.

            Here is an example of the exact JSON structure to return:
            {{
                "flash_cards": {json.dumps(example_cards, indent=2)}
            }}

            Here are the student notes:
            {relevant_content}
            """

            # Call Gemini API with enhanced retry logic
            max_retries = 5  # Increased retries
            retry_count = 0
            flash_cards = []

            while retry_count < max_retries:
                try:
                    # Use Gemini for flash card generation with document understanding
                    # Call Gemini API with JSON response format in sync mode
                    import asyncio

                    # Create a new event loop for this task
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        response = loop.run_until_complete(gemini_service.chat_completion(
                            messages=[
                                {"role": "system", "content": system_prompt},
                                {"role": "user", "content": user_prompt}
                            ],
                            model="gemini-2.0-flash",
                            temperature=0.3,  # Lower temperature for more consistent formatting
                            max_tokens=4000,  # Increased token limit
                            response_format={"type": "json_object"}
                        ))
                    finally:
                        loop.close()

                    # Parse the response
                    content = response["choices"][0]["message"]["content"]
                    logger.info(f"Received response from Gemini: {content[:100]}...")

                    # Parse JSON response
                    try:
                        response_data = json.loads(content)

                        # Extract flash cards from the response
                        if "flash_cards" in response_data and isinstance(response_data["flash_cards"], list):
                            flash_cards = response_data["flash_cards"]
                        else:
                            # Try to use the entire response as the flash cards array
                            flash_cards = response_data

                        # Validate flash cards
                        valid_cards = []
                        for card in flash_cards:
                            if isinstance(card, dict) and "front_content" in card and "back_content" in card:
                                # Ensure topic exists
                                if "topic" not in card:
                                    card["topic"] = "General"
                                valid_cards.append(card)

                        if valid_cards:
                            logger.info(f"Successfully generated {len(valid_cards)} valid flash cards in sync mode")
                            return valid_cards
                        else:
                            logger.error("No valid flash cards found in response")
                            retry_count += 1
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse JSON from response in sync mode: {e}")
                        logger.error(f"Response content: {content[:500]}...")
                        retry_count += 1

                except RateLimitError as rate_error:
                    logger.warning(f"Rate limit hit during sync flash card generation: {str(rate_error)}")
                    # Use exponential backoff for rate limit errors
                    wait_time = min(60, 2 ** retry_count)  # Cap at 60 seconds
                    logger.info(f"Waiting {wait_time} seconds before retry due to rate limit")
                    import time
                    time.sleep(wait_time)
                    retry_count += 1

                except Exception as e:
                    logger.error(f"Error generating flash cards in sync mode (attempt {retry_count + 1}): {str(e)}")
                    retry_count += 1

                # Wait before retrying (shorter for non-rate-limit errors)
                import time
                if retry_count < max_retries:
                    time.sleep(3)  # Slightly longer wait

            # If we get here, all retries failed
            raise ValueError("Failed to generate valid flash cards after multiple attempts")

        except Exception as e:
            logger.error(f"Error generating flash cards in sync mode: {str(e)}")
            raise



    def create_flash_card_objects_sync(
        self,
        flash_cards: List[Dict],
        course_name: Optional[str],
        student_id: int,
        db: Session,
        job_id: Optional[int] = None
    ) -> List[int]:
        """
        Synchronous version of create_flash_card_objects for Celery tasks.
        """
        # Don't use asyncio.run() in a Celery task as it can cause issues with the event loop
        card_ids = []

        for card_data in flash_cards:
            try:
                # Extract data
                front_content = card_data.get("front_content", "")
                back_content = card_data.get("back_content", "")
                topic = card_data.get("topic", "General")

                # Create flash card object
                card_data = FlashCardCreate(
                    student_id=student_id,
                    front_content=front_content,
                    back_content=back_content,
                    topic=topic,
                    course_name=course_name,
                    job_id=job_id  # Set the job_id to link the card to the job
                )

                # Create flash card in database
                card = flash_card.create(db, obj_in=card_data)
                card_ids.append(card.id)
            except Exception as e:
                logger.error(f"Error creating flash card in database: {str(e)}")
                # Skip this card on error

        # If no cards were created, log a warning
        if not card_ids:
            logger.warning("No flash cards were created")

        return card_ids


# Create a singleton instance
flash_card_generation_service = FlashCardGenerationService()
