import logging
import asyncio
import tempfile
import os
from typing import Dict, List, Optional, Any
from pathlib import Path

from langchain.document_loaders import PyMuPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import Qdrant
from langchain.chains import MapReduceDocumentsChain, ReduceDocumentsChain
from langchain.chains.llm import LLMChain
from langchain.prompts import PromptTemplate
from langchain.chains.combine_documents.stuff import StuffDocumentsChain
from langchain.llms import OpenAI
from langchain.schema import Document
from qdrant_client import QdrantClient

from app.core.config import settings
from app.services.enhanced_vector_store import enhanced_vector_store

logger = logging.getLogger(__name__)


class LangChainService:
    """LangChain integration service for document processing orchestration."""
    
    def __init__(self):
        self.embeddings = OpenAIEmbeddings(
            openai_api_key=settings.OPENAI_API_KEY,
            model="text-embedding-3-small"
        )
        self.llm = OpenAI(
            openai_api_key=settings.OPENAI_API_KEY,
            temperature=0.3
        )
        self.qdrant_client = QdrantClient(url=settings.QDRANT_URL)
        
    async def load_and_split_pdf(
        self,
        file_path: str,
        chunk_size: int = 1200,
        chunk_overlap: int = 200
    ) -> List[Document]:
        """
        Load PDF and split into documents using LangChain.
        
        Args:
            file_path: Path to PDF file
            chunk_size: Target chunk size in characters
            chunk_overlap: Overlap between chunks
            
        Returns:
            List of LangChain Document objects
        """
        try:
            # Load PDF
            loader = PyMuPDFLoader(file_path)
            documents = await asyncio.to_thread(loader.load)
            
            # Split documents
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=chunk_size * 4,  # Approximate character count
                chunk_overlap=chunk_overlap * 4,
                length_function=len,
                separators=["\n\n", "\n", ". ", "! ", "? ", " ", ""]
            )
            
            split_docs = await asyncio.to_thread(text_splitter.split_documents, documents)
            
            # Add metadata
            for i, doc in enumerate(split_docs):
                doc.metadata.update({
                    'chunk_index': i,
                    'source_file': file_path,
                    'total_chunks': len(split_docs)
                })
            
            return split_docs
            
        except Exception as e:
            logger.error(f"Error loading and splitting PDF: {str(e)}")
            raise
    
    async def create_vector_store(
        self,
        documents: List[Document],
        collection_name: str
    ) -> Qdrant:
        """
        Create a Qdrant vector store from documents.
        
        Args:
            documents: List of documents to index
            collection_name: Name of the collection
            
        Returns:
            Qdrant vector store instance
        """
        try:
            vector_store = await asyncio.to_thread(
                Qdrant.from_documents,
                documents,
                self.embeddings,
                url=settings.QDRANT_URL,
                collection_name=collection_name,
                force_recreate=True
            )
            
            return vector_store
            
        except Exception as e:
            logger.error(f"Error creating vector store: {str(e)}")
            raise
    
    async def summarize_documents_map_reduce(
        self,
        documents: List[Document],
        max_tokens: int = 250
    ) -> str:
        """
        Summarize documents using map-reduce approach.
        
        Args:
            documents: Documents to summarize
            max_tokens: Maximum tokens for final summary
            
        Returns:
            Summarized content
        """
        try:
            # Map prompt - summarize individual documents
            map_template = """The following is a section of educational content:
{docs}

Based on this content, write a concise summary that captures the key concepts, facts, and relationships. Focus on information that would be useful for generating educational questions.

Summary:"""
            
            map_prompt = PromptTemplate.from_template(map_template)
            map_chain = LLMChain(llm=self.llm, prompt=map_prompt)
            
            # Reduce prompt - combine summaries
            reduce_template = """The following are summaries of different sections of educational content:
{doc_summaries}

Combine these summaries into a single, coherent summary that captures all the key educational concepts and information. Keep it under {max_tokens} tokens.

Combined Summary:"""
            
            reduce_prompt = PromptTemplate.from_template(reduce_template)
            reduce_chain = LLMChain(llm=self.llm, prompt=reduce_prompt)
            
            # Combine documents chain
            combine_documents_chain = StuffDocumentsChain(
                llm_chain=reduce_chain,
                document_variable_name="doc_summaries"
            )
            
            # Reduce documents chain
            reduce_documents_chain = ReduceDocumentsChain(
                combine_documents_chain=combine_documents_chain,
                collapse_documents_chain=combine_documents_chain,
                token_max=max_tokens * 10  # Allow more tokens for intermediate processing
            )
            
            # Map-reduce chain
            map_reduce_chain = MapReduceDocumentsChain(
                llm_chain=map_chain,
                reduce_documents_chain=reduce_documents_chain,
                document_variable_name="docs",
                return_intermediate_steps=False
            )
            
            # Run the chain
            result = await asyncio.to_thread(
                map_reduce_chain.run,
                input_documents=documents,
                max_tokens=max_tokens
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in map-reduce summarization: {str(e)}")
            # Fallback to simple concatenation and truncation
            combined_text = "\n\n".join([doc.page_content for doc in documents])
            return combined_text[:max_tokens * 4]  # Rough token estimation
    
    async def extract_topics_and_concepts(
        self,
        documents: List[Document]
    ) -> Dict[str, Any]:
        """
        Extract topics and key concepts from documents.
        
        Args:
            documents: Documents to analyze
            
        Returns:
            Dictionary with topics and concepts
        """
        try:
            # Create prompt for topic extraction
            topic_template = """Analyze the following educational content and extract:
1. Main topics and subjects covered
2. Key concepts and terms
3. Learning objectives that could be derived
4. Difficulty level assessment

Content:
{docs}

Provide your analysis in the following format:
Topics: [list of main topics]
Key Concepts: [list of key concepts]
Learning Objectives: [list of potential learning objectives]
Difficulty Level: [beginner/intermediate/advanced]
"""
            
            topic_prompt = PromptTemplate.from_template(topic_template)
            topic_chain = LLMChain(llm=self.llm, prompt=topic_prompt)
            
            # Combine all document content
            combined_content = "\n\n".join([doc.page_content for doc in documents[:5]])  # Limit to first 5 docs
            
            result = await asyncio.to_thread(
                topic_chain.run,
                docs=combined_content
            )
            
            # Parse the result (simple parsing, could be enhanced)
            lines = result.split('\n')
            analysis = {
                'topics': [],
                'key_concepts': [],
                'learning_objectives': [],
                'difficulty_level': 'intermediate'
            }
            
            current_section = None
            for line in lines:
                line = line.strip()
                if line.startswith('Topics:'):
                    current_section = 'topics'
                    content = line.replace('Topics:', '').strip()
                    if content:
                        analysis['topics'] = [t.strip() for t in content.split(',')]
                elif line.startswith('Key Concepts:'):
                    current_section = 'key_concepts'
                    content = line.replace('Key Concepts:', '').strip()
                    if content:
                        analysis['key_concepts'] = [c.strip() for c in content.split(',')]
                elif line.startswith('Learning Objectives:'):
                    current_section = 'learning_objectives'
                    content = line.replace('Learning Objectives:', '').strip()
                    if content:
                        analysis['learning_objectives'] = [o.strip() for o in content.split(',')]
                elif line.startswith('Difficulty Level:'):
                    level = line.replace('Difficulty Level:', '').strip().lower()
                    if level in ['beginner', 'intermediate', 'advanced']:
                        analysis['difficulty_level'] = level
                elif current_section and line:
                    # Continue parsing multi-line sections
                    if current_section == 'topics':
                        analysis['topics'].extend([t.strip() for t in line.split(',') if t.strip()])
                    elif current_section == 'key_concepts':
                        analysis['key_concepts'].extend([c.strip() for c in line.split(',') if c.strip()])
                    elif current_section == 'learning_objectives':
                        analysis['learning_objectives'].extend([o.strip() for o in line.split(',') if o.strip()])
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error extracting topics and concepts: {str(e)}")
            return {
                'topics': [],
                'key_concepts': [],
                'learning_objectives': [],
                'difficulty_level': 'intermediate'
            }
    
    async def similarity_search(
        self,
        vector_store: Qdrant,
        query: str,
        k: int = 5
    ) -> List[Document]:
        """
        Perform similarity search on vector store.
        
        Args:
            vector_store: Qdrant vector store
            query: Search query
            k: Number of results to return
            
        Returns:
            List of similar documents
        """
        try:
            results = await asyncio.to_thread(
                vector_store.similarity_search,
                query,
                k=k
            )
            return results
            
        except Exception as e:
            logger.error(f"Error in similarity search: {str(e)}")
            return []
    
    async def process_pdf_with_langchain(
        self,
        file_path: str,
        document_id: str,
        chunk_size: int = 1200
    ) -> Dict[str, Any]:
        """
        Complete PDF processing using LangChain orchestration.
        
        Args:
            file_path: Path to PDF file
            document_id: Document identifier
            chunk_size: Target chunk size
            
        Returns:
            Processing results with documents and analysis
        """
        try:
            # Load and split PDF
            documents = await self.load_and_split_pdf(file_path, chunk_size)
            
            # Create vector store
            collection_name = f"langchain_{document_id}"
            vector_store = await self.create_vector_store(documents, collection_name)
            
            # Extract topics and concepts
            analysis = await self.extract_topics_and_concepts(documents)
            
            # Create summary using map-reduce
            summary = await self.summarize_documents_map_reduce(documents)
            
            return {
                'documents': documents,
                'vector_store': vector_store,
                'analysis': analysis,
                'summary': summary,
                'collection_name': collection_name,
                'total_chunks': len(documents)
            }
            
        except Exception as e:
            logger.error(f"Error in LangChain PDF processing: {str(e)}")
            raise


# Create singleton instance
langchain_service = LangChainService()
