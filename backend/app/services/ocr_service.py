import logging
import asyncio
import os
import tempfile
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
# import cv2
import numpy as np
from PIL import Image
# import pytesseract
import pdf2image
# from diskcache import Cache
# import aiofiles

from app.core.config import settings
from app.services.gemini_service import gemini_service

logger = logging.getLogger(__name__)

# Initialize cache for OCR results (simplified)
# cache = Cache(directory=os.path.join(tempfile.gettempdir(), "campuspq_ocr_cache"))
cache = {}


class OCRService:
    """Enhanced OCR service with handwriting support and confidence scoring."""
    
    def __init__(self):
        self.confidence_threshold = 60  # Minimum confidence for text extraction
        self.low_confidence_threshold = 40  # Threshold for flagging low-confidence pages
        
    async def preprocess_image(self, image) -> Image.Image:
        """
        Preprocess image for better OCR results (simplified version).

        Args:
            image: Input PIL Image

        Returns:
            Preprocessed image
        """
        try:
            # Simple preprocessing without OpenCV
            if isinstance(image, Image.Image):
                # Convert to grayscale if needed
                if image.mode != 'L':
                    image = image.convert('L')
                return image
            else:
                return image

        except Exception as e:
            logger.error(f"Error preprocessing image: {str(e)}")
            return image
    
    async def extract_text_with_confidence(
        self,
        image: Image.Image,
        use_handwriting_model: bool = False
    ) -> Tuple[str, float, Dict]:
        """
        Extract text from image with confidence scoring (simplified version).

        Args:
            image: Input PIL Image
            use_handwriting_model: Whether to use handwriting-capable OCR

        Returns:
            Tuple of (extracted_text, confidence_score, metadata)
        """
        try:
            # Simplified version - just return basic metadata
            # In a real implementation, this would use Tesseract

            # For now, return empty text and low confidence
            # This will trigger the Gemini fallback
            text = ""
            avg_confidence = 30.0  # Low confidence to trigger fallback

            # Create metadata
            metadata = {
                'avg_confidence': avg_confidence,
                'word_count': 0,
                'low_confidence_words': 0,
                'preprocessing_applied': True,
                'handwriting_model_used': use_handwriting_model,
                'simplified_ocr': True
            }

            return text.strip(), avg_confidence, metadata

        except Exception as e:
            logger.error(f"Error extracting text with confidence: {str(e)}")
            return "", 0.0, {}
    
    async def process_pdf_with_ocr(
        self, 
        file_path: str, 
        use_handwriting_detection: bool = True
    ) -> Dict[str, Any]:
        """
        Process PDF with enhanced OCR capabilities.
        
        Args:
            file_path: Path to PDF file
            use_handwriting_detection: Whether to detect and handle handwriting
            
        Returns:
            Dictionary with extracted text, confidence scores, and metadata
        """
        try:
            # Check cache first (simplified)
            cache_key = f"ocr_{os.path.basename(file_path)}_{os.path.getmtime(file_path)}"
            cached_result = cache.get(cache_key)
            if cached_result:
                logger.info(f"Using cached OCR result for {file_path}")
                return cached_result
            
            # Convert PDF to images
            images = await asyncio.to_thread(
                pdf2image.convert_from_path, 
                file_path, 
                dpi=300,  # High DPI for better OCR
                fmt='RGB'
            )
            
            pages_data = []
            total_text = ""
            low_confidence_pages = []
            
            for page_num, image in enumerate(images):
                try:
                    # Use PIL image directly
                    img_pil = image

                    # Try standard OCR first
                    text, confidence, metadata = await self.extract_text_with_confidence(
                        img_pil, use_handwriting_model=False
                    )

                    # If confidence is low and handwriting detection is enabled, try handwriting OCR
                    if (confidence < self.confidence_threshold and
                        use_handwriting_detection and
                        len(text.strip()) > 0):

                        hw_text, hw_confidence, hw_metadata = await self.extract_text_with_confidence(
                            img_pil, use_handwriting_model=True
                        )
                        
                        # Use handwriting result if it's better
                        if hw_confidence > confidence:
                            text, confidence, metadata = hw_text, hw_confidence, hw_metadata
                            metadata['handwriting_fallback_used'] = True
                    
                    # Tag low-confidence pages
                    if confidence < self.low_confidence_threshold:
                        low_confidence_pages.append(page_num + 1)
                        metadata['flagged_low_confidence'] = True
                    
                    page_data = {
                        'page_number': page_num + 1,
                        'text': text,
                        'confidence': confidence,
                        'metadata': metadata
                    }
                    
                    pages_data.append(page_data)
                    total_text += f"\n\n--- Page {page_num + 1} ---\n{text}"
                    
                except Exception as e:
                    logger.error(f"Error processing page {page_num + 1}: {str(e)}")
                    pages_data.append({
                        'page_number': page_num + 1,
                        'text': "",
                        'confidence': 0.0,
                        'metadata': {'error': str(e)}
                    })
            
            # Calculate overall statistics
            all_confidences = [page['confidence'] for page in pages_data if page['confidence'] > 0]
            avg_confidence = sum(all_confidences) / len(all_confidences) if all_confidences else 0
            
            result = {
                'total_text': total_text.strip(),
                'pages_data': pages_data,
                'overall_confidence': avg_confidence,
                'low_confidence_pages': low_confidence_pages,
                'total_pages': len(images),
                'processing_metadata': {
                    'handwriting_detection_used': use_handwriting_detection,
                    'cache_key': cache_key
                }
            }
            
            # Cache the result (simplified)
            cache[cache_key] = result
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing PDF with OCR: {str(e)}")
            raise
    
    async def extract_text_from_pdf_enhanced(
        self, 
        file_path: str, 
        fallback_to_gemini: bool = True
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Enhanced text extraction with OCR and Gemini fallback.
        
        Args:
            file_path: Path to PDF file
            fallback_to_gemini: Whether to use Gemini as fallback
            
        Returns:
            Tuple of (extracted_text, metadata)
        """
        try:
            # First try OCR
            ocr_result = await self.process_pdf_with_ocr(file_path)
            
            # Check if OCR was successful
            if (ocr_result['overall_confidence'] > self.confidence_threshold and 
                len(ocr_result['total_text']) > 100):
                return ocr_result['total_text'], ocr_result
            
            # If OCR failed or low confidence, try Gemini
            if fallback_to_gemini:
                try:
                    logger.info("OCR confidence low, falling back to Gemini")
                    prompt = "Extract all text content from this document, preserving structure and formatting."
                    gemini_text = await gemini_service.process_pdf_content(file_path, prompt)
                    
                    if gemini_text and len(gemini_text) > 100:
                        ocr_result['gemini_fallback_used'] = True
                        ocr_result['gemini_text'] = gemini_text
                        return gemini_text, ocr_result
                        
                except Exception as gemini_error:
                    logger.warning(f"Gemini fallback failed: {str(gemini_error)}")
            
            # Return OCR result even if low confidence
            return ocr_result['total_text'], ocr_result
            
        except Exception as e:
            logger.error(f"Error in enhanced text extraction: {str(e)}")
            raise


# Create singleton instance
ocr_service = OCRService()
