import logging
import asyncio
import time
from typing import Dict, List, Optional, Any

from fastapi import UploadFile
from sqlalchemy.orm import Session

from app.schemas.student_tools import (
    StudentNoteUploadResponse,
    StudentNoteCreate,
    StudentNoteUpdate,
    MCQGenerationResponse,
    MCQGenerationJobCreate,
    MCQGenerationJobUpdate,
    MCQGenerationStatusResponse,
    NoteGeneratedMCQCreate,
    FlashCardGenerationResponse,
    FlashCardGenerationJobCreate,
    FlashCardGenerationJobUpdate,
    FlashCardGenerationStatusResponse,
    SummaryGenerationResponse,
    SummaryGenerationJobCreate,
    SummaryGenerationJobUpdate,
    SummaryGenerationStatusResponse
)
from app.crud.crud_student_tools import (
    student_note,
    note_generated_mcq,
    mcq_generation_job,
    flash_card,
    note_generated_flash_card,
    flash_card_generation_job,
    note_summary,
    note_generated_summary,
    summary_generation_job
)
from app.models.student_tools import ProcessingStatus
from app.services.pdf_processing_service import pdf_processing_service
from app.services.mcq_generation_service import mcq_generation_service
from app.services.flash_card_generation_service import flash_card_generation_service
from app.services.summary_generation_service import summary_generation_service
from app.core.celery_app import celery_app

logger = logging.getLogger(__name__)


class StudentToolsService:
    """Service for managing student tools like note uploads and MCQ generation."""

    async def upload_notes(
        self,
        files: List[UploadFile],
        student_id: int,
        db: Session,
        course_name: Optional[str] = None
    ) -> List[StudentNoteUploadResponse]:
        """
        Upload and process student notes.

        Args:
            files: List of uploaded files
            student_id: ID of the student
            db: Database session
            course_name: Optional course name entered by the student

        Returns:
            List of upload responses
        """
        responses = []

        # Validate files
        is_valid, error_message = await pdf_processing_service.validate_pdf_upload(files)
        if not is_valid:
            # Return error for all files
            for file in files:
                responses.append(
                    StudentNoteUploadResponse(
                        note_id=0,
                        file_name=file.filename,
                        status=ProcessingStatus.FAILED,
                        message=error_message
                    )
                )
            return responses

        # Process files with lightning-fast parallel upload
        logger.info(f"🚀 Starting lightning-fast upload for {len(files)} files")
        upload_start_time = time.time()

        # Process each file
        for file in files:
            try:
                # Save the file
                file_info = await pdf_processing_service.save_pdf_file(file, student_id)

                # Create note record in database
                note_data = StudentNoteCreate(
                    student_id=student_id,
                    file_name=file_info["file_name"],
                    file_path=file_info["file_path"],
                    file_size=file_info["file_size"],
                    content_type=file_info["content_type"],
                    course_name=course_name  # Use manually entered course name if provided
                )

                note = student_note.create(db, obj_in=note_data)

                # Update status to uploading
                note = student_note.update(
                    db,
                    db_obj=note,
                    obj_in=StudentNoteUpdate(status=ProcessingStatus.UPLOADING)
                )

                # Add response
                responses.append(
                    StudentNoteUploadResponse(
                        note_id=note.id,
                        file_name=note.file_name,
                        status=note.status,
                        message="File uploaded successfully. Processing will begin shortly."
                    )
                )

                # Start processing in background using Celery
                celery_app.send_task('app.tasks.process_note', args=[note.id])

            except Exception as e:
                logger.error(f"Error uploading note: {str(e)}")
                responses.append(
                    StudentNoteUploadResponse(
                        note_id=0,
                        file_name=file.filename,
                        status=ProcessingStatus.FAILED,
                        message=f"Error uploading file: {str(e)}"
                    )
                )

        upload_time = time.time() - upload_start_time
        successful_uploads = len([r for r in responses if r.status != ProcessingStatus.FAILED])
        logger.info(f"⚡ Upload completed in {upload_time:.2f} seconds for {successful_uploads}/{len(files)} files")

        return responses

    async def _process_note(self, note_id: int, db: Session) -> None:
        """
        Process a note in the background.

        Args:
            note_id: ID of the note
            db: Database session
        """
        try:
            # Get the note
            note = student_note.get(db, id=note_id)
            if not note:
                logger.error(f"Note {note_id} not found")
                return

            # Update status to processing
            note = student_note.update(
                db,
                db_obj=note,
                obj_in=StudentNoteUpdate(status=ProcessingStatus.PROCESSING)
            )

            # Extract text from PDF
            text = await pdf_processing_service.extract_text_from_pdf(note.file_path)

            # Update processing metadata
            metadata = note.processing_metadata or {}
            metadata["text_length"] = len(text)
            note = student_note.update(
                db,
                db_obj=note,
                obj_in=StudentNoteUpdate(processing_metadata=metadata)
            )

            # Only detect topics, not course
            _, topics = await mcq_generation_service.detect_course(text, db)

            # Update note with detected topics
            note = student_note.update(
                db,
                db_obj=note,
                obj_in=StudentNoteUpdate(
                    detected_topics=topics
                )
            )

            # Update status to embedding
            note = student_note.update(
                db,
                db_obj=note,
                obj_in=StudentNoteUpdate(status=ProcessingStatus.EMBEDDING)
            )

            # Chunk text
            chunks = await pdf_processing_service.chunk_text(text)

            # Create embeddings
            embedding_ids = await pdf_processing_service.create_embeddings(chunks, note.id)

            # Update note with embedding info
            metadata["chunk_count"] = len(chunks)
            metadata["embedding_count"] = len(embedding_ids)
            note = student_note.update(
                db,
                db_obj=note,
                obj_in=StudentNoteUpdate(
                    embedding_id=embedding_ids[0] if embedding_ids else None,
                    processing_metadata=metadata,
                    status=ProcessingStatus.COMPLETED
                )
            )

        except Exception as e:
            logger.error(f"Error processing note {note_id}: {str(e)}")
            # Update note status to failed
            student_note.update(
                db,
                db_obj=note,
                obj_in=StudentNoteUpdate(
                    status=ProcessingStatus.FAILED,
                    processing_metadata={
                        **(note.processing_metadata or {}),
                        "error": str(e)
                    }
                )
            )

    async def generate_mcqs(
        self,
        note_ids: List[int],
        student_id: int,
        course_name: Optional[str],
        question_count: int,
        db: Session
    ) -> MCQGenerationResponse:
        """
        Generate MCQs from student notes.

        Args:
            note_ids: List of note IDs
            student_id: ID of the student
            course_name: Name of the course (optional)
            question_count: Number of questions to generate
            db: Database session

        Returns:
            MCQ generation response
        """
        try:
            # Validate note IDs
            notes = []
            for note_id in note_ids:
                note = student_note.get(db, id=note_id)
                if note and note.student_id == student_id and note.status == ProcessingStatus.COMPLETED:
                    notes.append(note)

            if not notes:
                return MCQGenerationResponse(
                    job_id=0,
                    status=ProcessingStatus.FAILED,
                    progress_percentage=0,
                    message="No valid completed notes found"
                )

            # If course_name is not provided, use the first note's course name
            if not course_name and notes[0].course_name:
                course_name = notes[0].course_name

            # Generate a meaningful title based on note file names
            if len(notes) == 1:
                # Single note: use the file name without extension
                title = notes[0].file_name.rsplit('.', 1)[0] if '.' in notes[0].file_name else notes[0].file_name
            else:
                # Multiple notes: create a descriptive title
                if len(notes) <= 3:
                    # For 2-3 notes, list them
                    note_names = [note.file_name.rsplit('.', 1)[0] if '.' in note.file_name else note.file_name for note in notes]
                    title = " + ".join(note_names)
                else:
                    # For more than 3 notes, use a summary format
                    first_note_name = notes[0].file_name.rsplit('.', 1)[0] if '.' in notes[0].file_name else notes[0].file_name
                    title = f"{first_note_name} + {len(notes) - 1} more"

            # Truncate title if too long
            if title and len(title) > 100:
                title = title[:97] + "..."

            # Create MCQ generation job
            job_data = MCQGenerationJobCreate(
                student_id=student_id,
                note_ids=[note.id for note in notes],
                title=title,
                course_name=course_name,
                question_count=question_count
            )

            job = mcq_generation_job.create(db, obj_in=job_data)

            # Start generation in background using Celery
            celery_app.send_task('app.tasks.generate_mcqs', args=[job.id])

            return MCQGenerationResponse(
                job_id=job.id,
                status=job.status,
                progress_percentage=job.progress_percentage,
                message="MCQ generation started"
            )

        except Exception as e:
            logger.error(f"Error starting MCQ generation: {str(e)}")
            return MCQGenerationResponse(
                job_id=0,
                status=ProcessingStatus.FAILED,
                progress_percentage=0,
                message=f"Error starting MCQ generation: {str(e)}"
            )

    async def _generate_mcqs_task(self, job_id: int, db: Session) -> None:
        """
        Generate MCQs in the background.

        Args:
            job_id: ID of the MCQ generation job
            db: Database session
        """
        try:
            # Get the job
            job = mcq_generation_job.get(db, id=job_id)
            if not job:
                logger.error(f"Job {job_id} not found")
                return

            # Update status to processing
            job = mcq_generation_job.update(
                db,
                db_obj=job,
                obj_in=MCQGenerationJobUpdate(
                    status=ProcessingStatus.PROCESSING,
                    progress_percentage=10
                )
            )

            # Get notes
            note_contents = []
            for note_id in job.note_ids:
                note = student_note.get(db, id=note_id)
                if note and note.status == ProcessingStatus.COMPLETED:
                    # Extract text from PDF
                    text = await pdf_processing_service.extract_text_from_pdf(note.file_path)
                    note_contents.append(text)

            # Update progress
            job = mcq_generation_job.update(
                db,
                db_obj=job,
                obj_in=MCQGenerationJobUpdate(progress_percentage=30)
            )

            # Generate MCQs using lightning-fast enhanced pipeline
            logger.info(f"🚀 Starting lightning-fast MCQ generation for {job.question_count} questions")
            logger.info(f"📊 Strategy: {'Single batch' if job.question_count <= 10 else 'Parallel chunks' if job.question_count <= 30 else 'Advanced parallel'}")
            start_time = time.time()

            questions = await mcq_generation_service.generate_mcqs_enhanced(
                note_contents,
                job.course_name,
                job.question_count,
                db,
                use_enhanced_pipeline=True  # Force enhanced pipeline for speed
            )

            generation_time = time.time() - start_time
            questions_per_second = len(questions) / generation_time if generation_time > 0 else 0
            logger.info(f"⚡ MCQ generation completed in {generation_time:.2f} seconds for {len(questions)} questions")
            logger.info(f"🎯 Performance: {questions_per_second:.1f} questions/second")

            # Update progress
            job = mcq_generation_job.update(
                db,
                db_obj=job,
                obj_in=MCQGenerationJobUpdate(progress_percentage=70)
            )

            # Create question objects
            question_ids = await mcq_generation_service.create_question_objects(
                questions,
                job.course_name,
                job.student_id,
                db,
                job.id  # Pass the job ID
            )

            # If no question IDs were generated, log a warning
            if not question_ids:
                logger.warning("No question IDs generated")

            # Log the question IDs
            logger.info(f"Question IDs: {question_ids}")

            # Create NoteGeneratedMCQ links
            try:
                for note_id in job.note_ids:
                    for question_id in question_ids:
                        try:
                            link_data = NoteGeneratedMCQCreate(
                                note_id=note_id,
                                question_id=question_id
                            )
                            note_generated_mcq.create(db, obj_in=link_data)
                        except Exception as e:
                            logger.error(f"Error creating NoteGeneratedMCQ link: {str(e)}")
                            continue
            except Exception as e:
                logger.error(f"Error creating NoteGeneratedMCQ links: {str(e)}")

            # If no question IDs were generated, log a warning
            if not question_ids:
                logger.warning("No question IDs generated in _generate_mcqs_task")

            # Update job as completed
            job = mcq_generation_job.update(
                db,
                db_obj=job,
                obj_in=MCQGenerationJobUpdate(
                    status=ProcessingStatus.COMPLETED,
                    progress_percentage=100,
                    generated_question_ids=question_ids
                )
            )

            # Log the generated question IDs
            logger.info(f"Generated question IDs: {question_ids}")

        except Exception as e:
            logger.error(f"Error generating MCQs for job {job_id}: {str(e)}")
            # Update job status to failed
            mcq_generation_job.update(
                db,
                db_obj=job,
                obj_in=MCQGenerationJobUpdate(
                    status=ProcessingStatus.FAILED,
                    error_message=str(e)
                )
            )

    async def get_job_status(self, job_id: int, student_id: int, db: Session) -> Optional[MCQGenerationStatusResponse]:
        """
        Get the status of an MCQ generation job.

        Args:
            job_id: ID of the MCQ generation job
            student_id: ID of the student
            db: Database session

        Returns:
            MCQ generation status response
        """
        job = mcq_generation_job.get(db, id=job_id)

        if not job or job.student_id != student_id:
            return None

        # Get the generated question IDs
        generated_question_ids = job.generated_question_ids
        if job.status == ProcessingStatus.COMPLETED and not generated_question_ids:
            logger.warning(f"Job {job_id} is completed but has no generated question IDs")

        return MCQGenerationStatusResponse(
            job_id=job.id,
            status=job.status,
            progress_percentage=job.progress_percentage,
            note_ids=job.note_ids,
            title=job.title,
            course_name=job.course_name,
            question_count=job.question_count,
            generated_question_ids=generated_question_ids,
            error_message=job.error_message,
            created_at=job.created_at,
            updated_at=job.updated_at
        )

    async def generate_flash_cards(
        self,
        note_ids: List[int],
        student_id: int,
        course_name: Optional[str],
        card_count: int,
        db: Session
    ) -> FlashCardGenerationResponse:
        """
        Generate flash cards from student notes.

        Args:
            note_ids: List of note IDs
            student_id: ID of the student
            course_name: Name of the course (optional)
            card_count: Number of flash cards to generate
            db: Database session

        Returns:
            Flash card generation response
        """
        try:
            # Validate note IDs
            notes = []
            for note_id in note_ids:
                note = student_note.get(db, id=note_id)
                if note and note.student_id == student_id and note.status == ProcessingStatus.COMPLETED:
                    notes.append(note)

            if not notes:
                return FlashCardGenerationResponse(
                    job_id=0,
                    status=ProcessingStatus.FAILED,
                    progress_percentage=0,
                    message="No valid completed notes found"
                )

            # If course_name is not provided, use the first note's course name
            if not course_name and notes[0].course_name:
                course_name = notes[0].course_name

            # Generate a meaningful title based on note file names
            if len(notes) == 1:
                # Single note: use the file name without extension
                title = notes[0].file_name.rsplit('.', 1)[0] if '.' in notes[0].file_name else notes[0].file_name
            else:
                # Multiple notes: create a descriptive title
                if len(notes) <= 3:
                    # For 2-3 notes, list them
                    note_names = [note.file_name.rsplit('.', 1)[0] if '.' in note.file_name else note.file_name for note in notes]
                    title = " + ".join(note_names)
                else:
                    # For more than 3 notes, use a summary format
                    first_note_name = notes[0].file_name.rsplit('.', 1)[0] if '.' in notes[0].file_name else notes[0].file_name
                    title = f"{first_note_name} + {len(notes) - 1} more"

            # Truncate title if too long
            if title and len(title) > 100:
                title = title[:97] + "..."

            # Create flash card generation job
            job_data = FlashCardGenerationJobCreate(
                student_id=student_id,
                note_ids=[note.id for note in notes],
                title=title,
                course_name=course_name,
                card_count=card_count
            )

            job = flash_card_generation_job.create(db, obj_in=job_data)

            # Start generation in background using Celery
            celery_app.send_task('app.tasks.generate_flash_cards', args=[job.id])

            return FlashCardGenerationResponse(
                job_id=job.id,
                status=job.status,
                progress_percentage=job.progress_percentage,
                message="Flash card generation started"
            )

        except Exception as e:
            logger.error(f"Error starting flash card generation: {str(e)}")
            return FlashCardGenerationResponse(
                job_id=0,
                status=ProcessingStatus.FAILED,
                progress_percentage=0,
                message=f"Error starting flash card generation: {str(e)}"
            )

    async def get_flash_card_job_status(self, job_id: int, student_id: int, db: Session) -> Optional[FlashCardGenerationStatusResponse]:
        """
        Get the status of a flash card generation job.

        Args:
            job_id: ID of the flash card generation job
            student_id: ID of the student
            db: Database session

        Returns:
            Flash card generation status response
        """
        job = flash_card_generation_job.get(db, id=job_id)

        if not job or job.student_id != student_id:
            return None

        # Get the generated card IDs
        generated_card_ids = job.generated_card_ids
        if job.status == ProcessingStatus.COMPLETED and not generated_card_ids:
            logger.warning(f"Job {job_id} is completed but has no generated card IDs")

        return FlashCardGenerationStatusResponse(
            job_id=job.id,
            status=job.status,
            progress_percentage=job.progress_percentage,
            note_ids=job.note_ids,
            title=job.title,
            course_name=job.course_name,
            card_count=job.card_count,
            generated_card_ids=generated_card_ids,
            error_message=job.error_message,
            created_at=job.created_at,
            updated_at=job.updated_at
        )

    async def generate_summary(
        self,
        note_ids: List[int],
        student_id: int,
        course_name: Optional[str],
        db: Session
    ) -> SummaryGenerationResponse:
        """
        Generate summary from student notes using multiagent system.

        Args:
            note_ids: List of note IDs
            student_id: ID of the student
            course_name: Name of the course (optional)
            db: Database session

        Returns:
            Summary generation response
        """
        try:
            logger.info(f"Starting summary generation for notes {note_ids}, student {student_id}")

            # Validate note IDs
            notes = []
            for note_id in note_ids:
                note = student_note.get(db, id=note_id)
                logger.info(f"Checking note {note_id}: found={note is not None}")

                if note:
                    logger.info(f"Note {note_id} details: student_id={note.student_id}, status={note.status}")

                    if note.student_id == student_id:
                        # Allow notes that are completed or have content
                        if note.status == ProcessingStatus.COMPLETED or (hasattr(note, 'content') and note.content):
                            notes.append(note)
                            logger.info(f"Note {note_id} added to processing list")
                        else:
                            logger.warning(f"Note {note_id} has status {note.status} - may not be ready for summary generation")
                    else:
                        logger.warning(f"Note {note_id} belongs to student {note.student_id}, not {student_id}")
                else:
                    logger.warning(f"Note {note_id} not found in database")

            if not notes:
                return SummaryGenerationResponse(
                    job_id=0,
                    status=ProcessingStatus.FAILED,
                    progress_percentage=0,
                    message="No valid completed notes found"
                )

            # If course_name is not provided, use the first note's course name
            if not course_name and notes[0].course_name:
                course_name = notes[0].course_name

            # Create summary generation job
            logger.info(f"Creating summary job for {len(notes)} notes, course: {course_name}")

            job_data = SummaryGenerationJobCreate(
                student_id=student_id,
                note_ids=[note.id for note in notes],
                course_name=course_name
            )

            try:
                job = summary_generation_job.create(db, obj_in=job_data)
                logger.info(f"Created summary job with ID: {job.id}")
            except Exception as e:
                logger.error(f"Error creating summary job: {str(e)}")
                return SummaryGenerationResponse(
                    job_id=0,
                    status=ProcessingStatus.FAILED,
                    progress_percentage=0,
                    message=f"Error creating summary job: {str(e)}"
                )

            # Start generation in background using Celery
            try:
                celery_app.send_task('app.tasks.generate_summary', args=[job.id])
                logger.info(f"Sent Celery task for job {job.id}")
            except Exception as e:
                logger.error(f"Error sending Celery task: {str(e)}")
                # Continue anyway - the job is created

            return SummaryGenerationResponse(
                job_id=job.id,
                status=job.status,
                progress_percentage=job.progress_percentage,
                message="Summary generation started"
            )

        except Exception as e:
            logger.error(f"Error starting summary generation: {str(e)}")
            return SummaryGenerationResponse(
                job_id=0,
                status=ProcessingStatus.FAILED,
                progress_percentage=0,
                message=f"Error starting summary generation: {str(e)}"
            )

    async def _generate_summary_task(self, job_id: int, db: Session) -> None:
        """
        Generate summary in the background using multiagent system.

        Args:
            job_id: ID of the summary generation job
            db: Database session
        """
        try:
            # Get the job
            job = summary_generation_job.get(db, id=job_id)
            if not job:
                logger.error(f"Summary job {job_id} not found")
                return

            # Update status to processing
            job = summary_generation_job.update(
                db,
                db_obj=job,
                obj_in=SummaryGenerationJobUpdate(
                    status=ProcessingStatus.PROCESSING,
                    progress_percentage=10
                )
            )

            # Generate summary using the multiagent service
            logger.info(f"🚀 Starting lightning-fast summary generation for notes {job.note_ids}")
            start_time = time.time()

            result = await summary_generation_service.generate_summary_from_notes(
                note_ids=job.note_ids,
                student_id=job.student_id,
                course_name=job.course_name,
                db=db,
                job_id=job.id
            )

            generation_time = time.time() - start_time
            logger.info(f"⚡ Summary generation completed in {generation_time:.2f} seconds")

            if "error" in result:
                # Update job status to failed
                summary_generation_job.update(
                    db,
                    db_obj=job,
                    obj_in=SummaryGenerationJobUpdate(
                        status=ProcessingStatus.FAILED,
                        error_message=result["error"]
                    )
                )
            else:
                # Job should already be updated by the summary service
                logger.info(f"Summary generation job {job_id} completed successfully")

        except Exception as e:
            logger.error(f"Error generating summary for job {job_id}: {str(e)}")
            # Update job status to failed
            summary_generation_job.update(
                db,
                db_obj=job,
                obj_in=SummaryGenerationJobUpdate(
                    status=ProcessingStatus.FAILED,
                    error_message=str(e)
                )
            )

    async def get_summary_job_status(self, job_id: int, student_id: int, db: Session) -> Optional[SummaryGenerationStatusResponse]:
        """
        Get the status of a summary generation job.

        Args:
            job_id: ID of the summary generation job
            student_id: ID of the student
            db: Database session

        Returns:
            Summary generation status response
        """
        job = summary_generation_job.get(db, id=job_id)

        if not job or job.student_id != student_id:
            return None

        return SummaryGenerationStatusResponse(
            job_id=job.id,
            status=job.status,
            progress_percentage=job.progress_percentage,
            note_ids=job.note_ids,
            course_name=job.course_name,
            generated_summary_ids=job.generated_summary_ids,
            error_message=job.error_message,
            processing_metadata=job.processing_metadata,
            created_at=job.created_at,
            updated_at=job.updated_at
        )

    async def generate_summary_for_single_note(
        self,
        note_id: int,
        student_id: int,
        db: Session
    ) -> SummaryGenerationResponse:
        """
        Generate summary for a single note (convenience method).

        Args:
            note_id: ID of the note to summarize
            student_id: ID of the student
            db: Database session

        Returns:
            Summary generation response
        """
        # Get note to determine course name
        note = student_note.get(db, id=note_id)
        if not note:
            return SummaryGenerationResponse(
                job_id=0,
                status=ProcessingStatus.FAILED,
                progress_percentage=0,
                message="Note not found"
            )

        if note.student_id != student_id:
            return SummaryGenerationResponse(
                job_id=0,
                status=ProcessingStatus.FAILED,
                progress_percentage=0,
                message="Access denied"
            )

        # Generate summary
        return await self.generate_summary(
            note_ids=[note_id],
            student_id=student_id,
            course_name=note.course_name,
            db=db
        )


# Create a singleton instance
student_tools_service = StudentToolsService()
