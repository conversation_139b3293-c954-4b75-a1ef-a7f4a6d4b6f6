import logging
import os
import asyncio
import time
from typing import List, Dict, Any, Optional, Union
import io
import random

import google.generativeai as genai
from google.api_core.exceptions import ResourceExhausted, DeadlineExceeded

from app.core.config import settings
from app.core.rate_limiting_config import rate_limit_config

logger = logging.getLogger(__name__)

class RateLimitError(Exception):
    """Custom exception for rate limit errors."""
    def __init__(self, message: str, retry_after: int = 60):
        super().__init__(message)
        self.retry_after = retry_after

class GeminiService:
    """Service for interacting with Google Gemini API with rate limiting and retry logic."""

    def __init__(self):
        """Initialize the Gemini service."""
        self.api_key = settings.GEMINI_API_KEY
        genai.configure(api_key=self.api_key)
        self.model_name = "gemini-1.5-flash"  # Default model for faster processing

        # Rate limiting settings from config
        self.max_retries = rate_limit_config.gemini_max_retries
        self.base_delay = rate_limit_config.gemini_base_delay
        self.max_delay = rate_limit_config.gemini_max_delay
        self.jitter_range = rate_limit_config.gemini_jitter_range

        # Track last request time for basic rate limiting
        self.last_request_time = 0
        self.min_request_interval = rate_limit_config.gemini_min_request_interval

    async def _wait_for_rate_limit(self):
        """Ensure minimum interval between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.min_request_interval:
            wait_time = self.min_request_interval - time_since_last
            logger.info(f"Rate limiting: waiting {wait_time:.2f} seconds")
            await asyncio.sleep(wait_time)

        self.last_request_time = time.time()

    def _calculate_backoff_delay(self, attempt: int, suggested_delay: Optional[int] = None) -> float:
        """Calculate exponential backoff delay with jitter."""
        if suggested_delay:
            # Use the suggested delay from the API response
            base_delay = min(suggested_delay, self.max_delay)
        else:
            # Exponential backoff: 2^attempt * base_delay
            base_delay = min(self.base_delay * (2 ** attempt), self.max_delay)

        # Add jitter to avoid thundering herd
        jitter = base_delay * self.jitter_range * (2 * random.random() - 1)
        delay = base_delay + jitter

        return max(0, delay)

    def _is_rate_limit_error(self, error: Exception) -> tuple[bool, Optional[int]]:
        """Check if error is a rate limit error and extract retry delay."""
        error_str = str(error).lower()

        # Check for 429 rate limit errors
        if "429" in error_str or "quota" in error_str or "rate limit" in error_str:
            # Try to extract retry delay from error message
            retry_after = None
            if "retry_delay" in error_str:
                try:
                    # Extract seconds from retry_delay
                    import re
                    match = re.search(r'seconds: (\d+)', error_str)
                    if match:
                        retry_after = int(match.group(1))
                except:
                    pass

            return True, retry_after

        # Check for ResourceExhausted exception
        if isinstance(error, ResourceExhausted):
            return True, None

        return False, None

    async def _make_request_with_retry(self, request_func, *args, **kwargs):
        """Make a request with exponential backoff retry logic."""
        last_exception = None

        for attempt in range(self.max_retries):
            try:
                # Wait for rate limit before making request
                await self._wait_for_rate_limit()

                # Make the request
                result = await request_func(*args, **kwargs)
                return result

            except Exception as e:
                last_exception = e
                is_rate_limit, suggested_delay = self._is_rate_limit_error(e)

                if is_rate_limit:
                    if attempt < self.max_retries - 1:
                        delay = self._calculate_backoff_delay(attempt, suggested_delay)
                        logger.warning(
                            f"Rate limit hit (attempt {attempt + 1}/{self.max_retries}). "
                            f"Retrying in {delay:.2f} seconds. Error: {str(e)[:200]}"
                        )
                        await asyncio.sleep(delay)
                        continue
                    else:
                        logger.error(f"Rate limit exceeded after {self.max_retries} attempts")
                        raise RateLimitError(
                            f"Gemini API rate limit exceeded after {self.max_retries} attempts",
                            retry_after=suggested_delay or 60
                        )
                else:
                    # Non-rate-limit error, don't retry
                    logger.error(f"Non-retryable error: {str(e)}")
                    raise e

        # If we get here, all retries failed
        raise last_exception

    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        response_format: Optional[Dict] = None
    ) -> Any:
        """
        Generate a chat completion using Gemini API.

        Args:
            messages: List of messages in the conversation
            model: Gemini model to use
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate
            response_format: Format for the response (e.g., JSON)

        Returns:
            Gemini API response formatted like OpenAI's response
        """
        async def _make_chat_request():
            # Convert OpenAI-style messages to Gemini format
            system_message = None
            user_messages = []

            for message in messages:
                role = message.get("role", "user")
                content = message.get("content", "")

                if role == "system":
                    system_message = content
                elif role == "user":
                    user_messages.append(content)

            # Set up generation config
            generation_config = genai.GenerationConfig(
                temperature=temperature,
            )

            if max_tokens:
                generation_config.max_output_tokens = max_tokens

            # Use the specified model or default
            model_to_use = model or self.model_name

            # Create the model
            gemini_model = genai.GenerativeModel(model_name=model_to_use)

            # Handle JSON response format
            if response_format and response_format.get("type") == "json_object":
                # Set response format to JSON
                generation_config.response_mime_type = "application/json"

            # Combine system message and user message if needed
            prompt = ""
            if system_message:
                prompt = f"System: {system_message}\n\n"

            prompt += "\n".join(user_messages)

            # Call Gemini API
            response = await asyncio.to_thread(
                gemini_model.generate_content,
                prompt,
                generation_config=generation_config
            )

            # Format response to be similar to OpenAI's format
            formatted_response = {
                "choices": [
                    {
                        "message": {
                            "content": response.text,
                            "role": "assistant"
                        }
                    }
                ]
            }

            return formatted_response

        # Use retry logic for the request
        return await self._make_request_with_retry(_make_chat_request)



    async def upload_file(self, file_content: Union[bytes, io.BytesIO], mime_type: str = "application/pdf") -> Any:
        """
        Upload a file to Gemini using the File API.

        Args:
            file_content: Content of the file as bytes or BytesIO
            mime_type: MIME type of the file

        Returns:
            File object that can be used in generate_content calls
        """
        try:
            # If file_content is BytesIO, get the bytes
            if isinstance(file_content, io.BytesIO):
                file_content.seek(0)
                file_bytes = file_content.read()
            else:
                file_bytes = file_content

            # Upload the file using the File API
            file_obj = await asyncio.to_thread(
                genai.upload_file,
                file=file_bytes,
                mime_type=mime_type
            )

            return file_obj
        except Exception as e:
            logger.error(f"Error uploading file to Gemini: {str(e)}")
            raise

    async def process_pdf(self, file_obj: Any, prompt: str) -> str:
        """
        Process a PDF file with Gemini.

        Args:
            file_obj: File object from upload_file
            prompt: Prompt to send with the PDF

        Returns:
            Generated content
        """
        try:
            # Create the model
            gemini_model = genai.GenerativeModel(model_name=self.model_name)

            # Generate content with the file and prompt
            response = await asyncio.to_thread(
                gemini_model.generate_content,
                [file_obj, prompt]
            )

            return response.text
        except Exception as e:
            logger.error(f"Error processing PDF with Gemini: {str(e)}")
            raise

    async def process_pdf_content(self, file_path: str, prompt: str) -> str:
        """
        Process a PDF file with Gemini using direct file reading with retry logic.

        Args:
            file_path: Path to the PDF file
            prompt: Prompt to send with the PDF

        Returns:
            Generated content
        """
        async def _make_pdf_request():
            # Read the file
            with open(file_path, "rb") as f:
                file_bytes = f.read()

            # Create the model
            gemini_model = genai.GenerativeModel(model_name=self.model_name)

            # Process with Gemini using document understanding
            response = await asyncio.to_thread(
                gemini_model.generate_content,
                [
                    {"mime_type": "application/pdf", "data": file_bytes},
                    prompt
                ]
            )

            return response.text

        # Use retry logic for the request
        return await self._make_request_with_retry(_make_pdf_request)

    async def process_pdf_with_file_api(self, file_path: str, prompt: str) -> str:
        """
        Process a PDF file with Gemini using the File API for larger documents.

        Args:
            file_path: Path to the PDF file
            prompt: Prompt to send with the PDF

        Returns:
            Generated content
        """
        try:
            # Upload the file using the File API
            file_obj = await asyncio.to_thread(
                genai.upload_file,
                file_path=file_path
            )

            # Create the model
            gemini_model = genai.GenerativeModel(model_name=self.model_name)

            # Generate content with the file and prompt
            response = await asyncio.to_thread(
                gemini_model.generate_content,
                [file_obj, prompt]
            )

            return response.text
        except Exception as e:
            logger.error(f"Error processing PDF with Gemini File API: {str(e)}")
            raise

    def process_pdf_content_sync(self, file_path: str, prompt: str) -> str:
        """
        Synchronous version of process_pdf_content for Celery tasks with retry logic.
        """
        def _make_pdf_request_sync():
            # Read the file
            with open(file_path, "rb") as f:
                file_bytes = f.read()

            # Create the model
            gemini_model = genai.GenerativeModel(model_name=self.model_name)

            # Process with Gemini using document understanding
            response = gemini_model.generate_content(
                [
                    {"mime_type": "application/pdf", "data": file_bytes},
                    prompt
                ]
            )

            return response.text

        # Use synchronous retry logic
        return self._make_request_with_retry_sync(_make_pdf_request_sync)

    def _make_request_with_retry_sync(self, request_func, *args, **kwargs):
        """Synchronous version of retry logic for Celery tasks."""
        last_exception = None

        for attempt in range(self.max_retries):
            try:
                # Basic rate limiting
                current_time = time.time()
                time_since_last = current_time - self.last_request_time

                if time_since_last < self.min_request_interval:
                    wait_time = self.min_request_interval - time_since_last
                    logger.info(f"Rate limiting: waiting {wait_time:.2f} seconds")
                    time.sleep(wait_time)

                self.last_request_time = time.time()

                # Make the request
                result = request_func(*args, **kwargs)
                return result

            except Exception as e:
                last_exception = e
                is_rate_limit, suggested_delay = self._is_rate_limit_error(e)

                if is_rate_limit:
                    if attempt < self.max_retries - 1:
                        delay = self._calculate_backoff_delay(attempt, suggested_delay)
                        logger.warning(
                            f"Rate limit hit (attempt {attempt + 1}/{self.max_retries}). "
                            f"Retrying in {delay:.2f} seconds. Error: {str(e)[:200]}"
                        )
                        time.sleep(delay)
                        continue
                    else:
                        logger.error(f"Rate limit exceeded after {self.max_retries} attempts")
                        raise RateLimitError(
                            f"Gemini API rate limit exceeded after {self.max_retries} attempts",
                            retry_after=suggested_delay or 60
                        )
                else:
                    # Non-rate-limit error, don't retry
                    logger.error(f"Non-retryable error: {str(e)}")
                    raise e

        # If we get here, all retries failed
        raise last_exception

    def process_pdf_with_file_api_sync(self, file_path: str, prompt: str) -> str:
        """
        Synchronous version of process_pdf_with_file_api for Celery tasks.
        """
        try:
            # Upload the file using the File API
            file_obj = genai.upload_file(file_path=file_path)

            # Create the model
            gemini_model = genai.GenerativeModel(model_name=self.model_name)

            # Generate content with the file and prompt
            response = gemini_model.generate_content([file_obj, prompt])

            return response.text
        except Exception as e:
            logger.error(f"Error processing PDF with Gemini File API in sync mode: {str(e)}")
            raise




# Create a singleton instance
gemini_service = GeminiService()
