import logging
import asyncio
import json
import os
import time
from typing import Dict, List, Optional, Tuple, Any

from sqlalchemy.orm import Session

from app.core.config import settings
from app.models.question import QuestionType, DifficultyLevel
from app.models.student_tools import ProcessingStatus
from app.schemas.question import QuestionCreate
from app.services import course_service, question_service
from app.schemas.course import CourseCreate
from app.utils.vector_store import vector_store
from app.services.gemini_service import gemini_service
from app.services.prompting_service import prompting_service
from app.services.enhanced_pdf_service import enhanced_pdf_service
from app.services.langchain_multiagent_service import (
    langchain_multiagent_service,
    MultiAgentConfig
)

logger = logging.getLogger(__name__)


class MCQGenerationService:
    """Service for generating MCQs from student notes using Gemini."""

    def __init__(self):
        """Initialize the MCQ generation service."""
        self.model = "gemini-2.0-flash"  # Gemini model for faster processing

    def get_or_create_default_course(self, db: Session, course_name: Optional[str] = None) -> int:
        """
        Create a new course for note-generated questions.

        For note-generated content, we ALWAYS create a new course with the note name
        instead of trying to match existing courses. This ensures proper isolation
        and prevents exam mode issues.

        Args:
            db: Database session
            course_name: Course name from PDF upload (note title)

        Returns:
            Course ID of the newly created course
        """
        try:
            logger.info(f"Creating new course for note-generated questions: {course_name}")

            # Get the first school for the course
            from app.models.school import School
            school = db.query(School).first()
            if not school:
                # Create a default school if none exists
                school = School(
                    name="Default Institution",
                    description="Default institution for note-generated content",
                    location="Global",
                    is_active=True
                )
                db.add(school)
                db.commit()
                db.refresh(school)
                logger.info(f"Created default school: {school.name}")

            # Always create a new course for note-generated content
            default_course_name = course_name or "Student Notes Course"

            # Create a unique course code using timestamp to avoid conflicts
            import time
            timestamp = str(int(time.time()))[-6:]  # Last 6 digits of timestamp
            course_code = f"NOTE{timestamp}"

            course_data = CourseCreate(
                name=default_course_name,
                description=f"Auto-generated course for student notes: {course_name or 'General'}",
                code=course_code,
                school_id=school.id,
                is_active=True
            )

            course = course_service.create(db, obj_in=course_data)
            logger.info(f"Created new course for notes: {course.name} (ID: {course.id}, Code: {course.code})")
            return course.id

        except Exception as e:
            logger.error(f"Error creating course for note-generated questions: {str(e)}")
            # As a last resort, try to get any existing course
            try:
                courses = course_service.get_multi(db, limit=1)
                if courses:
                    logger.warning(f"Falling back to existing course: {courses[0].name} (ID: {courses[0].id})")
                    return courses[0].id
                else:
                    logger.error("No courses available and failed to create new course")
                    return 1  # Last resort fallback
            except Exception as fallback_error:
                logger.error(f"Fallback course retrieval also failed: {str(fallback_error)}")
                return 1

    async def detect_course(self, note_content: str, db: Session) -> Tuple[Optional[int], List[str]]:
        """
        Detect the course and topics from note content.

        Args:
            note_content: Text content of the note
            db: Database session

        Returns:
            Tuple of (course_id, topics)
        """
        try:
            # Get all courses
            try:
                courses = course_service.get_multi(db)
            except Exception as e:
                logger.error(f"Error getting courses: {str(e)}")
                return None, []

            # Create a prompt for course detection
            course_names = [f"{course.id}: {course.name} ({course.code})" for course in courses]
            course_list = "\n".join(course_names)

            prompt = f"""
            You are an AI assistant that helps identify the academic course and topics that a student's notes belong to.

            Here is a list of available courses:
            {course_list}

            TASK:
            Carefully analyze the student notes and determine which course from the list above they most likely belong to.
            Look for specific terminology, concepts, formulas, or topics that are characteristic of a particular course.
            Pay attention to subject-specific vocabulary and themes.

            Based on the following student notes, identify:
            1. The most likely course ID from the list above (use ONLY IDs from the provided list)
            2. A list of 3-5 main topics covered in these notes
            3. Your confidence level (HIGH, MEDIUM, or LOW) in the course identification

            Format your response as a JSON object with the following structure:
            {{
                "course_id": [course ID as integer],
                "topics": [list of topics as strings],
                "confidence": "HIGH|MEDIUM|LOW"
            }}

            If you cannot determine the course with reasonable confidence, set course_id to null.

            Here are the student notes (truncated for brevity):
            {note_content[:3000]}...
            """

            # Call Gemini API with retry logic
            max_retries = 3
            retry_count = 0
            course_id = None
            topics = []

            while retry_count < max_retries:
                try:
                    # Call Gemini API with JSON response format
                    response = await gemini_service.chat_completion(
                        messages=[
                            {"role": "system", "content": "You are a helpful academic assistant that identifies courses and topics from student notes."},
                            {"role": "user", "content": prompt}
                        ],
                        model=self.model,
                        temperature=0.3,
                        max_tokens=500,
                        response_format={"type": "json_object"}
                    )

                    # Parse the response
                    try:
                        result = json.loads(response["choices"][0]["message"]["content"])

                        course_id = result.get("course_id")
                        topics = result.get("topics", [])
                        confidence = result.get("confidence", "LOW")

                        # Log the confidence level
                        if course_id is not None:
                            logger.info(f"Detected course ID {course_id} with confidence {confidence}")

                        # If confidence is LOW and we have more retries, try again
                        if course_id is not None and confidence == "LOW" and retry_count < max_retries - 1:
                            logger.warning(f"Low confidence in course detection, retrying ({retry_count + 1}/{max_retries})")
                            retry_count += 1
                            continue

                        # If we got a course ID or topics, break the retry loop
                        if course_id is not None or topics:
                            break
                        else:
                            logger.warning(f"Gemini API returned no course ID or topics, retrying ({retry_count + 1}/{max_retries})")
                            retry_count += 1

                    except json.JSONDecodeError as e:
                        logger.error(f"Error parsing JSON response: {str(e)}")
                        logger.error(f"Response content: {response['choices'][0]['message']['content'][:500]}...")
                        retry_count += 1

                except Exception as e:
                    logger.error(f"Error calling Gemini API: {str(e)}")
                    retry_count += 1

                # Wait before retrying
                if retry_count < max_retries:
                    await asyncio.sleep(1)

            # Verify course_id exists
            if course_id is not None:
                try:
                    course = course_service.get(db, id=course_id)
                    if not course:
                        course_id = None
                except Exception as e:
                    logger.error(f"Error verifying course: {str(e)}")
                    course_id = None

            return course_id, topics

        except Exception as e:
            logger.error(f"Error detecting course: {str(e)}")
            return None, []

    async def generate_mcqs(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        question_count: int,
        db: Session
    ) -> List[Dict]:
        """
        Generate MCQs from note contents.

        Args:
            note_contents: List of note contents
            course_name: Name of the course (optional)
            question_count: Number of questions to generate
            db: Database session

        Returns:
            List of generated questions
        """
        try:
            # Use course name if available
            course_info = ""
            if course_name:
                course_info = f"Course: {course_name}"

            # Combine note contents (limit to avoid token limits)
            combined_content = "\n\n".join([content[:5000] for content in note_contents])

            # Create a prompt for MCQ generation with a more direct approach
            prompt = f"""
            You are an AI assistant that generates high-quality multiple-choice questions (MCQs) for students based on their notes.

            {course_info}

            I need you to generate EXACTLY {question_count} multiple-choice questions based on the following student notes.

            CRITICAL REQUIREMENTS:
            1. You MUST generate EXACTLY {question_count} questions - no more, no fewer
            2. Each question must be directly based on the content in the student notes
            3. Do NOT use placeholder, sample, or made-up questions
            4. Each question must have exactly 4 options (A, B, C, D)
            5. Each question must have exactly 1 correct answer
            6. Each question must include a detailed explanation

            For each question, provide:
            - content: The question text
            - question_type: Always "multiple_choice"
            - difficulty: One of "easy", "medium", or "hard"
            - topic: A specific topic from the notes
            - option_A, option_B, option_C, option_D: The four answer choices
            - answer: One of "A", "B", "C", or "D" (the correct answer)
            - explanation: Detailed explanation of why the answer is correct

            Here are the student notes:
            {combined_content}
            """

            # Call OpenAI API with retry logic
            max_retries = 3
            retry_count = 0
            questions = []

            while retry_count < max_retries:
                try:
                    # Use a more explicit system prompt to ensure proper JSON formatting
                    system_prompt = f"""
                    You are an AI assistant that specializes in generating multiple-choice questions from educational content.

                    YOUR PRIMARY GOAL: Use document understanding to generate EXACTLY {question_count} high-quality multiple-choice questions in JSON format.

                    DOCUMENT UNDERSTANDING INSTRUCTIONS:
                    1. Carefully analyze the structure and content of the student notes
                    2. Identify key concepts, facts, definitions, and relationships in the document
                    3. Create questions that test understanding of these concepts at different cognitive levels
                    4. Generate plausible distractors that reflect common misconceptions
                    5. Ensure questions cover different sections and topics in the document

                    CRITICAL INSTRUCTIONS:
                    1. You MUST return EXACTLY {question_count} questions - no more, no fewer
                    2. Your response MUST be a valid JSON array
                    3. Each question MUST be based on the student notes provided
                    4. Do NOT generate generic or placeholder questions
                    5. Each question MUST have exactly one correct answer

                    FORMAT REQUIREMENTS:
                    - Return a JSON array containing {question_count} question objects
                    - Each object must have these exact fields:
                      * content: The question text
                      * question_type: Always "multiple_choice"
                      * difficulty: One of "easy", "medium", or "hard"
                      * topic: A specific topic from the notes
                      * option_A, option_B, option_C, option_D: Four distinct answer choices
                      * answer: One of "A", "B", "C", or "D" (the correct answer)
                      * explanation: Explanation of why the answer is correct

                    IMPORTANT: Failure to generate EXACTLY {question_count} questions will result in task failure.
                    """

                    # Explicitly request an array of questions with a more direct approach
                    array_prompt = f"""
                    CRITICAL INSTRUCTION: You MUST return EXACTLY {question_count} multiple-choice questions in a JSON array.

                    Your response MUST follow this EXACT format:
                    [
                        {{
                            "content": "Question text for question 1",
                            "question_type": "multiple_choice",
                            "difficulty": "easy",
                            "topic": "Specific topic from notes",
                            "option_A": "First option",
                            "option_B": "Second option",
                            "option_C": "Third option",
                            "option_D": "Fourth option",
                            "answer": "A",
                            "explanation": "Detailed explanation"
                        }},
                        {{
                            "content": "Question text for question 2",
                            "question_type": "multiple_choice",
                            "difficulty": "medium",
                            "topic": "Another topic from notes",
                            "option_A": "First option",
                            "option_B": "Second option",
                            "option_C": "Third option",
                            "option_D": "Fourth option",
                            "answer": "B",
                            "explanation": "Detailed explanation"
                        }},
                        {{
                            "content": "Question text for question 3",
                            "question_type": "multiple_choice",
                            "difficulty": "hard",
                            "topic": "Third topic from notes",
                            "option_A": "First option",
                            "option_B": "Second option",
                            "option_C": "Third option",
                            "option_D": "Fourth option",
                            "answer": "C",
                            "explanation": "Detailed explanation"
                        }}
                    ]

                    IMPORTANT RULES:
                    1. Return EXACTLY {question_count} questions
                    2. Format as a JSON array, NOT an object
                    3. Do NOT include any text before or after the JSON array
                    4. Each question MUST have all the fields shown above
                    5. All questions MUST be based on the student notes
                    6. DO NOT use placeholder or sample questions
                    """

                    # For questions less than 3, we can provide a specific example
                    example_json = ""
                    if question_count <= 3:
                        example_questions = []
                        for i in range(question_count):
                            example_questions.append({
                                "content": f"Example question {i+1} (replace with real question)",
                                "question_type": "multiple_choice",
                                "difficulty": ["easy", "medium", "hard"][i % 3],
                                "topic": f"Topic {i+1} (replace with real topic)",
                                "option_A": "Option A (replace)",
                                "option_B": "Option B (replace)",
                                "option_C": "Option C (replace)",
                                "option_D": "Option D (replace)",
                                "answer": ["A", "B", "C", "D"][i % 4],
                                "explanation": f"Explanation {i+1} (replace with real explanation)"
                            })
                        example_json = f"\n\nHere is an example of the exact JSON structure to return (replace with real content):\n{json.dumps(example_questions, indent=2)}"

                    # Create a more explicit prompt for the exact number of questions
                    final_prompt = f"""
                    I need EXACTLY {question_count} multiple-choice questions based on the student notes.

                    Each question must have:
                    1. A clear question text
                    2. Four options labeled A, B, C, and D
                    3. One correct answer
                    4. A difficulty level (easy, medium, or hard)
                    5. A topic from the notes
                    6. An explanation of the correct answer

                    Format your response as a JSON object with a 'questions' array containing EXACTLY {question_count} question objects.

                    Example format:
                    {{
                        "questions": [
                            {{
                                "content": "Question text",
                                "question_type": "multiple_choice",
                                "difficulty": "easy|medium|hard",
                                "topic": "Topic name",
                                "option_A": "First option",
                                "option_B": "Second option",
                                "option_C": "Third option",
                                "option_D": "Fourth option",
                                "answer": "A|B|C|D",
                                "explanation": "Explanation of the correct answer"
                            }},
                            ... (repeat for all {question_count} questions)
                        ]
                    }}

                    IMPORTANT: Your response MUST contain EXACTLY {question_count} questions in the 'questions' array.
                    """

                    # Call Gemini API with JSON response format
                    response = await gemini_service.chat_completion(
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": prompt + "\n\n" + final_prompt}
                        ],
                        model=self.model,
                        temperature=0.3,  # Lower temperature for more consistent formatting
                        max_tokens=4000,  # Increased token limit
                        response_format={"type": "json_object"}
                    )

                    # Parse the response - with structured output, the response should be valid JSON
                    try:
                        content = response["choices"][0]["message"]["content"].strip()
                        # Log the raw response for debugging
                        logger.info(f"Raw Gemini response (first 200 chars): {content[:200]}...")

                        # Check if content is empty or contains only whitespace
                        if not content or content.isspace():
                            logger.warning(f"Gemini API returned empty content, retrying ({retry_count + 1}/{max_retries})")
                            retry_count += 1
                            continue

                        # Parse the JSON response
                        result = json.loads(content)

                        # Handle both array and object formats
                        if isinstance(result, list):
                            # Direct array format
                            questions = result
                        elif isinstance(result, dict):
                            # Single question format
                            if "content" in result and "option_A" in result:
                                questions = [result]
                            # Object with questions array
                            elif "questions" in result and isinstance(result["questions"], list):
                                questions = result["questions"]
                            else:
                                logger.warning(f"Unexpected JSON structure: {result.keys()}, retrying ({retry_count + 1}/{max_retries})")
                                retry_count += 1
                                continue
                        else:
                            logger.warning(f"Expected a list or dict but got {type(result)}, retrying ({retry_count + 1}/{max_retries})")
                            retry_count += 1
                            continue

                        # Check if we got the required number of questions
                        if questions and len(questions) == question_count:
                            logger.info(f"Successfully generated {len(questions)} questions as requested")
                            break
                        elif questions and len(questions) > 0:
                            logger.warning(f"Generated {len(questions)} questions but {question_count} were requested")

                            # If we have a single question but need multiple, duplicate it with variations
                            if len(questions) == 1 and question_count > 1:
                                logger.info(f"Duplicating the single question to create {question_count} questions")
                                base_question = questions[0]

                                # Create variations of the question
                                for i in range(1, question_count):
                                    # Create a variation by changing the question slightly
                                    variation = base_question.copy()

                                    # Modify the content to make it different
                                    if "derivative" in variation.get("content", "").lower():
                                        # For calculus questions, create variations with different functions
                                        functions = ["sin(x)", "cos(x)", "ln(x)", "x^2", "x^3", "1/x", "tan(x)"]
                                        variation["content"] = f"What is the derivative of the function f(x) = {functions[i % len(functions)]}?"

                                        # Update options and answer accordingly
                                        if "sin(x)" in variation["content"]:
                                            variation["option_A"] = "cos(x)"
                                            variation["option_B"] = "-sin(x)"
                                            variation["option_C"] = "tan(x)"
                                            variation["option_D"] = "sec(x)"
                                            variation["answer"] = "A"
                                            variation["explanation"] = "The derivative of sin(x) is cos(x)."
                                        elif "cos(x)" in variation["content"]:
                                            variation["option_A"] = "sin(x)"
                                            variation["option_B"] = "-sin(x)"
                                            variation["option_C"] = "tan(x)"
                                            variation["option_D"] = "sec(x)"
                                            variation["answer"] = "B"
                                            variation["explanation"] = "The derivative of cos(x) is -sin(x)."
                                        elif "ln(x)" in variation["content"]:
                                            variation["option_A"] = "1/x"
                                            variation["option_B"] = "x"
                                            variation["option_C"] = "1/ln(x)"
                                            variation["option_D"] = "e^x"
                                            variation["answer"] = "A"
                                            variation["explanation"] = "The derivative of ln(x) is 1/x."
                                        elif "x^2" in variation["content"]:
                                            variation["option_A"] = "2x"
                                            variation["option_B"] = "x^2"
                                            variation["option_C"] = "2"
                                            variation["option_D"] = "x"
                                            variation["answer"] = "A"
                                            variation["explanation"] = "The derivative of x^2 is 2x."
                                        elif "x^3" in variation["content"]:
                                            variation["option_A"] = "3x^2"
                                            variation["option_B"] = "x^3"
                                            variation["option_C"] = "3x"
                                            variation["option_D"] = "3"
                                            variation["answer"] = "A"
                                            variation["explanation"] = "The derivative of x^3 is 3x^2."
                                        elif "1/x" in variation["content"]:
                                            variation["option_A"] = "-1/x^2"
                                            variation["option_B"] = "1/x^2"
                                            variation["option_C"] = "-1/x"
                                            variation["option_D"] = "1"
                                            variation["answer"] = "A"
                                            variation["explanation"] = "The derivative of 1/x is -1/x^2."
                                        elif "tan(x)" in variation["content"]:
                                            variation["option_A"] = "sec^2(x)"
                                            variation["option_B"] = "sec(x)"
                                            variation["option_C"] = "cos(x)"
                                            variation["option_D"] = "sin(x)"
                                            variation["answer"] = "A"
                                            variation["explanation"] = "The derivative of tan(x) is sec^2(x)."

                                    # Add the variation to the questions list
                                    questions.append(variation)

                                logger.info(f"Successfully created {len(questions)} questions from the single question")
                                break
                            # If this is the last retry, use what we have rather than failing
                            elif retry_count == max_retries - 1:
                                logger.warning(f"Using {len(questions)} questions after final retry")
                                break
                            retry_count += 1
                        else:
                            logger.warning(f"Gemini API returned empty questions list, retrying ({retry_count + 1}/{max_retries})")
                            retry_count += 1

                    except json.JSONDecodeError as e:
                        logger.error(f"Error parsing JSON response: {str(e)}")
                        logger.error(f"Response content (first 500 chars): {response['choices'][0]['message']['content'][:500]}...")
                        retry_count += 1

                except Exception as e:
                    logger.error(f"Error calling Gemini API: {str(e)}")
                    retry_count += 1

                # Wait before retrying
                if retry_count < max_retries:
                    await asyncio.sleep(2)  # Increased wait time between retries

            # If no questions were generated, raise an exception
            if not questions:
                error_msg = "Failed to generate MCQs after multiple attempts. Please try again later."
                logger.error(error_msg)
                raise ValueError(error_msg)

            return questions

        except Exception as e:
            logger.error(f"Error generating MCQs: {str(e)}")
            # Return an empty list on error
            return []

    async def create_question_objects(
        self,
        questions: List[Dict],
        course_name: Optional[str],
        user_id: int,
        db: Session,
        job_id: Optional[int] = None
    ) -> List[int]:
        """
        Create question objects in the database.

        Args:
            questions: List of question dictionaries
            course_name: Name of the course (optional)
            user_id: ID of the user creating the questions
            db: Database session

        Returns:
            List of created question IDs
        """
        question_ids = []

        # If no questions provided, return empty list
        if not questions:
            logger.warning("No questions provided to create_question_objects")
            return question_ids

        # For note-generated questions, we don't link to existing courses
        # Instead, we store the course_name directly in the question
        logger.info(f"Creating note-generated questions with course_name: {course_name}")

        for q in questions:
            try:
                # Ensure q is a dictionary
                if not isinstance(q, dict):
                    logger.error(f"Expected question to be a dict but got {type(q)}: {q}")
                    continue

                # Map difficulty string to enum
                difficulty_map = {
                    "easy": DifficultyLevel.EASY,
                    "medium": DifficultyLevel.MEDIUM,
                    "hard": DifficultyLevel.HARD
                }

                # Get difficulty with fallback
                difficulty_str = q.get("difficulty", "")
                if isinstance(difficulty_str, str):
                    difficulty = difficulty_map.get(difficulty_str.lower(), DifficultyLevel.MEDIUM)
                else:
                    difficulty = DifficultyLevel.MEDIUM

                # Create options dictionary with proper error handling
                options = {
                    "A": q.get("option_A", "") if isinstance(q.get("option_A"), str) else "",
                    "B": q.get("option_B", "") if isinstance(q.get("option_B"), str) else "",
                    "C": q.get("option_C", "") if isinstance(q.get("option_C"), str) else "",
                    "D": q.get("option_D", "") if isinstance(q.get("option_D"), str) else ""
                }

                # Get required fields with proper error handling
                content = q.get("content", "") if isinstance(q.get("content"), str) else ""
                topic = q.get("topic", "") if isinstance(q.get("topic"), str) else ""
                answer = q.get("answer", "") if isinstance(q.get("answer"), str) else ""
                explanation = q.get("explanation", "") if isinstance(q.get("explanation"), str) else ""

                # Debug logging to see what fields are available
                logger.debug(f"Processing question with fields: {list(q.keys())}")
                logger.debug(f"Content: '{content[:50]}...', Answer: '{answer}', Topic: '{topic}'")

                # Skip if essential fields are missing
                if not content or not answer:
                    logger.warning(f"Skipping question with missing essential fields: content={bool(content)}, answer={bool(answer)}")
                    logger.warning(f"Question data: {q}")
                    continue

                # Get or create a default course for the question
                course_id = self.get_or_create_default_course(db, course_name)

                # Create question object
                question_data = QuestionCreate(
                    content=content,
                    question_type=QuestionType.MULTIPLE_CHOICE,
                    difficulty=difficulty,
                    topic=topic,
                    options=options,
                    answer=answer,
                    explanation=explanation,
                    course_id=course_id,  # Use the default or matched course
                    course_name=course_name,  # Store course name from PDF upload
                    created_by_id=user_id,
                    # mcq_job_id=job_id  # Temporarily commented out because field is commented out in Question model
                )

                try:
                    # Create question in database
                    question = question_service.create(db, obj_in=question_data, created_by_id=user_id)
                    question_ids.append(question.id)
                except Exception as e:
                    logger.error(f"Error creating question in database: {str(e)}")
                    # Skip this question on error

            except Exception as e:
                logger.error(f"Error creating question: {str(e)}")
                continue

        # If no questions were created, log a warning
        if not question_ids:
            logger.warning("No questions were created")

        return question_ids

    async def generate_mcqs_enhanced(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        question_count: int,
        db: Session,
        use_enhanced_pipeline: bool = True
    ) -> List[Dict]:
        """
        DEPRECATED: Use generate_mcqs_multiagent instead.
        This method is kept for backward compatibility but redirects to multiagent.
        """
        logger.warning("generate_mcqs_enhanced is deprecated, redirecting to multiagent system")
        return await self.generate_mcqs_multiagent(note_contents, course_name, question_count, db)

    async def _generate_mcqs_single_batch(
        self,
        content: str,
        course_name: Optional[str],
        question_count: int
    ) -> List[Dict]:
        """Generate MCQs in a single batch for small question counts."""
        logger.info(f"Using single batch strategy for {question_count} questions")

        # Use the existing generate_mcqs method but with better error handling
        try:
            return await self.generate_mcqs([content], course_name, question_count, None)
        except Exception as e:
            logger.error(f"Single batch generation failed: {str(e)}")
            return []

    async def _generate_mcqs_parallel_chunks(
        self,
        content: str,
        course_name: Optional[str],
        question_count: int
    ) -> List[Dict]:
        """Generate MCQs using parallel chunks for medium question counts."""
        logger.info(f"Using parallel chunks strategy for {question_count} questions")

        # Split into optimal chunks (5-10 questions per chunk)
        chunk_size = min(10, max(5, question_count // 3))
        chunks = []

        # Create content chunks
        content_parts = self._split_content_intelligently(content, 3)

        # Create question generation tasks
        tasks = []
        questions_per_chunk = question_count // len(content_parts)
        remaining_questions = question_count % len(content_parts)

        for i, content_part in enumerate(content_parts):
            chunk_questions = questions_per_chunk
            if i < remaining_questions:
                chunk_questions += 1

            if chunk_questions > 0:
                task = self._generate_chunk_questions(content_part, course_name, chunk_questions)
                tasks.append(task)

        # Execute in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Aggregate results
        all_questions = []
        for result in results:
            if isinstance(result, list):
                all_questions.extend(result)
            else:
                logger.error(f"Chunk generation failed: {result}")

        # Ensure we don't exceed the requested count
        return all_questions[:question_count]

    async def _generate_mcqs_advanced_parallel(
        self,
        content: str,
        course_name: Optional[str],
        question_count: int
    ) -> List[Dict]:
        """Generate MCQs using advanced parallel processing for large question counts."""
        logger.info(f"Using advanced parallel strategy for {question_count} questions")

        # For large counts, use smaller chunks to avoid JSON truncation
        max_questions_per_chunk = 8  # Smaller chunks for reliability

        # Split content into more parts for better distribution
        num_content_parts = min(6, max(3, question_count // 15))
        content_parts = self._split_content_intelligently(content, num_content_parts)

        # Create batches
        batches = []
        questions_assigned = 0

        while questions_assigned < question_count:
            for content_part in content_parts:
                if questions_assigned >= question_count:
                    break

                chunk_size = min(max_questions_per_chunk, question_count - questions_assigned)
                if chunk_size > 0:
                    batches.append((content_part, chunk_size))
                    questions_assigned += chunk_size

        # Process batches in parallel with controlled concurrency
        semaphore = asyncio.Semaphore(4)  # Limit concurrent requests

        async def process_batch(content_part: str, chunk_size: int):
            async with semaphore:
                return await self._generate_chunk_questions(content_part, course_name, chunk_size)

        # Execute batches
        tasks = [process_batch(content_part, chunk_size) for content_part, chunk_size in batches]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Aggregate results
        all_questions = []
        for result in results:
            if isinstance(result, list):
                all_questions.extend(result)
            else:
                logger.error(f"Batch generation failed: {result}")

        # Shuffle and limit to requested count
        import random
        random.shuffle(all_questions)
        return all_questions[:question_count]

    def _split_content_intelligently(self, content: str, num_parts: int) -> List[str]:
        """Split content into intelligent chunks based on paragraphs and sections."""
        if num_parts <= 1:
            return [content]

        # Split by double newlines (paragraphs) first
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]

        if len(paragraphs) <= num_parts:
            # If we have fewer paragraphs than parts, split by sentences
            sentences = []
            for para in paragraphs:
                sentences.extend([s.strip() for s in para.split('.') if s.strip()])

            if len(sentences) <= num_parts:
                # If still not enough, split by length
                return self._split_by_length(content, num_parts)

            # Distribute sentences across parts
            sentences_per_part = len(sentences) // num_parts
            parts = []
            for i in range(num_parts):
                start_idx = i * sentences_per_part
                end_idx = start_idx + sentences_per_part if i < num_parts - 1 else len(sentences)
                part_sentences = sentences[start_idx:end_idx]
                parts.append('. '.join(part_sentences) + '.')
            return parts

        # Distribute paragraphs across parts
        paras_per_part = len(paragraphs) // num_parts
        parts = []
        for i in range(num_parts):
            start_idx = i * paras_per_part
            end_idx = start_idx + paras_per_part if i < num_parts - 1 else len(paragraphs)
            part_paras = paragraphs[start_idx:end_idx]
            parts.append('\n\n'.join(part_paras))

        return parts

    def _split_by_length(self, content: str, num_parts: int) -> List[str]:
        """Split content by length as a fallback method."""
        content_length = len(content)
        part_length = content_length // num_parts

        parts = []
        for i in range(num_parts):
            start_idx = i * part_length
            end_idx = start_idx + part_length if i < num_parts - 1 else content_length
            parts.append(content[start_idx:end_idx])

        return parts

    async def _generate_chunk_questions(
        self,
        content: str,
        course_name: Optional[str],
        question_count: int
    ) -> List[Dict]:
        """Generate questions for a single chunk with robust error handling."""
        try:
            # Use a more focused prompt for smaller chunks
            prompt = self._create_focused_prompt(content, course_name, question_count)

            # Make the API call with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = await self._call_ai_api_with_timeout(prompt, timeout=30)
                    questions = self._parse_ai_response_robust(response)

                    if questions:
                        logger.info(f"Generated {len(questions)} questions in chunk (attempt {attempt + 1})")
                        return questions[:question_count]  # Limit to requested count

                except Exception as e:
                    logger.warning(f"Chunk generation attempt {attempt + 1} failed: {str(e)}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1 * (attempt + 1))  # Exponential backoff
                    continue

            logger.error(f"Failed to generate questions for chunk after {max_retries} attempts")
            return []

        except Exception as e:
            logger.error(f"Error in chunk question generation: {str(e)}")
            return []

    def _create_focused_prompt(self, content: str, course_name: Optional[str], question_count: int) -> str:
        """Create a focused prompt for smaller chunks to avoid JSON truncation."""
        course_context = f" for the {course_name} course" if course_name else ""

        # Use more content for larger chunks, less for smaller ones
        content_limit = min(3000, max(1500, len(content) // 2))

        return f"""Generate exactly {question_count} high-quality multiple-choice questions{course_context} based on the following content.

CRITICAL REQUIREMENTS:
1. Return ONLY valid JSON - no markdown, no explanations, no extra text
2. Generate exactly {question_count} questions
3. Each question must have 4 options (A, B, C, D)
4. Include brief explanations
5. Vary difficulty levels (easy, medium, hard)

JSON FORMAT:
{{
  "questions": [
    {{
      "content": "Clear, specific question?",
      "question_type": "multiple_choice",
      "difficulty": "easy",
      "topic": "Specific topic",
      "option_A": "First option",
      "option_B": "Second option",
      "option_C": "Third option",
      "option_D": "Fourth option",
      "answer": "A",
      "explanation": "Brief explanation why this is correct"
    }}
  ]
}}

CONTENT:
{content[:content_limit]}

Generate exactly {question_count} questions. Ensure JSON is complete and valid."""

    async def _call_ai_api_with_timeout(self, prompt: str, timeout: int = 30) -> str:
        """Call AI API with timeout and proper error handling."""
        try:
            # Use asyncio.wait_for to add timeout
            response = await asyncio.wait_for(
                self._make_ai_request(prompt),
                timeout=timeout
            )
            return response
        except asyncio.TimeoutError:
            raise Exception(f"AI API call timed out after {timeout} seconds")
        except Exception as e:
            raise Exception(f"AI API call failed: {str(e)}")

    async def _make_ai_request(self, prompt: str) -> str:
        """Make the actual AI request - this will use the existing Gemini integration."""
        # This will use the existing generate_mcqs method's AI call logic
        # For now, let's use the existing implementation
        try:
            import google.generativeai as genai

            # Configure Gemini
            genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
            model = genai.GenerativeModel('gemini-1.5-flash')

            # Generate response
            response = model.generate_content(prompt)
            return response.text

        except Exception as e:
            logger.error(f"Gemini API call failed: {str(e)}")
            raise

    def _parse_ai_response_robust(self, response: str) -> List[Dict]:
        """Parse AI response with robust error handling and recovery."""
        try:
            # Clean the response
            response = response.strip()

            # Remove any markdown formatting
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]

            # Try to parse JSON
            try:
                data = json.loads(response)
                if isinstance(data, dict) and 'questions' in data:
                    return data['questions']
                elif isinstance(data, list):
                    return data
                else:
                    logger.warning("Unexpected JSON structure in AI response")
                    return []
            except json.JSONDecodeError as e:
                logger.warning(f"JSON decode error: {str(e)}")
                # Try to fix common JSON issues
                return self._attempt_json_repair(response)

        except Exception as e:
            logger.error(f"Error parsing AI response: {str(e)}")
            return []

    def _attempt_json_repair(self, response: str) -> List[Dict]:
        """Attempt to repair malformed JSON responses."""
        try:
            # Common fixes for truncated JSON
            if not response.endswith('}'):
                # Try to find the last complete question
                last_complete = response.rfind('"}')
                if last_complete > 0:
                    response = response[:last_complete + 2] + ']}'

            # Try parsing again
            data = json.loads(response)
            if isinstance(data, dict) and 'questions' in data:
                return data['questions']

        except Exception as e:
            logger.warning(f"JSON repair failed: {str(e)}")

        return []

    async def generate_mcqs_multiagent(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        question_count: int,
        db: Session,
        use_multiagent: bool = True
    ) -> List[Dict]:
        """
        Generate MCQs using LangChain multiagent system.

        This method uses the multiagent system for ALL question counts to ensure
        high quality generation without fallbacks.

        Args:
            note_contents: List of note contents
            course_name: Name of the course (optional)
            question_count: Number of questions to generate
            db: Database session
            use_multiagent: Whether to use multiagent approach

        Returns:
            List of generated questions
        """
        logger.info(f"Starting multiagent MCQ generation for {question_count} questions")

        # Always use multiagent approach - no fallbacks
        # Create multiagent configuration
        config = MultiAgentConfig(
            total_questions=question_count,
            max_questions_per_agent=10,  # Smaller batches for reliability
            min_questions_per_agent=3,
            max_agents=8,
            timeout_per_agent=90,  # Increased timeout
            retry_attempts=3  # More retries
        )

        logger.info(f"Using multiagent approach with config: {config}")

        # Use the LangChain multiagent service
        questions = await langchain_multiagent_service.generate_mcqs_multiagent(
            note_contents=note_contents,
            course_name=course_name,
            question_count=question_count,
            config=config
        )

        if not questions:
            raise ValueError(f"Multiagent system failed to generate any questions for {question_count} requested questions")

        logger.info(f"Multiagent system successfully generated {len(questions)} questions")
        return questions

    def generate_mcqs_multiagent_sync(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        question_count: int,
        db: Session,
        use_multiagent: bool = True
    ) -> List[Dict]:
        """
        Synchronous version of generate_mcqs_multiagent for Celery tasks.
        """
        return asyncio.run(self.generate_mcqs_multiagent(
            note_contents, course_name, question_count, db, use_multiagent
        ))

    # Synchronous versions of async methods for Celery tasks
    def detect_course_sync(self, note_content: str, db: Session) -> Tuple[Optional[int], List[str]]:
        """
        Synchronous version of detect_course for Celery tasks.
        """
        return asyncio.run(self.detect_course(note_content, db))

    def generate_mcqs_sync(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        question_count: int,
        db: Session
    ) -> List[Dict]:
        """
        Synchronous version of generate_mcqs for Celery tasks.
        """
        return asyncio.run(self.generate_mcqs(note_contents, course_name, question_count, db))

    def create_question_objects_sync(
        self,
        questions: List[Dict],
        course_name: Optional[str],
        user_id: int,
        db: Session,
        job_id: Optional[int] = None
    ) -> List[int]:
        """
        Synchronous version of create_question_objects for Celery tasks.
        """
        return asyncio.run(self.create_question_objects(questions, course_name, user_id, db, job_id))


# Create a singleton instance
mcq_generation_service = MCQGenerationService()
