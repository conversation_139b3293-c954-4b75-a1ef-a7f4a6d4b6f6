import logging
import os
from typing import List, Dict, Any, Optional

import openai
from openai import OpenAI

from app.core.config import settings

logger = logging.getLogger(__name__)

class OpenAIService:
    """Service for interacting with OpenAI API."""

    def __init__(self):
        """Initialize the OpenAI service."""
        self.api_key = settings.OPENAI_API_KEY
        self.client = OpenAI(api_key=self.api_key)

    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> Any:
        """
        Generate a chat completion using OpenAI API.

        Args:
            messages: List of messages in the conversation
            model: OpenAI model to use
            temperature: Sampling temperature
            max_tokens: Maximum number of tokens to generate

        Returns:
            OpenAI API response
        """
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {str(e)}")
            raise

    async def generate_embeddings(self, text: str) -> List[float]:
        """
        Generate embeddings for a text using OpenAI API.

        Args:
            text: Text to generate embeddings for

        Returns:
            List of embedding values
        """
        try:
            response = self.client.embeddings.create(
                model="text-embedding-ada-002",
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise


# Create a singleton instance
openai_service = OpenAIService()
