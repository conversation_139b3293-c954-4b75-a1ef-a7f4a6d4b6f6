from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Any, <PERSON><PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, text

from app.models.user import User
from app.models.gamification import (
    Badge, UserBadge, PointsTransaction, UserLevel, UserStreak,
    BadgeType
)
from app.models.student_progress import StudentExam, StudentQuestionAttempt
from app.schemas.gamification import (
    BadgeCreate, UserBadgeCreate, PointsTransactionCreate,
    UserLevelCreate, UserStreakCreate
)

# Constants for point values
POINTS = {
    "EXAM_COMPLETION": 50,
    "PRACTICE_COMPLETION": 20,
    "CORRECT_ANSWER": 5,
    "PERFECT_SCORE": 100,
    "STREAK_DAY": 10,
    "PROFILE_COMPLETION": 30,
    "COURSE_ENROLLMENT": 15,
    "BADGE_EARNED": 25,
}

# Level definitions
LEVELS = [
    {"level_number": 1, "name": "<PERSON><PERSON>", "min_points": 0, "max_points": 99, "icon_url": "/icons/level1.png"},
    {"level_number": 2, "name": "Beginner", "min_points": 100, "max_points": 299, "icon_url": "/icons/level2.png"},
    {"level_number": 3, "name": "Apprentice", "min_points": 300, "max_points": 599, "icon_url": "/icons/level3.png"},
    {"level_number": 4, "name": "Scholar", "min_points": 600, "max_points": 999, "icon_url": "/icons/level4.png"},
    {"level_number": 5, "name": "Expert", "min_points": 1000, "max_points": 1499, "icon_url": "/icons/level5.png"},
    {"level_number": 6, "name": "Master", "min_points": 1500, "max_points": 2499, "icon_url": "/icons/level6.png"},
    {"level_number": 7, "name": "Grandmaster", "min_points": 2500, "max_points": 3999, "icon_url": "/icons/level7.png"},
    {"level_number": 8, "name": "Legend", "min_points": 4000, "max_points": 9999999, "icon_url": "/icons/level8.png"},
]

# Badge definitions
DEFAULT_BADGES = [
    {
        "name": "First Perfect Score",
        "description": "Achieved a perfect score on an exam",
        "badge_type": BadgeType.FIRST_PERFECT_SCORE,
        "icon_url": "/icons/badges/perfect_score.png",
        "points_value": 50
    },
    {
        "name": "Practice Master",
        "description": "Completed 10 practice sessions",
        "badge_type": BadgeType.PRACTICE_MASTER,
        "icon_url": "/icons/badges/practice_master.png",
        "points_value": 30
    },
    {
        "name": "Streak Champion",
        "description": "Maintained a 7-day learning streak",
        "badge_type": BadgeType.STREAK,
        "icon_url": "/icons/badges/streak.png",
        "points_value": 40
    },
    {
        "name": "Course Completion",
        "description": "Completed all questions in a course",
        "badge_type": BadgeType.COURSE_COMPLETION,
        "icon_url": "/icons/badges/course_completion.png",
        "points_value": 75
    },
    {
        "name": "Quick Learner",
        "description": "Answered 20 questions correctly in a row",
        "badge_type": BadgeType.QUICK_LEARNER,
        "icon_url": "/icons/badges/quick_learner.png",
        "points_value": 35
    },
    {
        "name": "Consistent Learner",
        "description": "Studied for 5 consecutive days",
        "badge_type": BadgeType.CONSISTENT_LEARNER,
        "icon_url": "/icons/badges/consistent.png",
        "points_value": 25
    }
]


def get_level_for_points(points: int) -> Dict[str, Any]:
    """Get the level information for a given point total"""
    for level in LEVELS:
        if level["min_points"] <= points <= level["max_points"]:
            return level
    # Default to highest level if points exceed all defined levels
    return LEVELS[-1]


def initialize_gamification_data(db: Session) -> None:
    """Initialize default badges and levels in the database"""
    try:
        # Create default levels if they don't exist
        existing_levels = db.execute(text("SELECT COUNT(*) FROM userlevel")).scalar() or 0
        if existing_levels == 0:
            for level_data in LEVELS:
                # Insert level using raw SQL
                db.execute(
                    text("""
                        INSERT INTO userlevel
                        (level_number, name, min_points, max_points, icon_url)
                        VALUES (:level_number, :name, :min_points, :max_points, :icon_url)
                    """),
                    {
                        "level_number": level_data["level_number"],
                        "name": level_data["name"],
                        "min_points": level_data["min_points"],
                        "max_points": level_data["max_points"],
                        "icon_url": level_data["icon_url"]
                    }
                )
            db.commit()
            print("Default levels created successfully")

        # Create default badges if they don't exist
        existing_badges = db.execute(text("SELECT COUNT(*) FROM badge")).scalar() or 0
        if existing_badges == 0:
            for badge_data in DEFAULT_BADGES:
                # Insert badge using raw SQL
                db.execute(
                    text("""
                        INSERT INTO badge
                        (name, description, badge_type, icon_url, points_value)
                        VALUES (:name, :description, :badge_type, :icon_url, :points_value)
                    """),
                    {
                        "name": badge_data["name"],
                        "description": badge_data["description"],
                        "badge_type": badge_data["badge_type"],
                        "icon_url": badge_data["icon_url"],
                        "points_value": badge_data["points_value"]
                    }
                )
            db.commit()
            print("Default badges created successfully")
    except Exception as e:
        print(f"Error initializing gamification data: {e}")
        db.rollback()


def award_points(
    db: Session,
    user_id: int,
    points: int,
    description: str,
    transaction_type: str,
    reference_id: Optional[int] = None
) -> PointsTransaction:
    """Award points to a user and create a transaction record"""
    transaction = PointsTransaction(
        user_id=user_id,
        points=points,
        description=description,
        transaction_type=transaction_type,
        reference_id=reference_id
    )
    db.add(transaction)
    db.commit()
    db.refresh(transaction)

    # Check for level-up achievements
    check_for_level_achievements(db, user_id)

    return transaction


def award_badge(
    db: Session,
    user_id: int,
    badge_id: int
) -> Optional[UserBadge]:
    """Award a badge to a user if they don't already have it"""
    # Check if user already has this badge
    existing_badge = db.query(UserBadge).filter(
        UserBadge.user_id == user_id,
        UserBadge.badge_id == badge_id
    ).first()

    if existing_badge:
        return None

    # Get the badge to award points
    badge = db.query(Badge).filter(Badge.id == badge_id).first()
    if not badge:
        return None

    # Create user badge
    user_badge = UserBadge(
        user_id=user_id,
        badge_id=badge_id
    )
    db.add(user_badge)

    # Award points for earning the badge
    award_points(
        db=db,
        user_id=user_id,
        points=badge.points_value,
        description=f"Earned the '{badge.name}' badge",
        transaction_type="badge_earned",
        reference_id=badge_id
    )

    db.commit()
    db.refresh(user_badge)
    return user_badge


def update_streak(db: Session, user_id: int) -> UserStreak:
    """Update a user's learning streak"""
    today = datetime.now(timezone.utc).date()

    # Get or create user streak
    user_streak = db.query(UserStreak).filter(UserStreak.user_id == user_id).first()
    if not user_streak:
        user_streak = UserStreak(
            user_id=user_id,
            current_streak=1,
            longest_streak=1,
            last_activity_date=datetime.now(timezone.utc)
        )
        db.add(user_streak)

        # Award points for first day streak
        award_points(
            db=db,
            user_id=user_id,
            points=POINTS["STREAK_DAY"],
            description="Started a learning streak",
            transaction_type="streak_day"
        )
    else:
        last_activity = user_streak.last_activity_date.date()

        # If last activity was yesterday, increment streak
        if (today - last_activity).days == 1:
            user_streak.current_streak += 1
            user_streak.last_activity_date = datetime.now(timezone.utc)

            # Update longest streak if current is longer
            if user_streak.current_streak > user_streak.longest_streak:
                user_streak.longest_streak = user_streak.current_streak

            # Award points for continuing streak
            award_points(
                db=db,
                user_id=user_id,
                points=POINTS["STREAK_DAY"],
                description=f"Continued learning streak: {user_streak.current_streak} days",
                transaction_type="streak_day"
            )

            # Check for streak badges
            check_for_streak_achievements(db, user_id, user_streak.current_streak)

        # If last activity was today, just update the timestamp
        elif (today - last_activity).days == 0:
            user_streak.last_activity_date = datetime.now(timezone.utc)

        # If more than 1 day has passed, reset streak
        else:
            user_streak.current_streak = 1
            user_streak.last_activity_date = datetime.now(timezone.utc)

            # Award points for new streak
            award_points(
                db=db,
                user_id=user_id,
                points=POINTS["STREAK_DAY"],
                description="Started a new learning streak",
                transaction_type="streak_day"
            )

    db.commit()
    db.refresh(user_streak)
    return user_streak


def check_for_streak_achievements(db: Session, user_id: int, streak_days: int) -> None:
    """Check and award streak-related achievements"""
    if streak_days >= 7:
        # Award 7-day streak badge
        streak_badge = db.query(Badge).filter(Badge.badge_type == BadgeType.STREAK).first()
        if streak_badge:
            award_badge(db, user_id, streak_badge.id)

    if streak_days >= 5:
        # Award consistent learner badge
        consistent_badge = db.query(Badge).filter(Badge.badge_type == BadgeType.CONSISTENT_LEARNER).first()
        if consistent_badge:
            award_badge(db, user_id, consistent_badge.id)


def check_for_level_achievements(db: Session, user_id: int) -> None:
    """Check for level-up achievements"""
    # This could be expanded with level-specific badges
    pass


def check_for_exam_achievements(db: Session, user_id: int, exam_id: int) -> None:
    """Check and award exam-related achievements"""
    exam = db.query(StudentExam).filter(StudentExam.id == exam_id).first()
    if not exam:
        return

    # Check for perfect score
    if exam.score == 100.0:
        perfect_score_badge = db.query(Badge).filter(
            Badge.badge_type == BadgeType.FIRST_PERFECT_SCORE
        ).first()
        if perfect_score_badge:
            award_badge(db, user_id, perfect_score_badge.id)

    # Count total exams for this user
    exam_count = db.query(StudentExam).filter(
        StudentExam.student_id == user_id
    ).count()

    # Could add more achievements based on exam count


def check_for_practice_achievements(db: Session, user_id: int) -> None:
    """Check and award practice-related achievements"""
    # Count practice attempts
    practice_count = db.query(StudentQuestionAttempt).filter(
        StudentQuestionAttempt.student_id == user_id
    ).count()

    if practice_count >= 100:
        practice_master_badge = db.query(Badge).filter(
            Badge.badge_type == BadgeType.PRACTICE_MASTER
        ).first()
        if practice_master_badge:
            award_badge(db, user_id, practice_master_badge.id)

    # Check for consecutive correct answers
    consecutive_correct = db.query(
        func.count(StudentQuestionAttempt.id)
    ).filter(
        StudentQuestionAttempt.student_id == user_id,
        StudentQuestionAttempt.is_correct == True
    ).order_by(
        StudentQuestionAttempt.attempt_time.desc()
    ).limit(20).scalar()

    if consecutive_correct >= 20:
        quick_learner_badge = db.query(Badge).filter(
            Badge.badge_type == BadgeType.QUICK_LEARNER
        ).first()
        if quick_learner_badge:
            award_badge(db, user_id, quick_learner_badge.id)


def get_user_badges(db: Session, user_id: int) -> List[Dict[str, Any]]:
    """Get all badges earned by a user with badge details"""
    user_badges = db.query(UserBadge, Badge).join(
        Badge, UserBadge.badge_id == Badge.id
    ).filter(
        UserBadge.user_id == user_id
    ).all()

    return [
        {
            "id": badge.id,
            "name": badge.name,
            "description": badge.description,
            "badge_type": badge.badge_type,
            "icon_url": badge.icon_url,
            "earned_at": user_badge.earned_at
        }
        for user_badge, badge in user_badges
    ]


def get_user_points(db: Session, user_id: int) -> int:
    """Get total points for a user"""
    total_points = db.query(func.sum(PointsTransaction.points)).filter(
        PointsTransaction.user_id == user_id
    ).scalar() or 0

    return total_points


def get_leaderboard(
    db: Session,
    limit: int = 10,
    school_id: Optional[int] = None,
    department_id: Optional[int] = None,
    course_id: Optional[int] = None,
    time_period: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Get top users by points for the leaderboard with optional filtering

    Args:
        db: Database session
        limit: Number of users to return
        school_id: Filter by school ID
        department_id: Filter by department ID
        course_id: Filter by course ID
        time_period: Filter by time period ('daily', 'weekly', 'all_time')
    """
    try:
        # Build query parameters
        params = {"limit": limit}

        # Use a simpler query approach to avoid circular import issues
        # First, get all students that match the filters
        student_query = """
            SELECT DISTINCT
                u.id,
                u.full_name,
                u.profile_picture_url,
                s.name as school_name,
                d.name as department_name
            FROM
                "user" u
            LEFT JOIN
                school s ON u.institution_id = s.id
            LEFT JOIN
                department d ON u.department_id = d.id
        """

        # Add course join only if course_id is provided
        if course_id is not None:
            student_query += """
            INNER JOIN
                user_course uc ON u.id = uc.user_id
            """
        else:
            student_query += """
            LEFT JOIN
                user_course uc ON u.id = uc.user_id
            """

        # Start building WHERE clause
        # Check for both uppercase and lowercase role values to be safe
        where_clauses = ["(u.role = 'STUDENT' OR u.role = 'student')"]

        # Add school filter
        if school_id is not None:
            where_clauses.append("u.institution_id = :school_id")
            params["school_id"] = school_id

        # Add department filter
        if department_id is not None:
            where_clauses.append("u.department_id = :department_id")
            params["department_id"] = department_id

        # Add course filter
        if course_id is not None:
            where_clauses.append("uc.course_id = :course_id")
            params["course_id"] = course_id

        # Combine WHERE clauses
        student_query += " WHERE " + " AND ".join(where_clauses)

        print(f"Student query: {student_query}")
        print(f"Params: {params}")

        # Get the students
        students = db.execute(text(student_query), params).fetchall()
        print(f"Found {len(students)} students")

        # For each student, get their points, badges, and streak
        result = []
        for student in students:
            # Get points with time period filter
            points_query = """
                SELECT COALESCE(SUM(points), 0) as total_points
                FROM pointstransaction
                WHERE user_id = :user_id
            """

            points_params = {"user_id": student.id}

            # Add time period filter
            if time_period:
                if time_period == 'daily':
                    points_query += " AND DATE(created_at) = CURRENT_DATE"
                elif time_period == 'weekly':
                    # Use proper date calculation for PostgreSQL
                    points_query += " AND created_at >= (CURRENT_DATE - INTERVAL '7 days')"
                # 'all_time' doesn't need a filter as it includes all points

            print(f"Points query for user {student.id}: {points_query}")
            print(f"Points params: {points_params}")

            points = db.execute(text(points_query), points_params).scalar() or 0
            print(f"User {student.id} points: {points}")

            # Get badge count
            badge_count = db.execute(text("""
                SELECT COUNT(DISTINCT badge_id) as badge_count
                FROM userbadge
                WHERE user_id = :user_id
            """), {"user_id": student.id}).scalar() or 0

            # Get streak
            streak = db.execute(text("""
                SELECT COALESCE(current_streak, 0) as current_streak
                FROM userstreak
                WHERE user_id = :user_id
            """), {"user_id": student.id}).scalar() or 0

            user_level = get_level_for_points(points)
            result.append({
                "user_id": student.id,
                "full_name": student.full_name,
                "profile_picture_url": student.profile_picture_url,
                "total_points": points,
                "badge_count": badge_count,
                "current_streak": streak,
                "level": user_level,
                "school_name": student.school_name,
                "department_name": student.department_name
            })

        # Sort by total points
        result.sort(key=lambda x: x["total_points"], reverse=True)

        # Apply limit
        result = result[:limit]

        # Log the result
        print(f"Leaderboard entries: {len(result)}")
        for entry in result:
            print(f"User: {entry['full_name']}, Points: {entry['total_points']}, Badges: {entry['badge_count']}, Streak: {entry['current_streak']}")

        return result
    except Exception as e:
        print(f"Error in get_leaderboard: {e}")
        # Return empty list in case of error
        return []
