from typing import Any, Dict, List, Optional, Union

from sqlalchemy import case, or_
from sqlalchemy.orm import Session

from app import schemas
from app.models.course import Course
from app.models.user import User


def get(db: Session, id: int) -> Optional[Course]:
    return db.query(Course).filter(Course.id == id).first()


def get_by_code(db: Session, code: str) -> Optional[Course]:
    return db.query(Course).filter(Course.code == code).first()


def get_multi(
    db: Session, *, skip: int = 0, limit: int = 100
) -> list[Course]:
    return db.query(Course).offset(skip).limit(limit).all()


def get_by_school(
    db: Session, *, school_id: int, skip: int = 0, limit: int = 100
) -> list[Course]:
    return (
        db.query(Course)
        .filter(Course.school_id == school_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def get_by_department(
    db: Session, *, department_id: int, skip: int = 0, limit: int = 100
) -> list[Course]:
    return (
        db.query(Course)
        .filter(Course.department_id == department_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def create(db: Session, *, obj_in: schemas.CourseCreate) -> Course:
    db_obj = Course(
        name=obj_in.name,
        description=obj_in.description,
        code=obj_in.code,
        is_active=obj_in.is_active,
        school_id=obj_in.school_id,
        department_id=obj_in.department_id,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(
    db: Session, *, db_obj: Course, obj_in: Union[schemas.CourseUpdate, Dict[str, Any]]
) -> Course:
    if isinstance(obj_in, dict):
        update_data = obj_in
    else:
        update_data = obj_in.model_dump(exclude_unset=True)
    for field in update_data:
        if field in update_data:
            setattr(db_obj, field, update_data[field])
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete(db: Session, *, id: int) -> Course:
    obj = db.query(Course).get(id)
    db.delete(obj)
    db.commit()
    return obj


def get_enrolled_users(db: Session, *, course_id: int) -> List[User]:
    course = get(db, id=course_id)
    if not course:
        return []
    return course.users


def get_prioritized_by_department(
    db: Session, *, user_department_id: Optional[int], skip: int = 0, limit: int = 100
) -> list[Course]:
    """
    Get all courses but prioritize those from the user's department.
    Courses from the user's department will appear first in the results.
    """
    if not user_department_id:
        return get_multi(db, skip=skip, limit=limit)

    # Use case statement to prioritize courses from user's department
    # Note: In newer SQLAlchemy versions, whens are passed as individual arguments, not as a list
    priority_case = case(
        (Course.department_id == user_department_id, 1),
        else_=2
    )

    return (
        db.query(Course)
        .order_by(priority_case, Course.name)
        .offset(skip)
        .limit(limit)
        .all()
    )


def get_prioritized_enrolled_courses(
    db: Session, *, user_id: int, skip: int = 0, limit: int = 100
) -> list[Course]:
    """
    Get all courses but prioritize those the user is enrolled in.
    Enrolled courses will appear first in the results.
    """
    from app.models.user import user_course
    from sqlalchemy import exists

    # Create a subquery to check if the user is enrolled in each course
    enrolled_subquery = exists().where(
        (user_course.c.user_id == user_id) &
        (user_course.c.course_id == Course.id)
    )

    # Use case statement to prioritize enrolled courses
    priority_case = case(
        (enrolled_subquery, 1),
        else_=2
    )

    return (
        db.query(Course)
        .order_by(priority_case, Course.name)
        .offset(skip)
        .limit(limit)
        .all()
    )


def search_courses(
    db: Session, *, search_query: str, skip: int = 0, limit: int = 100
) -> list[Course]:
    """
    Search for courses by name or code.

    Args:
        db: Database session
        search_query: Search query string
        skip: Number of records to skip
        limit: Maximum number of records to return

    Returns:
        List of courses matching the search query
    """
    # Create a search pattern with wildcards
    search_pattern = f"%{search_query}%"

    return (
        db.query(Course)
        .filter(
            or_(
                Course.name.ilike(search_pattern),
                Course.code.ilike(search_pattern),
                Course.description.ilike(search_pattern)
            )
        )
        .order_by(Course.name)
        .offset(skip)
        .limit(limit)
        .all()
    )
