import logging
import re
import math
from typing import Dict, List, Optional, Tuple, Any
# import tiktoken
# from langchain.text_splitter import RecursiveCharacterTextSplitter
# from langchain.docstore.document import Document

logger = logging.getLogger(__name__)


class ChunkingService:
    """Advanced chunking service with quota assignment and smart splitting."""
    
    def __init__(self):
        self.default_chunk_size = 1200  # Target tokens per chunk
        self.chunk_overlap = 200  # Overlap between chunks
        # self.encoding = tiktoken.get_encoding("cl100k_base")  # GPT-4 encoding
        
    def count_tokens(self, text: str) -> int:
        """Count tokens in text using rough estimation."""
        try:
            # Rough estimation: 1 token ≈ 0.75 words
            return int(len(text.split()) * 1.3)
        except Exception as e:
            logger.error(f"Error counting tokens: {str(e)}")
            # Fallback to character-based estimation
            return len(text) // 4
    
    def detect_headings(self, text: str) -> List[Dict[str, Any]]:
        """
        Detect headings and structure in text.
        
        Args:
            text: Input text
            
        Returns:
            List of heading information with positions
        """
        headings = []
        lines = text.split('\n')
        
        # Patterns for different heading types
        patterns = [
            (r'^#{1,6}\s+(.+)$', 'markdown'),  # Markdown headings
            (r'^([A-Z][A-Z\s]{2,}):?\s*$', 'all_caps'),  # ALL CAPS headings
            (r'^(\d+\.?\s+[A-Z].+)$', 'numbered'),  # Numbered headings
            (r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*):?\s*$', 'title_case'),  # Title Case
        ]
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            for pattern, heading_type in patterns:
                match = re.match(pattern, line)
                if match:
                    headings.append({
                        'text': match.group(1) if match.groups() else line,
                        'line_number': i,
                        'type': heading_type,
                        'level': self._get_heading_level(line, heading_type)
                    })
                    break
        
        return headings
    
    def _get_heading_level(self, line: str, heading_type: str) -> int:
        """Determine heading level based on type and content."""
        if heading_type == 'markdown':
            return len(re.match(r'^#+', line).group())
        elif heading_type == 'all_caps':
            return 1  # Assume top level
        elif heading_type == 'numbered':
            # Count dots to determine level (1.1.1 = level 3)
            return line.count('.') + 1
        else:
            return 2  # Default level
    
    def split_by_headings(self, text: str) -> List[Dict[str, Any]]:
        """
        Split text into sections based on headings.
        
        Args:
            text: Input text
            
        Returns:
            List of sections with metadata
        """
        headings = self.detect_headings(text)
        lines = text.split('\n')
        sections = []
        
        if not headings:
            # No headings found, return entire text as one section
            return [{
                'content': text,
                'heading': None,
                'level': 0,
                'start_line': 0,
                'end_line': len(lines) - 1
            }]
        
        # Add sections between headings
        for i, heading in enumerate(headings):
            start_line = heading['line_number']
            end_line = headings[i + 1]['line_number'] - 1 if i + 1 < len(headings) else len(lines) - 1
            
            section_lines = lines[start_line:end_line + 1]
            content = '\n'.join(section_lines).strip()
            
            if content:
                sections.append({
                    'content': content,
                    'heading': heading['text'],
                    'level': heading['level'],
                    'start_line': start_line,
                    'end_line': end_line
                })
        
        return sections
    
    def chunk_text_with_quota(
        self, 
        text: str, 
        total_questions: int,
        chunk_size: Optional[int] = None,
        preserve_headings: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Chunk text with assigned quotas for question generation.
        
        Args:
            text: Input text
            total_questions: Total number of questions to generate
            chunk_size: Target chunk size in tokens
            preserve_headings: Whether to preserve heading boundaries
            
        Returns:
            List of chunks with assigned quotas
        """
        chunk_size = chunk_size or self.default_chunk_size
        
        try:
            if preserve_headings:
                # First split by headings
                sections = self.split_by_headings(text)
                chunks = []
                
                for section in sections:
                    section_chunks = self._chunk_section(section['content'], chunk_size)
                    for chunk in section_chunks:
                        chunk['section_heading'] = section['heading']
                        chunk['section_level'] = section['level']
                        chunks.append(chunk)
            else:
                # Use standard recursive splitting
                chunks = self._chunk_section(text, chunk_size)
            
            # Assign quotas
            chunks_with_quotas = self._assign_quotas(chunks, total_questions)
            
            return chunks_with_quotas
            
        except Exception as e:
            logger.error(f"Error chunking text with quota: {str(e)}")
            raise
    
    def _chunk_section(self, text: str, chunk_size: int) -> List[Dict[str, Any]]:
        """Chunk a section of text using simple splitting."""
        try:
            # Calculate character-based chunk size (rough approximation)
            char_chunk_size = int(chunk_size * 4)  # ~4 chars per token
            char_overlap = int(self.chunk_overlap * 4)

            # Simple text splitting without LangChain
            separators = ["\n\n", "\n", ". ", "! ", "? ", " "]
            text_chunks = self._simple_split_text(text, char_chunk_size, char_overlap, separators)

            chunks = []
            for i, chunk_text in enumerate(text_chunks):
                token_count = self.count_tokens(chunk_text)
                chunks.append({
                    'content': chunk_text,
                    'token_count': token_count,
                    'chunk_index': i,
                    'section_heading': None,
                    'section_level': 0
                })

            return chunks

        except Exception as e:
            logger.error(f"Error chunking section: {str(e)}")
            return [{'content': text, 'token_count': self.count_tokens(text), 'chunk_index': 0}]

    def _simple_split_text(self, text: str, chunk_size: int, overlap: int, separators: List[str]) -> List[str]:
        """Simple text splitting implementation."""
        if len(text) <= chunk_size:
            return [text]

        chunks = []
        start = 0

        while start < len(text):
            end = start + chunk_size

            if end >= len(text):
                chunks.append(text[start:])
                break

            # Try to find a good split point
            split_point = end
            for separator in separators:
                last_sep = text.rfind(separator, start, end)
                if last_sep > start:
                    split_point = last_sep + len(separator)
                    break

            chunks.append(text[start:split_point])
            start = split_point - overlap if split_point > overlap else split_point

        return [chunk.strip() for chunk in chunks if chunk.strip()]
    
    def _assign_quotas(self, chunks: List[Dict[str, Any]], total_questions: int) -> List[Dict[str, Any]]:
        """
        Assign question quotas to chunks based on their size and importance.
        
        Args:
            chunks: List of chunks
            total_questions: Total questions to distribute
            
        Returns:
            Chunks with assigned quotas
        """
        if not chunks:
            return []
        
        # Calculate weights based on token count and heading level
        total_tokens = sum(chunk['token_count'] for chunk in chunks)
        
        for chunk in chunks:
            # Base weight from token count
            token_weight = chunk['token_count'] / total_tokens
            
            # Boost weight for sections with headings (more important content)
            heading_boost = 1.0
            if chunk.get('section_heading'):
                # Higher level headings get more boost
                level = chunk.get('section_level', 3)
                heading_boost = 1.5 - (level * 0.1)  # Level 1 = 1.4x, Level 2 = 1.3x, etc.
            
            chunk['weight'] = token_weight * heading_boost
        
        # Normalize weights
        total_weight = sum(chunk['weight'] for chunk in chunks)
        for chunk in chunks:
            chunk['weight'] = chunk['weight'] / total_weight
        
        # Assign quotas ensuring we get exactly total_questions
        assigned_questions = 0
        for i, chunk in enumerate(chunks):
            if i == len(chunks) - 1:  # Last chunk gets remaining questions
                chunk['question_quota'] = total_questions - assigned_questions
            else:
                quota = max(1, round(chunk['weight'] * total_questions))
                chunk['question_quota'] = quota
                assigned_questions += quota
        
        # Verify total
        actual_total = sum(chunk['question_quota'] for chunk in chunks)
        if actual_total != total_questions:
            # Adjust the largest chunk
            largest_chunk = max(chunks, key=lambda x: x['question_quota'])
            largest_chunk['question_quota'] += (total_questions - actual_total)
        
        return chunks
    
    def create_chunk_summary(self, chunk_content: str, max_tokens: int = 250) -> str:
        """
        Create a concise summary of chunk content for efficient LLM processing.
        
        Args:
            chunk_content: Content to summarize
            max_tokens: Maximum tokens for summary
            
        Returns:
            Summarized content
        """
        try:
            current_tokens = self.count_tokens(chunk_content)
            
            # If already under limit, return as-is
            if current_tokens <= max_tokens:
                return chunk_content
            
            # Extract key sentences and concepts
            sentences = re.split(r'[.!?]+', chunk_content)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            # Score sentences by length and position (earlier = more important)
            scored_sentences = []
            for i, sentence in enumerate(sentences):
                # Prefer longer sentences (more content) but penalize very long ones
                length_score = min(len(sentence.split()), 20) / 20
                position_score = 1.0 - (i / len(sentences)) * 0.3  # Earlier sentences get higher score
                score = length_score * position_score
                scored_sentences.append((sentence, score))
            
            # Sort by score and select top sentences
            scored_sentences.sort(key=lambda x: x[1], reverse=True)
            
            summary = ""
            for sentence, _ in scored_sentences:
                test_summary = summary + " " + sentence if summary else sentence
                if self.count_tokens(test_summary) > max_tokens:
                    break
                summary = test_summary
            
            return summary.strip() if summary else chunk_content[:1000]  # Fallback
            
        except Exception as e:
            logger.error(f"Error creating chunk summary: {str(e)}")
            # Fallback to simple truncation
            words = chunk_content.split()
            return " ".join(words[:max_tokens // 2])  # Rough token estimation


# Create singleton instance
chunking_service = ChunkingService()
