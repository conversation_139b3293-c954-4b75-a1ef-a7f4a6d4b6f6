"""
LangChain Multiagent Service for PDF Summary Generation

This service implements a multiagent system using LangChain to generate high-quality
summaries of PDF notes using 4 specialized agents working in parallel.
"""

import asyncio
import json
import logging
import os
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

# LangChain imports - required for multiagent functionality
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

from app.core.config import settings

logger = logging.getLogger(__name__)


@dataclass
class SummaryAgentResult:
    """Result from a single summary agent."""
    agent_id: str
    agent_type: str
    success: bool
    content: Dict[str, Any]
    error: Optional[str] = None
    execution_time: float = 0.0


@dataclass
class SummaryMultiAgentConfig:
    """Configuration for multiagent summary generation."""
    timeout_per_agent: int = 60
    retry_attempts: int = 2
    enable_parallel_processing: bool = True


class SummaryGenerationAgent:
    """Individual agent for generating specific parts of summaries."""
    
    def __init__(self, agent_id: str, agent_type: str, model_type: str = "gemini"):
        self.agent_id = agent_id
        self.agent_type = agent_type  # key_concepts, details, structure, quality_review
        self.model_type = model_type
        self.llm = self._create_llm()
        
    def _create_llm(self):
        """Create the appropriate LLM based on model type."""
        if self.model_type == "openai":
            return ChatOpenAI(
                model="gpt-4",  # Use GPT-4 for better quality
                temperature=0.3,
                max_tokens=4000,  # Increased for comprehensive summaries
                openai_api_key=settings.OPENAI_API_KEY
            )
        else:  # Default to Gemini
            return ChatGoogleGenerativeAI(
                model="gemini-1.5-flash",
                temperature=0.3,
                max_output_tokens=8000,  # Increased for comprehensive summaries
                google_api_key=settings.GEMINI_API_KEY or os.getenv("GEMINI_API_KEY")
            )
    
    async def generate_summary_component(
        self,
        content: str,
        course_name: Optional[str],
        content_chunk_id: str = "",
        previous_results: Optional[Dict] = None
    ) -> SummaryAgentResult:
        """Generate a specific component of the summary based on agent type."""
        start_time = time.time()
        
        try:
            logger.info(f"Agent {self.agent_id} ({self.agent_type}) starting summary generation")
            
            # Create specialized prompt based on agent type
            prompt = self._create_agent_prompt(content, course_name, previous_results)
            
            # Generate response
            messages = [
                SystemMessage(content=self._get_system_message()),
                HumanMessage(content=prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Parse the response based on agent type
            parsed_content = self._parse_agent_response(response.content)
            
            execution_time = time.time() - start_time
            
            if parsed_content:
                logger.info(f"Agent {self.agent_id} ({self.agent_type}) completed in {execution_time:.2f}s")
                return SummaryAgentResult(
                    agent_id=self.agent_id,
                    agent_type=self.agent_type,
                    success=True,
                    content=parsed_content,
                    execution_time=execution_time
                )
            else:
                return SummaryAgentResult(
                    agent_id=self.agent_id,
                    agent_type=self.agent_type,
                    success=False,
                    content={},
                    error="No valid content generated",
                    execution_time=execution_time
                )
                
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Agent {self.agent_id} ({self.agent_type}) failed: {str(e)}")
            return SummaryAgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                success=False,
                content={},
                error=str(e),
                execution_time=execution_time
            )
    
    def _get_system_message(self) -> str:
        """Get system message based on agent type."""
        system_messages = {
            "key_concepts": "You are an expert at extracting key concepts and main topics from educational content. Focus on identifying the most important ideas, theories, and concepts.",
            "details": "You are an expert at identifying important details and supporting information. Focus on facts, examples, explanations, and supporting evidence.",
            "structure": "You are an expert at creating structured, hierarchical summaries. Focus on organizing information logically with clear sections and subsections.",
            "quality_review": "You are an expert at reviewing and improving summaries for coherence, completeness, and clarity. Focus on ensuring logical flow and comprehensive coverage."
        }
        return system_messages.get(self.agent_type, "You are an expert educational content analyzer.")
    
    def _create_agent_prompt(self, content: str, course_name: Optional[str], previous_results: Optional[Dict] = None) -> str:
        """Create specialized prompt based on agent type."""
        course_context = f" for the {course_name} course" if course_name else ""
        
        if self.agent_type == "key_concepts":
            return self._create_key_concepts_prompt(content, course_context)
        elif self.agent_type == "details":
            return self._create_details_prompt(content, course_context)
        elif self.agent_type == "structure":
            return self._create_structure_prompt(content, course_context, previous_results)
        elif self.agent_type == "quality_review":
            return self._create_quality_review_prompt(content, course_context, previous_results)
        else:
            return f"Analyze this educational content{course_context}:\n\n{content[:3000]}"
    
    def _create_key_concepts_prompt(self, content: str, course_context: str) -> str:
        """Create prompt for key concepts extraction."""
        return f"""Extract the key concepts and main topics{course_context} from the following educational content.

IMPORTANT REQUIREMENTS:
1. Identify the most important concepts, theories, and ideas
2. Focus on fundamental principles and core knowledge
3. Include relevant terminology and definitions
4. Return ONLY valid JSON - no additional text or formatting

Content to analyze:
{content[:3000]}

Return your response as a JSON object with this exact structure:
{{
  "key_concepts": [
    {{
      "concept": "Concept name",
      "description": "Brief description",
      "importance": "high|medium|low"
    }}
  ],
  "main_topics": ["Topic 1", "Topic 2", "Topic 3"],
  "terminology": [
    {{
      "term": "Technical term",
      "definition": "Definition"
    }}
  ]
}}

Ensure the JSON is valid and complete."""
    
    def _create_details_prompt(self, content: str, course_context: str) -> str:
        """Create prompt for details extraction."""
        return f"""Extract ALL important details and supporting information{course_context} from the following educational content.

IMPORTANT REQUIREMENTS:
1. Extract ALL key facts, examples, explanations, and details from the ENTIRE content
2. Include ALL supporting evidence, specific information, formulas, and procedures
3. Capture ALL relevant data, statistics, case studies, and practical applications
4. Include step-by-step processes, methodologies, and detailed explanations
5. Extract ALL examples with their complete context and explanations
6. Ensure COMPREHENSIVE coverage - do not leave out any important details
7. Return ONLY valid JSON - no additional text or formatting

Content to analyze:
{content}

Return your response as a JSON object with this exact structure:
{{
  "important_facts": ["Comprehensive fact 1 with full explanation", "Detailed fact 2 with context", "Complete fact 3 with examples"],
  "detailed_explanations": [
    {{
      "topic": "Topic name",
      "explanation": "Complete, detailed explanation with all relevant information"
    }}
  ],
  "examples": [
    {{
      "example": "Detailed example description with complete context",
      "context": "Full explanation of where and how it applies",
      "details": "Additional details and implications"
    }}
  ],
  "procedures_and_methods": [
    {{
      "method": "Method or procedure name",
      "steps": ["Step 1 with details", "Step 2 with explanation", "Step 3 with context"],
      "applications": "Where and how this method is used"
    }}
  ],
  "supporting_evidence": ["Detailed evidence 1 with explanation", "Complete evidence 2 with context"],
  "data_points": [
    {{
      "type": "statistic|measurement|observation|formula",
      "value": "Specific value with complete description and context",
      "significance": "Why this data point is important"
    }}
  ]
}}

CRITICAL: Extract ALL details from the content. This should be comprehensive and detailed, not a summary."""
    
    def _create_structure_prompt(self, content: str, course_context: str, previous_results: Optional[Dict] = None) -> str:
        """Create prompt for structure organization."""
        context_info = ""
        if previous_results:
            context_info = f"\nPrevious analysis results to consider:\n{json.dumps(previous_results, indent=2)[:1000]}"

        return f"""Create a comprehensive, detailed structured summary{course_context} of the following educational content.

IMPORTANT REQUIREMENTS:
1. Create a COMPREHENSIVE summary that covers the ENTIRE document content
2. Organize information into logical sections and subsections with detailed explanations
3. Include ALL important topics, concepts, and details from the content
4. Each section should have substantial content, not just bullet points
5. Use clear hierarchy with main topics and detailed subtopics
6. Ensure NO important information is left out
7. Return ONLY valid JSON - no additional text or formatting

Content to analyze:
{content}{context_info}

Return your response as a JSON object with this exact structure:
{{
  "title": "Comprehensive Summary Title",
  "sections": [
    {{
      "section_title": "Main Section Title",
      "content": "Detailed paragraph explaining this section with comprehensive coverage",
      "subsections": [
        {{
          "subsection_title": "Detailed Subsection",
          "content": "Comprehensive explanation of this subsection with all relevant details, examples, and explanations from the source material",
          "key_points": ["Detailed point 1 with explanation", "Detailed point 2 with context", "Detailed point 3 with examples"]
        }}
      ]
    }}
  ],
  "conclusion": "Comprehensive conclusion that ties together all the main themes and concepts covered in the entire document"
}}

CRITICAL: Make sure to include ALL content from the source material. This should be a comprehensive summary, not a brief overview."""
    
    def _create_quality_review_prompt(self, content: str, course_context: str, previous_results: Optional[Dict] = None) -> str:
        """Create prompt for quality review."""
        analysis_info = ""
        if previous_results:
            analysis_info = f"\nPrevious analysis to review:\n{json.dumps(previous_results, indent=2)[:1500]}"
        
        return f"""Review and improve the summary analysis{course_context} for coherence, completeness, and clarity.

IMPORTANT REQUIREMENTS:
1. Ensure logical flow and coherence
2. Check for completeness and missing information
3. Improve clarity and readability
4. Return ONLY valid JSON - no additional text or formatting

Original content:
{content[:2000]}{analysis_info}

Return your response as a JSON object with this exact structure:
{{
  "coherence_score": 1-10,
  "completeness_score": 1-10,
  "clarity_score": 1-10,
  "improvements": [
    {{
      "area": "Area to improve",
      "suggestion": "Specific improvement suggestion"
    }}
  ],
  "missing_elements": ["Element 1", "Element 2"],
  "overall_assessment": "Overall quality assessment"
}}

Ensure the JSON is valid and complete."""
    
    def _parse_agent_response(self, response: str) -> Dict[str, Any]:
        """Parse agent response with robust error handling."""
        try:
            # Clean the response
            response = response.strip()
            
            # Find JSON content
            start_idx = response.find('{')
            end_idx = response.rfind('}')
            
            if start_idx != -1 and end_idx != -1:
                json_content = response[start_idx:end_idx + 1]
                data = json.loads(json_content)
                
                if isinstance(data, dict):
                    logger.info(f"Successfully parsed {self.agent_type} response")
                    return data
            
            logger.warning(f"Could not parse {self.agent_type} response as JSON")
            return {}
            
        except Exception as e:
            logger.error(f"Error parsing {self.agent_type} response: {str(e)}")
            return {}


class SummaryMultiAgentService:
    """Main service for coordinating multiple summary agents."""
    
    def __init__(self):
        self.model_type = "gemini"  # Default to Gemini for consistency
        self.agent_types = ["key_concepts", "details", "structure", "quality_review"]
    
    def _split_content_for_agents(self, content: str, num_chunks: int = 4) -> List[str]:
        """Split content into chunks for different agents."""
        if not content:
            return [""] * num_chunks
        
        # For summary generation, we want all agents to see the full content
        # but they will focus on different aspects
        return [content] * num_chunks
    
    async def generate_summary_multiagent(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        config: Optional[SummaryMultiAgentConfig] = None
    ) -> Dict[str, Any]:
        """Generate comprehensive summary using multiple agents."""
        if config is None:
            config = SummaryMultiAgentConfig()

        logger.info(f"Starting multiagent summary generation for {len(note_contents)} notes")
        start_time = asyncio.get_event_loop().time()

        try:
            # Combine all note contents
            combined_content = "\n\n".join(note_contents)
            
            # Split content for agents (all agents get full content but focus on different aspects)
            content_chunks = self._split_content_for_agents(combined_content, len(self.agent_types))
            
            # Create agents and tasks
            agents = []
            tasks = []
            
            for i, agent_type in enumerate(self.agent_types):
                agent_id = f"summary_agent_{i + 1}_{agent_type}"
                agent = SummaryGenerationAgent(agent_id, agent_type, self.model_type)
                agents.append(agent)
                
                # Create task
                task = agent.generate_summary_component(
                    content=content_chunks[i],
                    course_name=course_name,
                    content_chunk_id=f"chunk_{i + 1}"
                )
                tasks.append(task)
            
            # Execute all agents in parallel with timeout
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=config.timeout_per_agent * 2  # Allow extra time for parallel execution
                )
            except asyncio.TimeoutError:
                logger.error("Multiagent summary generation timed out")
                return {}
            
            # Process results
            agent_results = {}
            successful_agents = 0
            failed_agents = 0
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Agent {i + 1} ({self.agent_types[i]}) failed with exception: {str(result)}")
                    failed_agents += 1
                    continue
                
                if result.success:
                    agent_results[result.agent_type] = result.content
                    successful_agents += 1
                    logger.info(f"Agent {result.agent_id} completed successfully in {result.execution_time:.2f}s")
                else:
                    logger.warning(f"Agent {result.agent_id} failed: {result.error}")
                    failed_agents += 1
            
            # Combine results into final summary
            final_summary = self._combine_agent_results(agent_results, course_name)
            
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.info(f"Multiagent summary generation completed in {execution_time:.2f}s")
            logger.info(f"Successful agents: {successful_agents}, Failed agents: {failed_agents}")
            
            return final_summary

        except Exception as e:
            logger.error(f"Error in multiagent summary generation: {str(e)}")
            return {}
    
    def _combine_agent_results(self, agent_results: Dict[str, Dict], course_name: Optional[str]) -> Dict[str, Any]:
        """Combine results from all agents into a comprehensive summary."""
        try:
            # Extract components from each agent
            key_concepts_data = agent_results.get("key_concepts", {})
            details_data = agent_results.get("details", {})
            structure_data = agent_results.get("structure", {})
            quality_data = agent_results.get("quality_review", {})

            # Transform key_concepts from list of dicts to list of strings (for schema compatibility)
            key_concepts_list = []
            raw_concepts = key_concepts_data.get("key_concepts", [])
            for concept in raw_concepts:
                if isinstance(concept, dict):
                    concept_name = concept.get("concept", "")
                    if concept_name:
                        key_concepts_list.append(concept_name)
                elif isinstance(concept, str):
                    key_concepts_list.append(concept)

            # Transform structure from list to dict (for schema compatibility)
            structure_dict = {}
            raw_sections = structure_data.get("sections", [])
            if raw_sections:
                structure_dict = {
                    "title": structure_data.get("title", "Summary"),
                    "sections": raw_sections,
                    "conclusion": structure_data.get("conclusion", "")
                }

            # Create comprehensive summary
            summary = {
                "title": structure_data.get("title", f"Summary{' - ' + course_name if course_name else ''}"),
                "content": self._generate_summary_content(key_concepts_data, details_data, structure_data),
                "key_concepts": key_concepts_list,  # Now list of strings
                "structure": structure_dict,  # Now dict instead of list
                "topics": key_concepts_data.get("main_topics", []),
                "quality_assessment": quality_data,
                "metadata": {
                    "course_name": course_name,
                    "generation_method": "multiagent",
                    "agents_used": list(agent_results.keys()),
                    "completeness_score": quality_data.get("completeness_score", 0),
                    "coherence_score": quality_data.get("coherence_score", 0),
                    "clarity_score": quality_data.get("clarity_score", 0)
                }
            }

            return summary
            
        except Exception as e:
            logger.error(f"Error combining agent results: {str(e)}")
            return {
                "title": f"Summary{' - ' + course_name if course_name else ''}",
                "content": "Summary generation encountered an error.",
                "key_concepts": [],
                "structure": [],
                "topics": [],
                "metadata": {"error": str(e)}
            }
    
    def _generate_summary_content(self, key_concepts: Dict, details: Dict, structure: Dict) -> str:
        """Generate comprehensive, detailed summary content from agent results."""
        try:
            content_parts = []

            # Add title
            if structure.get("title"):
                content_parts.append(f"# {structure['title']}")
                content_parts.append("")

            # Add comprehensive key concepts section
            if key_concepts.get("key_concepts"):
                content_parts.append("## Key Concepts and Main Topics")
                content_parts.append("")
                for concept in key_concepts["key_concepts"]:  # Include ALL concepts
                    if isinstance(concept, dict):
                        concept_name = concept.get('concept', 'Unknown')
                        description = concept.get('description', '')
                        importance = concept.get('importance', 'medium')
                        content_parts.append(f"### {concept_name}")
                        if description:
                            content_parts.append(f"{description}")
                        content_parts.append(f"*Importance Level: {importance.title()}*")
                        content_parts.append("")
                content_parts.append("")

            # Add comprehensive structured sections with ALL content
            if structure.get("sections"):
                content_parts.append("## Detailed Content Analysis")
                content_parts.append("")

                for section in structure["sections"]:
                    if isinstance(section, dict):
                        section_title = section.get('section_title', 'Section')
                        content_parts.append(f"## {section_title}")
                        content_parts.append("")

                        # Add section content if available
                        if section.get("content"):
                            content_parts.append(section["content"])
                            content_parts.append("")

                        # Add ALL subsections with comprehensive content
                        if section.get("subsections"):
                            for subsection in section["subsections"]:
                                if isinstance(subsection, dict):
                                    subsection_title = subsection.get('subsection_title', 'Subsection')
                                    content_parts.append(f"### {subsection_title}")
                                    content_parts.append("")

                                    # Add detailed subsection content
                                    if subsection.get("content"):
                                        content_parts.append(subsection["content"])
                                        content_parts.append("")

                                    # Add ALL key points with details
                                    if subsection.get("key_points"):
                                        content_parts.append("**Key Points:**")
                                        for point in subsection["key_points"]:
                                            content_parts.append(f"- {point}")
                                        content_parts.append("")

            # Add comprehensive details section
            self._add_detailed_information(content_parts, details)

            # Add comprehensive conclusion
            if structure.get("conclusion"):
                content_parts.append("## Comprehensive Conclusion")
                content_parts.append("")
                content_parts.append(structure["conclusion"])
                content_parts.append("")

            return "\n".join(content_parts)

        except Exception as e:
            logger.error(f"Error generating summary content: {str(e)}")
            return "Error generating comprehensive summary content."

    def _add_detailed_information(self, content_parts: List[str], details: Dict):
        """Add comprehensive detailed information to the summary."""
        try:
            # Add ALL important facts
            if details.get("important_facts"):
                content_parts.append("## Important Facts and Information")
                content_parts.append("")
                for fact in details["important_facts"]:  # Include ALL facts
                    content_parts.append(f"- {fact}")
                content_parts.append("")

            # Add detailed explanations
            if details.get("detailed_explanations"):
                content_parts.append("## Detailed Explanations")
                content_parts.append("")
                for explanation in details["detailed_explanations"]:
                    if isinstance(explanation, dict):
                        topic = explanation.get("topic", "Topic")
                        explanation_text = explanation.get("explanation", "")
                        content_parts.append(f"### {topic}")
                        content_parts.append(explanation_text)
                        content_parts.append("")

            # Add ALL examples with full context
            if details.get("examples"):
                content_parts.append("## Examples and Applications")
                content_parts.append("")
                for example in details["examples"]:
                    if isinstance(example, dict):
                        example_text = example.get("example", "")
                        context = example.get("context", "")
                        example_details = example.get("details", "")

                        content_parts.append(f"**Example:** {example_text}")
                        if context:
                            content_parts.append(f"**Context:** {context}")
                        if example_details:
                            content_parts.append(f"**Details:** {example_details}")
                        content_parts.append("")

            # Add procedures and methods
            if details.get("procedures_and_methods"):
                content_parts.append("## Procedures and Methods")
                content_parts.append("")
                for procedure in details["procedures_and_methods"]:
                    if isinstance(procedure, dict):
                        method = procedure.get("method", "Method")
                        steps = procedure.get("steps", [])
                        applications = procedure.get("applications", "")

                        content_parts.append(f"### {method}")
                        if steps:
                            content_parts.append("**Steps:**")
                            for i, step in enumerate(steps, 1):
                                content_parts.append(f"{i}. {step}")
                        if applications:
                            content_parts.append(f"**Applications:** {applications}")
                        content_parts.append("")

            # Add supporting evidence
            if details.get("supporting_evidence"):
                content_parts.append("## Supporting Evidence")
                content_parts.append("")
                for evidence in details["supporting_evidence"]:
                    content_parts.append(f"- {evidence}")
                content_parts.append("")

            # Add data points and formulas
            if details.get("data_points"):
                content_parts.append("## Data Points and Formulas")
                content_parts.append("")
                for data_point in details["data_points"]:
                    if isinstance(data_point, dict):
                        data_type = data_point.get("type", "data")
                        value = data_point.get("value", "")
                        significance = data_point.get("significance", "")

                        content_parts.append(f"**{data_type.title()}:** {value}")
                        if significance:
                            content_parts.append(f"**Significance:** {significance}")
                        content_parts.append("")

        except Exception as e:
            logger.error(f"Error adding detailed information: {str(e)}")
            content_parts.append("## Additional Details")
            content_parts.append("Error processing detailed information.")
            content_parts.append("")


# Create singleton instance
summary_multiagent_service = SummaryMultiAgentService()
