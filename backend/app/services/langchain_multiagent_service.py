"""
LangChain Multiagent Service for MCQ and Flashcard Generation

This service implements a multiagent system using LangChain to resolve JSON parsing errors
when generating large numbers of questions by distributing the work across multiple agents.
"""

import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed

import google.generativeai as genai

# LangChain imports - required for multiagent functionality
from langchain_google_genai import Chat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

from app.core.config import settings

logger = logging.getLogger(__name__)


@dataclass
class AgentResult:
    """Result from a single agent."""
    agent_id: str
    success: bool
    questions: List[Dict]
    error: Optional[str] = None
    execution_time: float = 0.0


@dataclass
class MultiAgentConfig:
    """Configuration for multiagent generation."""
    total_questions: int
    max_questions_per_agent: int = 12
    min_questions_per_agent: int = 5
    max_agents: int = 8
    timeout_per_agent: int = 45
    retry_attempts: int = 2


class QuestionGenerationAgent:
    """Individual agent for generating questions."""
    
    def __init__(self, agent_id: str, model_type: str = "gemini"):
        self.agent_id = agent_id
        self.model_type = model_type
        self.llm = self._create_llm()
        
    def _create_llm(self):
        """Create the appropriate LLM based on model type."""
        if self.model_type == "openai":
            return ChatOpenAI(
                model="gpt-3.5-turbo",
                temperature=0.3,
                max_tokens=2000,
                openai_api_key=settings.OPENAI_API_KEY
            )
        else:  # Default to Gemini
            return ChatGoogleGenerativeAI(
                model="gemini-1.5-flash",
                temperature=0.3,
                max_output_tokens=2000,
                google_api_key=settings.GEMINI_API_KEY or os.getenv("GEMINI_API_KEY")
            )
    
    async def generate_mcqs(
        self,
        content: str,
        course_name: Optional[str],
        question_count: int,
        content_chunk_id: str = ""
    ) -> AgentResult:
        """Generate MCQs using this agent."""
        import time
        start_time = time.time()
        
        try:
            logger.info(f"Agent {self.agent_id} generating {question_count} MCQs")
            
            # Create focused prompt for smaller batches
            prompt = self._create_mcq_prompt(content, course_name, question_count)
            
            # Generate response
            messages = [
                SystemMessage(content="You are an expert educational content creator specializing in multiple-choice questions."),
                HumanMessage(content=prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Parse the response
            questions = self._parse_mcq_response(response.content)
            
            execution_time = time.time() - start_time
            
            if questions:
                logger.info(f"Agent {self.agent_id} successfully generated {len(questions)} MCQs in {execution_time:.2f}s")
                return AgentResult(
                    agent_id=self.agent_id,
                    success=True,
                    questions=questions[:question_count],  # Ensure we don't exceed requested count
                    execution_time=execution_time
                )
            else:
                return AgentResult(
                    agent_id=self.agent_id,
                    success=False,
                    questions=[],
                    error="No valid questions generated",
                    execution_time=execution_time
                )
                
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Agent {self.agent_id} failed to generate MCQs: {str(e)}")
            return AgentResult(
                agent_id=self.agent_id,
                success=False,
                questions=[],
                error=str(e),
                execution_time=execution_time
            )
    
    async def generate_flashcards(
        self,
        content: str,
        course_name: Optional[str],
        card_count: int,
        content_chunk_id: str = ""
    ) -> AgentResult:
        """Generate flashcards using this agent."""
        import time
        start_time = time.time()
        
        try:
            logger.info(f"Agent {self.agent_id} generating {card_count} flashcards")
            
            # Create focused prompt for smaller batches
            prompt = self._create_flashcard_prompt(content, course_name, card_count)
            
            # Generate response
            messages = [
                SystemMessage(content="You are an expert educational content creator specializing in flashcards for effective learning."),
                HumanMessage(content=prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Parse the response
            flashcards = self._parse_flashcard_response(response.content)
            
            execution_time = time.time() - start_time
            
            if flashcards:
                logger.info(f"Agent {self.agent_id} successfully generated {len(flashcards)} flashcards in {execution_time:.2f}s")
                return AgentResult(
                    agent_id=self.agent_id,
                    success=True,
                    questions=flashcards[:card_count],  # Ensure we don't exceed requested count
                    execution_time=execution_time
                )
            else:
                return AgentResult(
                    agent_id=self.agent_id,
                    success=False,
                    questions=[],
                    error="No valid flashcards generated",
                    execution_time=execution_time
                )
                
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Agent {self.agent_id} failed to generate flashcards: {str(e)}")
            return AgentResult(
                agent_id=self.agent_id,
                success=False,
                questions=[],
                error=str(e),
                execution_time=execution_time
            )
    
    def _create_mcq_prompt(self, content: str, course_name: Optional[str], question_count: int) -> str:
        """Create a focused MCQ generation prompt."""
        course_context = f" for the {course_name} course" if course_name else ""
        
        return f"""Generate exactly {question_count} high-quality multiple-choice questions{course_context} based on the following content.

IMPORTANT REQUIREMENTS:
1. Generate EXACTLY {question_count} questions - no more, no less
2. Each question must have exactly 4 options (A, B, C, D)
3. Only one option should be correct
4. Include clear explanations for the correct answers
5. Return ONLY valid JSON - no additional text or formatting
6. Ensure the JSON is complete and properly closed

Content to analyze:
{content[:3000]}

Return your response as a JSON array with this exact structure:
[
  {{
    "content": "Question text here?",
    "option_A": "First option",
    "option_B": "Second option",
    "option_C": "Third option",
    "option_D": "Fourth option",
    "answer": "A",
    "explanation": "Detailed explanation of why this answer is correct"
  }}
]

Generate exactly {question_count} questions in this format. Ensure the JSON is valid and complete."""
    
    def _create_flashcard_prompt(self, content: str, course_name: Optional[str], card_count: int) -> str:
        """Create a focused flashcard generation prompt."""
        course_context = f" for the {course_name} course" if course_name else ""
        
        return f"""Generate exactly {card_count} high-quality flashcards{course_context} based on the following content.

IMPORTANT REQUIREMENTS:
1. Generate EXACTLY {card_count} flashcards - no more, no less
2. Each flashcard must have clear front_content (question/concept) and back_content (answer/explanation)
3. Include a relevant topic for each flashcard
4. Return ONLY valid JSON - no additional text or formatting
5. Ensure the JSON is complete and properly closed

Content to analyze:
{content[:3000]}

Return your response as a JSON array with this exact structure:
[
  {{
    "front_content": "Question or concept here",
    "back_content": "Detailed answer or explanation here",
    "topic": "Relevant topic name"
  }}
]

Generate exactly {card_count} flashcards in this format. Ensure the JSON is valid and complete."""
    
    def _parse_mcq_response(self, response: str) -> List[Dict]:
        """Parse MCQ response with robust error handling."""
        return self._parse_json_response(response, "MCQ")
    
    def _parse_flashcard_response(self, response: str) -> List[Dict]:
        """Parse flashcard response with robust error handling."""
        return self._parse_json_response(response, "flashcard")
    
    def _parse_json_response(self, response: str, content_type: str) -> List[Dict]:
        """Parse JSON response with robust error handling and repair."""
        try:
            # Clean the response
            response = response.strip()

            # Remove markdown formatting if present
            if response.startswith('```json'):
                response = response[7:]
            if response.startswith('```'):
                response = response[3:]
            if response.endswith('```'):
                response = response[:-3]

            # Try to parse JSON
            try:
                data = json.loads(response)
                if isinstance(data, list):
                    # Normalize field names for MCQs
                    if content_type == "MCQ":
                        data = self._normalize_mcq_fields(data)
                    return data
                elif isinstance(data, dict):
                    # Handle wrapped responses
                    if 'questions' in data:
                        questions = data['questions']
                        if content_type == "MCQ":
                            questions = self._normalize_mcq_fields(questions)
                        return questions
                    elif 'flashcards' in data or 'flash_cards' in data:
                        return data.get('flashcards', data.get('flash_cards', []))
                    else:
                        # Single item response
                        single_item = [data]
                        if content_type == "MCQ":
                            single_item = self._normalize_mcq_fields(single_item)
                        return single_item
                else:
                    logger.warning(f"Unexpected JSON structure for {content_type}: {type(data)}")
                    return []

            except json.JSONDecodeError as e:
                logger.warning(f"JSON decode error for {content_type}: {str(e)}")
                # Attempt to repair the JSON
                return self._attempt_json_repair(response, content_type)

        except Exception as e:
            logger.error(f"Error parsing {content_type} response: {str(e)}")
            return []

    def _normalize_mcq_fields(self, questions: List[Dict]) -> List[Dict]:
        """Normalize MCQ field names to match expected format."""
        normalized = []
        for q in questions:
            if isinstance(q, dict):
                # Create a copy to avoid modifying original
                normalized_q = q.copy()

                # Map correct_answer to answer if needed
                if 'correct_answer' in normalized_q and 'answer' not in normalized_q:
                    normalized_q['answer'] = normalized_q['correct_answer']
                    del normalized_q['correct_answer']

                # Ensure we have the required fields
                if 'content' in normalized_q and 'answer' in normalized_q:
                    logger.debug(f"Normalized MCQ: content='{normalized_q['content'][:50]}...', answer='{normalized_q['answer']}'")
                    normalized.append(normalized_q)
                else:
                    logger.warning(f"Skipping MCQ with missing required fields: {list(normalized_q.keys())}")
                    logger.warning(f"MCQ data: {normalized_q}")

        return normalized
    
    def _attempt_json_repair(self, response: str, content_type: str) -> List[Dict]:
        """Attempt to repair malformed JSON responses."""
        try:
            # Try to find and extract valid JSON array
            start_idx = response.find('[')
            if start_idx == -1:
                return []
            
            # Find the last complete object
            brace_count = 0
            last_complete_end = -1
            
            for i, char in enumerate(response[start_idx:], start_idx):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        last_complete_end = i
            
            if last_complete_end > start_idx:
                # Try to create valid JSON by closing the array
                repaired = response[start_idx:last_complete_end + 1] + ']'
                data = json.loads(repaired)
                if isinstance(data, list):
                    # Normalize field names for MCQs
                    if content_type == "MCQ":
                        data = self._normalize_mcq_fields(data)
                    logger.info(f"Successfully repaired {content_type} JSON, recovered {len(data)} items")
                    return data
            
        except Exception as e:
            logger.warning(f"JSON repair failed for {content_type}: {str(e)}")
        
        return []


class LangChainMultiAgentService:
    """Main service for coordinating multiple agents."""
    
    def __init__(self):
        self.model_type = "gemini"  # Default to Gemini for consistency
    
    def _calculate_agent_distribution(self, config: MultiAgentConfig) -> List[Tuple[str, int]]:
        """Calculate optimal agent distribution for the given configuration."""
        total_questions = config.total_questions
        max_per_agent = config.max_questions_per_agent
        min_per_agent = config.min_questions_per_agent
        max_agents = config.max_agents
        
        # Calculate optimal number of agents
        if total_questions <= max_per_agent:
            # Single agent can handle it
            return [("agent_1", total_questions)]
        
        # Calculate number of agents needed
        num_agents = min(max_agents, (total_questions + max_per_agent - 1) // max_per_agent)
        
        # Distribute questions evenly
        base_questions = total_questions // num_agents
        extra_questions = total_questions % num_agents
        
        # Ensure minimum questions per agent
        if base_questions < min_per_agent and num_agents > 1:
            num_agents = max(1, total_questions // min_per_agent)
            base_questions = total_questions // num_agents
            extra_questions = total_questions % num_agents
        
        # Create distribution
        distribution = []
        for i in range(num_agents):
            agent_id = f"agent_{i + 1}"
            questions_for_agent = base_questions + (1 if i < extra_questions else 0)
            distribution.append((agent_id, questions_for_agent))
        
        logger.info(f"Agent distribution: {distribution}")
        return distribution

    def _split_content_for_agents(self, content: str, num_agents: int) -> List[str]:
        """Split content into chunks for different agents."""
        if num_agents == 1:
            return [content]

        # Split content into roughly equal parts
        content_length = len(content)
        chunk_size = content_length // num_agents

        chunks = []
        for i in range(num_agents):
            start = i * chunk_size
            if i == num_agents - 1:
                # Last chunk gets remaining content
                end = content_length
            else:
                # Find a good break point (end of sentence or paragraph)
                end = start + chunk_size
                # Look for sentence endings within next 200 characters
                search_end = min(end + 200, content_length)
                for j in range(end, search_end):
                    if content[j] in '.!?\n':
                        end = j + 1
                        break

            chunk = content[start:end].strip()
            if chunk:
                chunks.append(chunk)

        # If we have fewer chunks than agents, duplicate the last chunk
        while len(chunks) < num_agents:
            chunks.append(chunks[-1])

        return chunks

    async def generate_mcqs_multiagent(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        question_count: int,
        config: Optional[MultiAgentConfig] = None
    ) -> List[Dict]:
        """Generate MCQs using multiple agents."""
        if config is None:
            config = MultiAgentConfig(total_questions=question_count)

        logger.info(f"Starting multiagent MCQ generation for {question_count} questions")
        start_time = asyncio.get_event_loop().time()

        try:
            # Combine note contents
            combined_content = "\n\n".join(note_contents)

            # Calculate agent distribution
            agent_distribution = self._calculate_agent_distribution(config)
            num_agents = len(agent_distribution)

            # Split content for agents
            content_chunks = self._split_content_for_agents(combined_content, num_agents)

            # Create agents and tasks
            agents = []
            tasks = []

            for i, (agent_id, questions_for_agent) in enumerate(agent_distribution):
                agent = QuestionGenerationAgent(agent_id, self.model_type)
                agents.append(agent)

                # Use appropriate content chunk
                content_chunk = content_chunks[i] if i < len(content_chunks) else content_chunks[-1]

                # Create task
                task = agent.generate_mcqs(
                    content=content_chunk,
                    course_name=course_name,
                    question_count=questions_for_agent,
                    content_chunk_id=f"chunk_{i + 1}"
                )
                tasks.append(task)

            # Execute all agents in parallel with timeout
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=config.timeout_per_agent * 2  # Allow extra time for parallel execution
                )
            except asyncio.TimeoutError:
                logger.error("Multiagent MCQ generation timed out")
                raise TimeoutError("Multiagent MCQ generation timed out - increase timeout or reduce question count")

            # Process results
            all_questions = []
            successful_agents = 0
            failed_agents = 0

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Agent {agent_distribution[i][0]} failed with exception: {str(result)}")
                    failed_agents += 1
                elif isinstance(result, AgentResult):
                    if result.success and result.questions:
                        all_questions.extend(result.questions)
                        successful_agents += 1
                        logger.info(f"Agent {result.agent_id} contributed {len(result.questions)} questions")
                    else:
                        logger.warning(f"Agent {result.agent_id} failed: {result.error}")
                        failed_agents += 1
                else:
                    logger.error(f"Unexpected result type from agent: {type(result)}")
                    failed_agents += 1

            execution_time = asyncio.get_event_loop().time() - start_time

            logger.info(f"Multiagent MCQ generation completed in {execution_time:.2f}s")
            logger.info(f"Successful agents: {successful_agents}, Failed agents: {failed_agents}")
            logger.info(f"Total questions generated: {len(all_questions)}")

            # If we have too many questions, trim to requested count
            if len(all_questions) > question_count:
                all_questions = all_questions[:question_count]
                logger.info(f"Trimmed to requested {question_count} questions")

            # If we have too few questions, implement retry logic for failed agents
            if len(all_questions) < question_count * 0.8 and failed_agents > 0:
                logger.warning(f"Only generated {len(all_questions)} out of {question_count} requested questions, retrying failed agents")

                # Retry failed agents with remaining question count
                remaining_questions = question_count - len(all_questions)
                if remaining_questions > 0:
                    retry_tasks = []
                    for i, result in enumerate(results):
                        if isinstance(result, Exception) or (hasattr(result, 'success') and not result.success):
                            # Retry this agent
                            agent = QuestionGenerationAgent(f"retry_agent_{i}", self.model_type)
                            content_chunk = content_chunks[i] if i < len(content_chunks) else content_chunks[-1]
                            retry_task = agent.generate_mcqs(
                                content=content_chunk,
                                course_name=course_name,
                                question_count=min(remaining_questions, config.max_questions_per_agent),
                                content_chunk_id=f"retry_chunk_{i + 1}"
                            )
                            retry_tasks.append(retry_task)

                    if retry_tasks:
                        try:
                            retry_results = await asyncio.wait_for(
                                asyncio.gather(*retry_tasks, return_exceptions=True),
                                timeout=config.timeout_per_agent
                            )

                            # Process retry results
                            for result in retry_results:
                                if isinstance(result, AgentResult) and result.success:
                                    all_questions.extend(result.questions)
                                    logger.info(f"Retry agent generated {len(result.questions)} additional questions")
                        except asyncio.TimeoutError:
                            logger.warning("Retry agents timed out")

            # Ensure we don't exceed requested count
            if len(all_questions) > question_count:
                all_questions = all_questions[:question_count]

            if len(all_questions) == 0:
                raise ValueError("No questions were generated by any agent")

            return all_questions

        except Exception as e:
            logger.error(f"Error in multiagent MCQ generation: {str(e)}")
            raise e  # Re-raise instead of returning empty list

    async def generate_flashcards_multiagent(
        self,
        note_contents: List[str],
        course_name: Optional[str],
        card_count: int,
        config: Optional[MultiAgentConfig] = None
    ) -> List[Dict]:
        """Generate flashcards using multiple agents."""
        if config is None:
            config = MultiAgentConfig(total_questions=card_count)

        logger.info(f"Starting multiagent flashcard generation for {card_count} cards")
        start_time = asyncio.get_event_loop().time()

        try:
            # Combine note contents
            combined_content = "\n\n".join(note_contents)

            # Calculate agent distribution
            agent_distribution = self._calculate_agent_distribution(config)
            num_agents = len(agent_distribution)

            # Split content for agents
            content_chunks = self._split_content_for_agents(combined_content, num_agents)

            # Create agents and tasks
            agents = []
            tasks = []

            for i, (agent_id, cards_for_agent) in enumerate(agent_distribution):
                agent = QuestionGenerationAgent(agent_id, self.model_type)
                agents.append(agent)

                # Use appropriate content chunk
                content_chunk = content_chunks[i] if i < len(content_chunks) else content_chunks[-1]

                # Create task
                task = agent.generate_flashcards(
                    content=content_chunk,
                    course_name=course_name,
                    card_count=cards_for_agent,
                    content_chunk_id=f"chunk_{i + 1}"
                )
                tasks.append(task)

            # Execute all agents in parallel with timeout
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=config.timeout_per_agent * 2  # Allow extra time for parallel execution
                )
            except asyncio.TimeoutError:
                logger.error("Multiagent flashcard generation timed out")
                raise TimeoutError("Multiagent flashcard generation timed out - increase timeout or reduce card count")

            # Process results
            all_flashcards = []
            successful_agents = 0
            failed_agents = 0

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Agent {agent_distribution[i][0]} failed with exception: {str(result)}")
                    failed_agents += 1
                elif isinstance(result, AgentResult):
                    if result.success and result.questions:
                        all_flashcards.extend(result.questions)
                        successful_agents += 1
                        logger.info(f"Agent {result.agent_id} contributed {len(result.questions)} flashcards")
                    else:
                        logger.warning(f"Agent {result.agent_id} failed: {result.error}")
                        failed_agents += 1
                else:
                    logger.error(f"Unexpected result type from agent: {type(result)}")
                    failed_agents += 1

            execution_time = asyncio.get_event_loop().time() - start_time

            logger.info(f"Multiagent flashcard generation completed in {execution_time:.2f}s")
            logger.info(f"Successful agents: {successful_agents}, Failed agents: {failed_agents}")
            logger.info(f"Total flashcards generated: {len(all_flashcards)}")

            # If we have too many flashcards, trim to requested count
            if len(all_flashcards) > card_count:
                all_flashcards = all_flashcards[:card_count]
                logger.info(f"Trimmed to requested {card_count} flashcards")

            # If we have too few flashcards, implement retry logic for failed agents
            if len(all_flashcards) < card_count * 0.8 and failed_agents > 0:
                logger.warning(f"Only generated {len(all_flashcards)} out of {card_count} requested flashcards, retrying failed agents")

                # Retry failed agents with remaining card count
                remaining_cards = card_count - len(all_flashcards)
                if remaining_cards > 0:
                    retry_tasks = []
                    for i, result in enumerate(results):
                        if isinstance(result, Exception) or (hasattr(result, 'success') and not result.success):
                            # Retry this agent
                            agent = QuestionGenerationAgent(f"retry_agent_{i}", self.model_type)
                            content_chunk = content_chunks[i] if i < len(content_chunks) else content_chunks[-1]
                            retry_task = agent.generate_flashcards(
                                content=content_chunk,
                                course_name=course_name,
                                card_count=min(remaining_cards, config.max_questions_per_agent),
                                content_chunk_id=f"retry_chunk_{i + 1}"
                            )
                            retry_tasks.append(retry_task)

                    if retry_tasks:
                        try:
                            retry_results = await asyncio.wait_for(
                                asyncio.gather(*retry_tasks, return_exceptions=True),
                                timeout=config.timeout_per_agent
                            )

                            # Process retry results
                            for result in retry_results:
                                if isinstance(result, AgentResult) and result.success:
                                    all_flashcards.extend(result.questions)
                                    logger.info(f"Retry agent generated {len(result.questions)} additional flashcards")
                        except asyncio.TimeoutError:
                            logger.warning("Retry agents timed out")

            # Ensure we don't exceed requested count
            if len(all_flashcards) > card_count:
                all_flashcards = all_flashcards[:card_count]

            if len(all_flashcards) == 0:
                raise ValueError("No flashcards were generated by any agent")

            return all_flashcards

        except Exception as e:
            logger.error(f"Error in multiagent flashcard generation: {str(e)}")
            raise e  # Re-raise instead of returning empty list


# Create singleton instance
langchain_multiagent_service = LangChainMultiAgentService()
