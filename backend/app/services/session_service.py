from typing import Any, Dict, List, Optional, Union

from sqlalchemy.orm import Session

from app import schemas
from app.models.session import Session, SessionStatus


def get(db: Session, id: int) -> Optional[Session]:
    return db.query(Session).filter(Session.id == id).first()


def get_multi(
    db: Session,
    *,
    skip: int = 0,
    limit: int = 100,
    course_id: Optional[int] = None,
    tutor_id: Optional[int] = None,
    status: Optional[SessionStatus] = None
) -> list[Session]:
    query = db.query(Session)

    if course_id is not None:
        query = query.filter(Session.course_id == course_id)

    if tutor_id is not None:
        query = query.filter(Session.tutor_id == tutor_id)

    if status is not None:
        query = query.filter(Session.status == status)

    return query.offset(skip).limit(limit).all()


def create(db: Session, *, obj_in: schemas.SessionCreate) -> Session:
    db_obj = Session(
        title=obj_in.title,
        description=obj_in.description,
        session_type=obj_in.session_type,
        status=obj_in.status,
        start_time=obj_in.start_time,
        end_time=obj_in.end_time,
        location=obj_in.location,
        video_link=obj_in.video_link,
        max_students=obj_in.max_students,
        is_recurring=obj_in.is_recurring,
        recurrence_pattern=obj_in.recurrence_pattern,
        course_id=obj_in.course_id,
        tutor_id=obj_in.tutor_id,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(
    db: Session, *, db_obj: Session, obj_in: Union[schemas.SessionUpdate, Dict[str, Any]]
) -> Session:
    if isinstance(obj_in, dict):
        update_data = obj_in
    else:
        update_data = obj_in.model_dump(exclude_unset=True)
    for field in update_data:
        if field in update_data:
            setattr(db_obj, field, update_data[field])
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete(db: Session, *, id: int) -> Session:
    obj = db.query(Session).get(id)
    db.delete(obj)
    db.commit()
    return obj


def get_by_tutor(
    db: Session, *, tutor_id: int, skip: int = 0, limit: int = 100
) -> List[Session]:
    return (
        db.query(Session)
        .filter(Session.tutor_id == tutor_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def get_by_student(
    db: Session, *, student_id: int, skip: int = 0, limit: int = 100
) -> List[Session]:
    return (
        db.query(Session)
        .filter(Session.student_id == student_id)
        .offset(skip)
        .limit(limit)
        .all()
    )
