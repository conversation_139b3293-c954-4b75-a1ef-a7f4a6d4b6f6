"""
PDF Generation Service

This service handles the generation of PDF documents from summary content
using ReportLab for high-quality PDF output.
"""

import logging
import io
import re
from typing import Dict, List, Optional, Any
from datetime import datetime

from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocT<PERSON>plate, Paragraph, Spacer, PageBreak
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY
from reportlab.lib.colors import black, blue, darkblue
from reportlab.lib.units import inch

logger = logging.getLogger(__name__)


class PDFGenerationService:
    """Service for generating PDF documents from summary content."""
    
    def __init__(self):
        self.styles = self._create_custom_styles()
    
    def _create_custom_styles(self) -> Dict[str, ParagraphStyle]:
        """Create custom paragraph styles for PDF generation."""
        base_styles = getSampleStyleSheet()
        
        custom_styles = {
            'title': ParagraphStyle(
                name='CustomTitle',
                parent=base_styles['Heading1'],
                alignment=TA_CENTER,
                fontSize=20,
                spaceAfter=24,
                textColor=darkblue,
                fontName='Helvetica-Bold'
            ),
            'heading1': ParagraphStyle(
                name='CustomHeading1',
                parent=base_styles['Heading1'],
                fontSize=16,
                spaceAfter=12,
                spaceBefore=18,
                textColor=darkblue,
                fontName='Helvetica-Bold'
            ),
            'heading2': ParagraphStyle(
                name='CustomHeading2',
                parent=base_styles['Heading2'],
                fontSize=14,
                spaceAfter=10,
                spaceBefore=14,
                textColor=darkblue,
                fontName='Helvetica-Bold'
            ),
            'heading3': ParagraphStyle(
                name='CustomHeading3',
                parent=base_styles['Heading3'],
                fontSize=12,
                spaceAfter=8,
                spaceBefore=12,
                textColor=darkblue,
                fontName='Helvetica-Bold'
            ),
            'normal': ParagraphStyle(
                name='CustomNormal',
                parent=base_styles['Normal'],
                fontSize=11,
                spaceAfter=6,
                alignment=TA_JUSTIFY,
                fontName='Helvetica'
            ),
            'bullet': ParagraphStyle(
                name='CustomBullet',
                parent=base_styles['Normal'],
                fontSize=11,
                spaceAfter=4,
                leftIndent=20,
                bulletIndent=10,
                fontName='Helvetica'
            ),
            'metadata': ParagraphStyle(
                name='CustomMetadata',
                parent=base_styles['Normal'],
                fontSize=9,
                spaceAfter=4,
                textColor=black,
                fontName='Helvetica-Oblique'
            )
        }
        
        return custom_styles
    
    def _parse_markdown_content(self, content: str) -> List[Dict[str, Any]]:
        """Parse markdown-style content into structured elements."""
        elements = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            
            if not line:
                elements.append({'type': 'spacer', 'height': 6})
                continue
            
            # Headers
            if line.startswith('### '):
                elements.append({
                    'type': 'heading3',
                    'text': line[4:].strip()
                })
            elif line.startswith('## '):
                elements.append({
                    'type': 'heading2',
                    'text': line[3:].strip()
                })
            elif line.startswith('# '):
                elements.append({
                    'type': 'heading1',
                    'text': line[2:].strip()
                })
            # Bullet points
            elif line.startswith('- ') or line.startswith('* '):
                elements.append({
                    'type': 'bullet',
                    'text': f"• {line[2:].strip()}"
                })
            # Numbered lists
            elif re.match(r'^\d+\.\s', line):
                elements.append({
                    'type': 'bullet',
                    'text': line
                })
            # Bold text (simple **text** format)
            elif '**' in line:
                # Convert **text** to <b>text</b> for ReportLab
                formatted_line = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', line)
                elements.append({
                    'type': 'normal',
                    'text': formatted_line
                })
            # Italic text (simple *text* format)
            elif '*' in line and '**' not in line:
                # Convert *text* to <i>text</i> for ReportLab
                formatted_line = re.sub(r'\*(.*?)\*', r'<i>\1</i>', line)
                elements.append({
                    'type': 'normal',
                    'text': formatted_line
                })
            # Regular paragraph
            else:
                elements.append({
                    'type': 'normal',
                    'text': line
                })
        
        return elements
    
    def _create_pdf_content(self, summary_data: Dict[str, Any]) -> List[Any]:
        """Create PDF content elements from summary data."""
        content = []
        
        # Title
        title = summary_data.get('title', 'Summary')
        content.append(Paragraph(title, self.styles['title']))
        content.append(Spacer(1, 12))
        
        # Metadata section
        if summary_data.get('course_name'):
            content.append(Paragraph(f"Course: {summary_data['course_name']}", self.styles['metadata']))
        
        if summary_data.get('created_at'):
            try:
                # Parse the datetime string and format it nicely
                created_date = datetime.fromisoformat(summary_data['created_at'].replace('Z', '+00:00'))
                formatted_date = created_date.strftime('%B %d, %Y at %I:%M %p')
                content.append(Paragraph(f"Generated: {formatted_date}", self.styles['metadata']))
            except:
                content.append(Paragraph(f"Generated: {summary_data['created_at']}", self.styles['metadata']))
        
        content.append(Spacer(1, 18))
        
        # Key concepts section (if available)
        if summary_data.get('key_concepts') and isinstance(summary_data['key_concepts'], list):
            content.append(Paragraph("Key Concepts", self.styles['heading2']))
            for concept in summary_data['key_concepts']:
                content.append(Paragraph(f"• {concept}", self.styles['bullet']))
            content.append(Spacer(1, 12))
        
        # Main content
        main_content = summary_data.get('content', '')
        if main_content:
            content.append(Paragraph("Summary", self.styles['heading2']))
            
            # Parse and add content elements
            parsed_elements = self._parse_markdown_content(main_content)
            for element in parsed_elements:
                if element['type'] == 'spacer':
                    content.append(Spacer(1, element['height']))
                elif element['type'] in self.styles:
                    content.append(Paragraph(element['text'], self.styles[element['type']]))
        
        # Topics section (if available)
        if summary_data.get('topics') and isinstance(summary_data['topics'], list):
            content.append(Spacer(1, 18))
            content.append(Paragraph("Topics Covered", self.styles['heading2']))
            for topic in summary_data['topics']:
                content.append(Paragraph(f"• {topic}", self.styles['bullet']))
        
        return content
    
    def generate_summary_pdf(self, summary_data: Dict[str, Any]) -> bytes:
        """
        Generate a PDF document from summary data.
        
        Args:
            summary_data: Dictionary containing summary information
            
        Returns:
            PDF content as bytes
        """
        try:
            # Create a BytesIO buffer to hold the PDF
            buffer = io.BytesIO()
            
            # Create the PDF document
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=72
            )
            
            # Create content
            content = self._create_pdf_content(summary_data)
            
            # Build the PDF
            doc.build(content)
            
            # Get the PDF bytes
            pdf_bytes = buffer.getvalue()
            buffer.close()
            
            logger.info(f"Successfully generated PDF for summary: {summary_data.get('title', 'Unknown')}")
            return pdf_bytes
            
        except Exception as e:
            logger.error(f"Error generating PDF: {str(e)}")
            raise
    
    def generate_filename(self, summary_data: Dict[str, Any]) -> str:
        """Generate a safe filename for the PDF."""
        title = summary_data.get('title', 'summary')
        course_name = summary_data.get('course_name', '')
        
        # Create base filename
        if course_name:
            base_name = f"{course_name}_{title}"
        else:
            base_name = title
        
        # Clean the filename (remove special characters)
        safe_name = re.sub(r'[^a-zA-Z0-9_\-\s]', '', base_name)
        safe_name = re.sub(r'\s+', '_', safe_name)
        safe_name = safe_name.lower()
        
        # Add timestamp if needed
        timestamp = datetime.now().strftime('%Y%m%d')
        
        return f"{safe_name}_{timestamp}.pdf"


# Create a global instance
pdf_generation_service = PDFGenerationService()
