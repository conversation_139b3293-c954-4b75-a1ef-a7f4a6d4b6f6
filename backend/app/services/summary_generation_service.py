"""
Summary Generation Service

This service handles the generation of high-quality summaries from PDF notes
using the multiagent system architecture with caching and optimization.
"""

import logging
import asyncio
import time
from typing import Dict, List, Optional, Any

from sqlalchemy.orm import Session

from app.models.student_tools import ProcessingStatus
from app.schemas.student_tools import (
    NoteSummaryCreate,
    NoteGeneratedSummaryCreate,
    SummaryGenerationJobCreate,
    SummaryGenerationJobUpdate
)
from app.crud.crud_student_tools import (
    student_note,
    note_summary,
    note_generated_summary,
    summary_generation_job
)
from app.services.summary_multiagent_service import (
    summary_multiagent_service,
    SummaryMultiAgentConfig
)
from app.utils.vector_store import vector_store
from app.core.config import settings

logger = logging.getLogger(__name__)


class SummaryGenerationService:
    """Service for generating summaries from student notes."""

    def __init__(self):
        self.cache = {}  # Simple in-memory cache for content
        self.multiagent_threshold = 1  # Always use multiagent for summaries
        
    async def generate_summary_from_notes(
        self,
        note_ids: List[int],
        student_id: int,
        course_name: Optional[str],
        db: Session,
        job_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Generate summary from student notes using multiagent system.
        
        Args:
            note_ids: List of note IDs to summarize
            student_id: ID of the student
            course_name: Name of the course (optional)
            db: Database session
            job_id: Optional job ID for tracking
            
        Returns:
            Dictionary with summary generation results
        """
        try:
            logger.info(f"Starting summary generation for notes {note_ids}")
            start_time = time.time()
            
            # Update job status to processing
            if job_id:
                self._update_job_status(db, job_id, ProcessingStatus.PROCESSING, 10)
            
            # Get note contents
            note_contents = await self._get_note_contents(note_ids, db)
            if not note_contents:
                error_msg = "No valid note contents found"
                logger.error(error_msg)
                if job_id:
                    self._update_job_status(db, job_id, ProcessingStatus.FAILED, 0, error_msg)
                return {"error": error_msg}
            
            # Update progress
            if job_id:
                self._update_job_status(db, job_id, ProcessingStatus.GENERATING, 30)
            
            # Determine course name if not provided
            if not course_name:
                course_name = self._determine_course_name(note_ids, db)
            
            # Generate summary using multiagent system
            config = SummaryMultiAgentConfig(
                timeout_per_agent=60,
                retry_attempts=2,
                enable_parallel_processing=True
            )
            
            summary_result = await summary_multiagent_service.generate_summary_multiagent(
                note_contents=note_contents,
                course_name=course_name,
                config=config
            )
            
            if not summary_result:
                error_msg = "Failed to generate summary using multiagent system"
                logger.error(error_msg)
                if job_id:
                    self._update_job_status(db, job_id, ProcessingStatus.FAILED, 0, error_msg)
                return {"error": error_msg}
            
            # Update progress
            if job_id:
                self._update_job_status(db, job_id, ProcessingStatus.GENERATING, 70)
            
            # Save summary to database
            summary_id = await self._save_summary_to_db(
                summary_result, student_id, course_name, note_ids, db, job_id
            )
            
            # Update job status to completed
            if job_id:
                self._update_job_status(
                    db, job_id, ProcessingStatus.COMPLETED, 100, 
                    "Summary generated successfully", [summary_id]
                )
            
            execution_time = time.time() - start_time
            logger.info(f"Summary generation completed in {execution_time:.2f}s")
            
            return {
                "summary_id": summary_id,
                "summary": summary_result,
                "execution_time": execution_time,
                "note_count": len(note_ids),
                "course_name": course_name
            }
            
        except Exception as e:
            error_msg = f"Error in summary generation: {str(e)}"
            logger.error(error_msg)
            if job_id:
                self._update_job_status(db, job_id, ProcessingStatus.FAILED, 0, error_msg)
            return {"error": error_msg}
    
    async def _get_note_contents(self, note_ids: List[int], db: Session) -> List[str]:
        """Get content from notes using vector store or direct text extraction."""
        note_contents = []
        
        for note_id in note_ids:
            try:
                # Check cache first
                cache_key = f"note_content_{note_id}"
                if cache_key in self.cache:
                    note_contents.append(self.cache[cache_key])
                    continue
                
                # Get note from database
                note = student_note.get(db, id=note_id)
                if not note:
                    logger.warning(f"Note {note_id} not found")
                    continue
                
                # Try to get content from vector store first
                content = await self._get_content_from_vector_store(note_id)
                
                if not content:
                    # Fallback: extract content directly from PDF
                    content = await self._extract_content_from_pdf(note.file_path)
                
                if content:
                    # Cache the content
                    self.cache[cache_key] = content
                    note_contents.append(content)
                    logger.info(f"Retrieved content for note {note_id} ({len(content)} chars)")
                else:
                    logger.warning(f"No content found for note {note_id}")
                    
            except Exception as e:
                logger.error(f"Error getting content for note {note_id}: {str(e)}")
                continue
        
        return note_contents
    
    async def _get_content_from_vector_store(self, note_id: int) -> Optional[str]:
        """Get content from vector store using note ID."""
        try:
            # Search for all chunks belonging to this note
            # Use a generic query since we're filtering by metadata
            results = vector_store.search(
                query="content",  # Generic query
                limit=50,  # Get more chunks to ensure we get all content
                filter_condition=[
                    {"key": "note_id", "match": {"value": str(note_id)}}
                ]
            )

            if results:
                # Sort by chunk_index if available, then combine
                sorted_results = sorted(
                    results,
                    key=lambda x: x.get("metadata", {}).get("chunk_index", 0)
                )
                content_parts = [result["text"] for result in sorted_results]
                return "\n\n".join(content_parts)
        except Exception as e:
            logger.warning(f"Error retrieving from vector store: {str(e)}")

        return None
    
    async def _extract_content_from_pdf(self, file_path: str) -> Optional[str]:
        """Extract content directly from PDF file."""
        try:
            from app.services.pdf_processing_service import pdf_processing_service
            content = await pdf_processing_service.extract_text_from_pdf(file_path)
            return content
        except Exception as e:
            logger.error(f"Error extracting content from PDF {file_path}: {str(e)}")
            return None
    
    def _determine_course_name(self, note_ids: List[int], db: Session) -> Optional[str]:
        """Determine course name from notes."""
        try:
            for note_id in note_ids:
                note = student_note.get(db, id=note_id)
                if note and note.course_name:
                    return note.course_name
        except Exception as e:
            logger.warning(f"Error determining course name: {str(e)}")
        
        return None
    
    async def _save_summary_to_db(
        self,
        summary_result: Dict[str, Any],
        student_id: int,
        course_name: Optional[str],
        note_ids: List[int],
        db: Session,
        job_id: Optional[int] = None
    ) -> int:
        """Save generated summary to database."""
        try:
            # Create summary record
            summary_data = NoteSummaryCreate(
                student_id=student_id,
                title=summary_result.get("title", "Generated Summary"),
                content=summary_result.get("content", ""),
                key_concepts=summary_result.get("key_concepts", []),
                structure=summary_result.get("structure", {}),
                course_name=course_name,
                topics=summary_result.get("topics", []),
                job_id=job_id
            )
            
            summary = note_summary.create(db, obj_in=summary_data)
            
            # Create note-summary relationships
            for note_id in note_ids:
                relationship_data = NoteGeneratedSummaryCreate(
                    note_id=note_id,
                    summary_id=summary.id
                )
                note_generated_summary.create(db, obj_in=relationship_data)
            
            logger.info(f"Saved summary {summary.id} for notes {note_ids}")
            return summary.id
            
        except Exception as e:
            logger.error(f"Error saving summary to database: {str(e)}")
            raise
    
    def _update_job_status(
        self,
        db: Session,
        job_id: int,
        status: ProcessingStatus,
        progress: int,
        error_message: Optional[str] = None,
        summary_ids: Optional[List[int]] = None
    ):
        """Update job status in database."""
        try:
            job = summary_generation_job.get(db, id=job_id)
            if job:
                update_data = SummaryGenerationJobUpdate(
                    status=status,
                    progress_percentage=progress,
                    error_message=error_message,
                    generated_summary_ids=summary_ids
                )
                summary_generation_job.update(db, db_obj=job, obj_in=update_data)
                logger.info(f"Updated job {job_id} status to {status} ({progress}%)")
        except Exception as e:
            logger.error(f"Error updating job status: {str(e)}")
    
    async def generate_summary_for_single_note(
        self,
        note_id: int,
        student_id: int,
        db: Session
    ) -> Dict[str, Any]:
        """
        Generate summary for a single note (convenience method).
        
        Args:
            note_id: ID of the note to summarize
            student_id: ID of the student
            db: Database session
            
        Returns:
            Dictionary with summary generation results
        """
        # Get note to determine course name
        note = student_note.get(db, id=note_id)
        if not note:
            return {"error": "Note not found"}
        
        if note.student_id != student_id:
            return {"error": "Access denied"}
        
        # Generate summary
        return await self.generate_summary_from_notes(
            note_ids=[note_id],
            student_id=student_id,
            course_name=note.course_name,
            db=db
        )
    
    def get_cached_content(self, note_id: int) -> Optional[str]:
        """Get cached content for a note."""
        cache_key = f"note_content_{note_id}"
        return self.cache.get(cache_key)
    
    def clear_cache(self):
        """Clear the content cache."""
        self.cache.clear()
        logger.info("Summary generation cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cached_items": len(self.cache),
            "cache_keys": list(self.cache.keys())
        }


# Create singleton instance
summary_generation_service = SummaryGenerationService()
