import logging
import uuid
import asyncio
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, timezone

from qdrant_client import QdrantClient
from qdrant_client.http import models as qdrant_models
import openai

from app.core.config import settings
from app.utils.vector_store import vector_store

logger = logging.getLogger(__name__)


class EnhancedVectorStore:
    """Enhanced vector store with chunk-specific functionality and metadata tracking."""
    
    def __init__(self):
        self.client = QdrantClient(url=settings.QDRANT_URL, timeout=5.0)
        self.collection_name = settings.QDRANT_COLLECTION
        self.chunk_collection_name = f"{settings.QDRANT_COLLECTION}_chunks"
        self._ensure_collections_exist()
    
    def _ensure_collections_exist(self) -> None:
        """Ensure that both main and chunk collections exist."""
        try:
            collections = self.client.get_collections().collections
            collection_names = [collection.name for collection in collections]
            
            # Create main collection if it doesn't exist
            if self.collection_name not in collection_names:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=qdrant_models.VectorParams(
                        size=1536,  # OpenAI embedding dimension
                        distance=qdrant_models.Distance.COSINE,
                    ),
                )
                logger.info(f"Created main collection: {self.collection_name}")
            
            # Create chunk collection if it doesn't exist
            if self.chunk_collection_name not in collection_names:
                self.client.create_collection(
                    collection_name=self.chunk_collection_name,
                    vectors_config=qdrant_models.VectorParams(
                        size=1536,  # OpenAI embedding dimension
                        distance=qdrant_models.Distance.COSINE,
                    ),
                )
                logger.info(f"Created chunk collection: {self.chunk_collection_name}")
                
        except Exception as e:
            logger.error(f"Error ensuring collections exist: {str(e)}")
    
    async def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using OpenAI's embedding model."""
        try:
            # Limit text length for embedding
            if len(text) > 8000:  # OpenAI's limit is ~8191 tokens
                text = text[:8000]
            
            client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
            response = await asyncio.to_thread(
                client.embeddings.create,
                model="text-embedding-3-small",
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error getting embedding: {str(e)}")
            return [0.0] * 1536  # Return zeros with correct dimension
    
    async def add_document_chunks(
        self,
        document_id: str,
        chunks: List[Dict[str, Any]],
        document_metadata: Optional[Dict] = None
    ) -> List[str]:
        """
        Add document chunks to the vector store with metadata.
        
        Args:
            document_id: Unique identifier for the document
            chunks: List of chunk dictionaries with content and metadata
            document_metadata: Additional metadata for the document
            
        Returns:
            List of chunk IDs that were successfully added
        """
        try:
            chunk_ids = []
            points = []
            
            for i, chunk in enumerate(chunks):
                # Use UUID format for Qdrant compatibility
                import uuid
                chunk_id = str(uuid.uuid4())
                chunk_content = chunk.get('content', '')
                
                if not chunk_content.strip():
                    continue
                
                # Get embedding for chunk
                embedding = await self.get_embedding(chunk_content)
                
                # Prepare metadata
                metadata = {
                    'document_id': document_id,
                    'chunk_index': i,
                    'original_chunk_id': f"{document_id}_chunk_{i}",  # Keep original ID for reference
                    'content': chunk_content,
                    'token_count': chunk.get('token_count', 0),
                    'question_quota': chunk.get('question_quota', 0),
                    'section_heading': chunk.get('section_heading'),
                    'section_level': chunk.get('section_level', 0),
                    'weight': chunk.get('weight', 0.0),
                    'created_at': datetime.now(timezone.utc).isoformat(),
                    **(document_metadata or {}),
                    **(chunk.get('metadata', {}))
                }
                
                points.append(qdrant_models.PointStruct(
                    id=chunk_id,
                    vector=embedding,
                    payload=metadata
                ))
                
                chunk_ids.append(chunk_id)
            
            # Batch insert chunks
            if points:
                await asyncio.to_thread(
                    self.client.upsert,
                    collection_name=self.chunk_collection_name,
                    points=points
                )
                logger.info(f"Added {len(points)} chunks for document {document_id}")
            
            return chunk_ids
            
        except Exception as e:
            logger.error(f"Error adding document chunks: {str(e)}")
            return []
    
    async def search_relevant_chunks(
        self,
        query: str,
        limit: int = 10,
        document_id: Optional[str] = None,
        min_quota: Optional[int] = None,
        section_level: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for relevant chunks based on query and filters.
        
        Args:
            query: Search query
            limit: Maximum number of results
            document_id: Filter by specific document
            min_quota: Minimum question quota for chunks
            section_level: Filter by section level
            
        Returns:
            List of relevant chunks with metadata
        """
        try:
            # Get query embedding
            query_embedding = await self.get_embedding(query)
            
            # Build filter conditions
            filter_conditions = []
            
            if document_id:
                filter_conditions.append(
                    qdrant_models.FieldCondition(
                        key="document_id",
                        match=qdrant_models.MatchValue(value=document_id)
                    )
                )
            
            if min_quota is not None:
                filter_conditions.append(
                    qdrant_models.FieldCondition(
                        key="question_quota",
                        range=qdrant_models.Range(gte=min_quota)
                    )
                )
            
            if section_level is not None:
                filter_conditions.append(
                    qdrant_models.FieldCondition(
                        key="section_level",
                        match=qdrant_models.MatchValue(value=section_level)
                    )
                )
            
            # Create filter
            query_filter = None
            if filter_conditions:
                query_filter = qdrant_models.Filter(must=filter_conditions)
            
            # Search
            search_result = await asyncio.to_thread(
                self.client.search,
                collection_name=self.chunk_collection_name,
                query_vector=query_embedding,
                limit=limit,
                query_filter=query_filter
            )
            
            # Format results
            results = []
            for result in search_result:
                chunk_data = {
                    'chunk_id': str(result.id),
                    'score': result.score,
                    'content': result.payload.get('content', ''),
                    'document_id': result.payload.get('document_id'),
                    'chunk_index': result.payload.get('chunk_index'),
                    'token_count': result.payload.get('token_count', 0),
                    'question_quota': result.payload.get('question_quota', 0),
                    'section_heading': result.payload.get('section_heading'),
                    'section_level': result.payload.get('section_level', 0),
                    'weight': result.payload.get('weight', 0.0),
                    'metadata': {k: v for k, v in result.payload.items() 
                               if k not in ['content', 'document_id', 'chunk_index', 
                                          'token_count', 'question_quota', 'section_heading', 
                                          'section_level', 'weight']}
                }
                results.append(chunk_data)
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching relevant chunks: {str(e)}")
            return []
    
    async def get_chunks_by_document(
        self, 
        document_id: str,
        sort_by: str = 'chunk_index'
    ) -> List[Dict[str, Any]]:
        """
        Get all chunks for a specific document.
        
        Args:
            document_id: Document identifier
            sort_by: Field to sort by ('chunk_index', 'weight', 'question_quota')
            
        Returns:
            List of chunks for the document
        """
        try:
            # Search with document filter
            filter_condition = qdrant_models.Filter(
                must=[
                    qdrant_models.FieldCondition(
                        key="document_id",
                        match=qdrant_models.MatchValue(value=document_id)
                    )
                ]
            )
            
            # Get all chunks (use high limit)
            search_result = await asyncio.to_thread(
                self.client.scroll,
                collection_name=self.chunk_collection_name,
                scroll_filter=filter_condition,
                limit=1000  # Adjust based on expected chunk count
            )
            
            # Format and sort results
            chunks = []
            for point in search_result[0]:  # scroll returns (points, next_page_offset)
                chunk_data = {
                    'chunk_id': str(point.id),
                    'content': point.payload.get('content', ''),
                    'document_id': point.payload.get('document_id'),
                    'chunk_index': point.payload.get('chunk_index'),
                    'token_count': point.payload.get('token_count', 0),
                    'question_quota': point.payload.get('question_quota', 0),
                    'section_heading': point.payload.get('section_heading'),
                    'section_level': point.payload.get('section_level', 0),
                    'weight': point.payload.get('weight', 0.0),
                    'metadata': {k: v for k, v in point.payload.items() 
                               if k not in ['content', 'document_id', 'chunk_index', 
                                          'token_count', 'question_quota', 'section_heading', 
                                          'section_level', 'weight']}
                }
                chunks.append(chunk_data)
            
            # Sort chunks
            if sort_by == 'chunk_index':
                chunks.sort(key=lambda x: x['chunk_index'])
            elif sort_by == 'weight':
                chunks.sort(key=lambda x: x['weight'], reverse=True)
            elif sort_by == 'question_quota':
                chunks.sort(key=lambda x: x['question_quota'], reverse=True)
            
            return chunks
            
        except Exception as e:
            logger.error(f"Error getting chunks by document: {str(e)}")
            return []
    
    async def delete_document_chunks(self, document_id: str) -> bool:
        """
        Delete all chunks for a specific document.
        
        Args:
            document_id: Document identifier
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get all chunk IDs for the document
            chunks = await self.get_chunks_by_document(document_id)
            chunk_ids = [chunk['chunk_id'] for chunk in chunks]
            
            if chunk_ids:
                await asyncio.to_thread(
                    self.client.delete,
                    collection_name=self.chunk_collection_name,
                    points_selector=qdrant_models.PointIdsList(points=chunk_ids)
                )
                logger.info(f"Deleted {len(chunk_ids)} chunks for document {document_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document chunks: {str(e)}")
            return False


# Create singleton instance
enhanced_vector_store = EnhancedVectorStore()
