# Import all the models, so that Base has them before being
# imported by <PERSON>embic
from app.db.base_class import Base  # noqa
from app.models.user import User  # noqa
from app.models.school import School  # noqa
from app.models.department import Department  # noqa
from app.models.course import Course  # noqa
from app.models.question import Question  # noqa
from app.models.session import Session  # noqa
from app.models.tutor import <PERSON>torProfile, TutorReview  # noqa
from app.models.student_progress import StudentQuestionAttempt, StudentBookmark, StudentExam  # noqa
from app.models.gamification import Badge, UserBadge, PointsTransaction, UserLevel, UserStreak  # noqa
from app.models.ai_analytics import AIAssistantInteraction  # noqa
from app.models.student_tools import StudentNote, NoteGeneratedMCQ, MCQGenerationJob  # noqa
