"""
Middleware to configure file upload limits for FastAPI.
"""
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import FastAP<PERSON>, Request, Response
import logging

logger = logging.getLogger(__name__)

class FileUploadConfig(BaseHTTPMiddleware):
    """
    Middleware to configure file upload limits for FastAPI.
    
    This middleware sets the maximum file upload size for the application.
    """
    
    def __init__(
        self,
        app: FastAPI,
        max_upload_size: int = 30 * 1024 * 1024,  # 30MB default
    ):
        super().__init__(app)
        self.max_upload_size = max_upload_size
        logger.info(f"File upload limit set to {self.max_upload_size / (1024 * 1024):.1f}MB")
        
    async def dispatch(self, request: Request, call_next):
        """
        Process the request and set the maximum upload size.
        
        Args:
            request: The incoming request
            call_next: The next middleware or route handler
            
        Returns:
            The response from the next middleware or route handler
        """
        # Set the maximum upload size in the request state
        request.state.max_upload_size = self.max_upload_size
        
        # Continue processing the request
        response = await call_next(request)
        return response
