from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.student_tools import (
    StudentNote,
    NoteGeneratedMCQ,
    MC<PERSON>GenerationJob,
    FlashCard,
    NoteGeneratedFlashCard,
    FlashCardGenerationJob,
    NoteSummary,
    NoteGeneratedSummary,
    SummaryGenerationJob
)
from app.schemas.student_tools import (
    StudentNoteCreate,
    StudentNoteUpdate,
    NoteGeneratedMCQCreate,
    MCQGenerationJobCreate,
    MCQGenerationJobUpdate,
    FlashCardCreate,
    FlashCardUpdate,
    NoteGeneratedFlashCardCreate,
    FlashCardGenerationJobCreate,
    FlashCardGenerationJobUpdate,
    NoteSummaryCreate,
    NoteSummaryUpdate,
    NoteGeneratedSummaryCreate,
    SummaryGenerationJobCreate,
    SummaryGenerationJobUpdate
)


class CRUDStudentNote(CRUDBase[StudentNote, StudentNoteCreate, StudentNoteUpdate]):
    """CRUD operations for StudentNote model."""

    def get_by_student(
        self, db: Session, *, student_id: int, skip: int = 0, limit: int = 100
    ) -> List[StudentNote]:
        """Get notes by student ID."""
        return (
            db.query(self.model)
            .filter(self.model.student_id == student_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )


class CRUDNoteGeneratedMCQ(CRUDBase[NoteGeneratedMCQ, NoteGeneratedMCQCreate, NoteGeneratedMCQCreate]):
    """CRUD operations for NoteGeneratedMCQ model."""

    def get_by_note(
        self, db: Session, *, note_id: int, skip: int = 0, limit: int = 100
    ) -> List[NoteGeneratedMCQ]:
        """Get MCQs by note ID."""
        return (
            db.query(self.model)
            .filter(self.model.note_id == note_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_question(
        self, db: Session, *, question_id: int
    ) -> List[NoteGeneratedMCQ]:
        """Get note links by question ID."""
        return (
            db.query(self.model)
            .filter(self.model.question_id == question_id)
            .all()
        )


class CRUDMCQGenerationJob(CRUDBase[MCQGenerationJob, MCQGenerationJobCreate, MCQGenerationJobUpdate]):
    """CRUD operations for MCQGenerationJob model."""

    def get_by_student(
        self, db: Session, *, student_id: int, skip: int = 0, limit: int = 100
    ) -> List[MCQGenerationJob]:
        """Get jobs by student ID."""
        return (
            db.query(self.model)
            .filter(self.model.student_id == student_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )


class CRUDFlashCard(CRUDBase[FlashCard, FlashCardCreate, FlashCardUpdate]):
    """CRUD operations for FlashCard model."""

    def get_by_student(
        self, db: Session, *, student_id: int, skip: int = 0, limit: int = 100
    ) -> List[FlashCard]:
        """Get flash cards by student ID."""
        return (
            db.query(self.model)
            .filter(self.model.student_id == student_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_course(
        self, db: Session, *, course_name: str, skip: int = 0, limit: int = 100
    ) -> List[FlashCard]:
        """Get flash cards by course name."""
        return (
            db.query(self.model)
            .filter(self.model.course_name == course_name)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_job(
        self, db: Session, *, job_id: int, student_id: int = None, skip: int = 0, limit: int = 100
    ) -> List[FlashCard]:
        """Get flash cards by job ID."""
        print(f"Looking for flash cards with job_id={job_id}, student_id={student_id}")

        # First check if the job exists
        from app.crud.crud_student_tools import flash_card_generation_job
        job = flash_card_generation_job.get(db, id=job_id)
        if not job:
            print(f"Job with ID {job_id} not found")
            return []

        print(f"Job found: {job.id}, status: {job.status}, generated_card_ids: {job.generated_card_ids}")

        # If the job has generated_card_ids, use those directly
        if job.generated_card_ids:
            cards = []
            for card_id in job.generated_card_ids:
                card = self.get(db, id=card_id)
                if card and (student_id is None or card.student_id == student_id):
                    cards.append(card)
            print(f"Found {len(cards)} cards using generated_card_ids")
            return cards

        # Otherwise, query by job_id
        query = db.query(self.model).filter(self.model.job_id == job_id)

        if student_id is not None:
            query = query.filter(self.model.student_id == student_id)

        cards = (
            query
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        print(f"Found {len(cards)} cards using job_id filter")
        return cards


class CRUDNoteGeneratedFlashCard(CRUDBase[NoteGeneratedFlashCard, NoteGeneratedFlashCardCreate, NoteGeneratedFlashCardCreate]):
    """CRUD operations for NoteGeneratedFlashCard model."""

    def get_by_note(
        self, db: Session, *, note_id: int, skip: int = 0, limit: int = 100
    ) -> List[NoteGeneratedFlashCard]:
        """Get flash cards by note ID."""
        return (
            db.query(self.model)
            .filter(self.model.note_id == note_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_flash_card(
        self, db: Session, *, flash_card_id: int
    ) -> List[NoteGeneratedFlashCard]:
        """Get note links by flash card ID."""
        return (
            db.query(self.model)
            .filter(self.model.flash_card_id == flash_card_id)
            .all()
        )


class CRUDFlashCardGenerationJob(CRUDBase[FlashCardGenerationJob, FlashCardGenerationJobCreate, FlashCardGenerationJobUpdate]):
    """CRUD operations for FlashCardGenerationJob model."""

    def get_by_student(
        self, db: Session, *, student_id: int, skip: int = 0, limit: int = 100
    ) -> List[FlashCardGenerationJob]:
        """Get jobs by student ID."""
        return (
            db.query(self.model)
            .filter(self.model.student_id == student_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )


class CRUDNoteSummary(CRUDBase[NoteSummary, NoteSummaryCreate, NoteSummaryUpdate]):
    """CRUD operations for NoteSummary model."""

    def get_by_student(
        self, db: Session, *, student_id: int, skip: int = 0, limit: int = 100
    ) -> List[NoteSummary]:
        """Get summaries by student ID."""
        return (
            db.query(self.model)
            .filter(self.model.student_id == student_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_course(
        self, db: Session, *, course_name: str, skip: int = 0, limit: int = 100
    ) -> List[NoteSummary]:
        """Get summaries by course name."""
        return (
            db.query(self.model)
            .filter(self.model.course_name == course_name)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_job(
        self, db: Session, *, job_id: int, student_id: int = None, skip: int = 0, limit: int = 100
    ) -> List[NoteSummary]:
        """Get summaries by job ID."""
        query = db.query(self.model).filter(self.model.job_id == job_id)

        if student_id is not None:
            query = query.filter(self.model.student_id == student_id)

        return (
            query
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )


class CRUDNoteGeneratedSummary(CRUDBase[NoteGeneratedSummary, NoteGeneratedSummaryCreate, NoteGeneratedSummaryCreate]):
    """CRUD operations for NoteGeneratedSummary model."""

    def get_by_note(
        self, db: Session, *, note_id: int, skip: int = 0, limit: int = 100
    ) -> List[NoteGeneratedSummary]:
        """Get summaries by note ID."""
        return (
            db.query(self.model)
            .filter(self.model.note_id == note_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_summary(
        self, db: Session, *, summary_id: int
    ) -> List[NoteGeneratedSummary]:
        """Get note links by summary ID."""
        return (
            db.query(self.model)
            .filter(self.model.summary_id == summary_id)
            .all()
        )


class CRUDSummaryGenerationJob(CRUDBase[SummaryGenerationJob, SummaryGenerationJobCreate, SummaryGenerationJobUpdate]):
    """CRUD operations for SummaryGenerationJob model."""

    def get_by_student(
        self, db: Session, *, student_id: int, skip: int = 0, limit: int = 100
    ) -> List[SummaryGenerationJob]:
        """Get jobs by student ID."""
        return (
            db.query(self.model)
            .filter(self.model.student_id == student_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )


student_note = CRUDStudentNote(StudentNote)
note_generated_mcq = CRUDNoteGeneratedMCQ(NoteGeneratedMCQ)
mcq_generation_job = CRUDMCQGenerationJob(MCQGenerationJob)
flash_card = CRUDFlashCard(FlashCard)
note_generated_flash_card = CRUDNoteGeneratedFlashCard(NoteGeneratedFlashCard)
flash_card_generation_job = CRUDFlashCardGenerationJob(FlashCardGenerationJob)
note_summary = CRUDNoteSummary(NoteSummary)
note_generated_summary = CRUDNoteGeneratedSummary(NoteGeneratedSummary)
summary_generation_job = CRUDSummaryGenerationJob(SummaryGenerationJob)
