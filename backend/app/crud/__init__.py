from app.crud.crud_student_tools import (
    student_note,
    note_generated_mcq,
    mcq_generation_job,
    flash_card,
    note_generated_flash_card,
    flash_card_generation_job
)

# These imports will be uncommented when the modules are created
# from app.crud.crud_user import user
# from app.crud.crud_school import school
# from app.crud.crud_department import department
# from app.crud.crud_course import course
# from app.crud.crud_question import question
# from app.crud.crud_session import session
# from app.crud.crud_student_progress import student_question_attempt, student_bookmark, student_exam, student_exam_attempt
# from app.crud.crud_gamification import badge, user_badge, points_transaction, user_level, user_streak
# from app.crud.crud_ai_analytics import ai_assistant_interaction
