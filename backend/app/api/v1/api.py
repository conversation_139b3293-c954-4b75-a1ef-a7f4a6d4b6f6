from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, schools, departments, courses, questions, sessions, tutors, ai, student_progress, ai_assistant, gamification, student_tools, debug, admin

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(schools.router, prefix="/schools", tags=["schools"])
api_router.include_router(departments.router, prefix="/departments", tags=["departments"])
api_router.include_router(courses.router, prefix="/courses", tags=["courses"])
api_router.include_router(questions.router, prefix="/questions", tags=["questions"])
api_router.include_router(sessions.router, prefix="/sessions", tags=["sessions"])
api_router.include_router(tutors.router, prefix="/tutors", tags=["tutors"])
api_router.include_router(ai.router, prefix="/ai", tags=["ai"])
api_router.include_router(ai_assistant.router, prefix="/ai-assistant", tags=["ai-assistant"])
api_router.include_router(student_tools.router, prefix="/student-tools", tags=["student-tools"])
api_router.include_router(student_progress.router, prefix="/student-progress", tags=["student-progress"])
api_router.include_router(gamification.router, prefix="/gamification", tags=["gamification"])
api_router.include_router(debug.router, prefix="/debug", tags=["debug"])
