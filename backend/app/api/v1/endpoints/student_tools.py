from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, status
from fastapi.responses import Response
from sqlalchemy.orm import Session

from app.schemas.student_tools import (
    StudentNoteUploadResponse,
    StudentNote,
    MCQGenerationResponse,
    MCQGenerationStatusResponse,
    MCQGenerationJob,
    MCQGenerationRequest,
    ExamConfigRequest,
    ExamConfigResponse,
    FlashCardGenerationResponse,
    FlashCardGenerationStatusResponse,
    FlashCardGenerationJob,
    FlashCardGenerationRequest,
    FlashCard,
    SummaryGenerationResponse,
    SummaryGenerationStatusResponse,
    SummaryGenerationRequest,
    NoteSummary
)
from app.api import deps
from app.models.user import User, UserRole
from app.services.student_tools_service import student_tools_service
from app.services.pdf_generation_service import pdf_generation_service

router = APIRouter()


@router.post("/notes/upload", response_model=List[StudentNoteUploadResponse])
async def upload_notes(
    files: List[UploadFile] = File(...),
    course_name: Optional[str] = Form(None),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Upload PDF notes for processing.

    - **files**: List of PDF files to upload (max 3)
    - **course_name**: Optional course name entered by the student
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can upload notes"
        )

    # Process the uploads
    responses = await student_tools_service.upload_notes(files, current_user.id, db, course_name)
    return responses


@router.get("/notes", response_model=List[StudentNote])
def get_notes(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get all notes uploaded by the current student.
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access notes"
        )

    # Get notes
    from app.crud.crud_student_tools import student_note
    notes = student_note.get_by_student(db, student_id=current_user.id, skip=skip, limit=limit)
    return notes


@router.get("/notes/{note_id}", response_model=StudentNote)
def get_note(
    note_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get a specific note by ID.
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access notes"
        )

    # Get note
    from app.crud.crud_student_tools import student_note
    note = student_note.get(db, id=note_id)

    # Check if note exists and belongs to the current user
    if not note or note.student_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found"
        )

    return note


@router.delete("/notes/{note_id}")
def delete_note(
    note_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Delete a specific note while preserving generated content.

    This will delete the note file and remove associations with generated content,
    but the MCQs, flash cards, and summaries themselves will be preserved.

    - **note_id**: ID of the note to delete
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can delete notes"
        )

    # Get note
    from app.crud.crud_student_tools import student_note
    note = student_note.get(db, id=note_id)

    # Check if note exists and belongs to the current user
    if not note or note.student_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found"
        )

    try:
        # Import CRUD classes for handling foreign key constraints
        from app.crud.crud_student_tools import (
            note_generated_mcq,
            note_generated_flash_card,
            note_generated_summary
        )

        # Delete only the linking records (keep the actual generated content)
        # This removes the association between the note and generated content
        # but preserves the MCQs, flash cards, and summaries themselves

        # Delete MCQ associations (but keep the questions)
        mcq_links = note_generated_mcq.get_by_note(db, note_id=note_id)
        for link in mcq_links:
            note_generated_mcq.remove(db, id=link.id)

        # Delete flash card associations (but keep the flash cards)
        flash_card_links = note_generated_flash_card.get_by_note(db, note_id=note_id)
        for link in flash_card_links:
            note_generated_flash_card.remove(db, id=link.id)

        # Delete summary associations (but keep the summaries)
        summary_links = note_generated_summary.get_by_note(db, note_id=note_id)
        for link in summary_links:
            note_generated_summary.remove(db, id=link.id)

        # Now delete the note itself
        deleted_note = student_note.remove(db, id=note_id)

        # TODO: Clean up physical file if needed
        # import os
        # if os.path.exists(note.file_path):
        #     os.remove(note.file_path)

        return {"message": "Note deleted successfully", "note_id": note_id}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting note: {str(e)}"
        )


@router.post("/mcq/generate", response_model=MCQGenerationResponse)
async def generate_mcqs(
    request: MCQGenerationRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Generate MCQs from uploaded notes.

    - **note_ids**: List of note IDs to use for generation
    - **course_name**: Optional course name (if not provided, will use course name from notes)
    - **question_count**: Number of questions to generate (default: 60)
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can generate MCQs"
        )

    # Validate question count
    if request.question_count < 1 or request.question_count > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Question count must be between 1 and 100"
        )

    # Generate MCQs
    response = await student_tools_service.generate_mcqs(
        request.note_ids,
        current_user.id,
        request.course_name,
        request.question_count,
        db
    )

    if response.job_id == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.message
        )

    return response


@router.get("/mcq/job/{job_id}", response_model=MCQGenerationStatusResponse)
async def get_mcq_generation_status(
    job_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get the status of an MCQ generation job.

    - **job_id**: ID of the MCQ generation job
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access MCQ generation jobs"
        )

    # Get job status
    status_response = await student_tools_service.get_job_status(job_id, current_user.id, db)

    if not status_response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="MCQ generation job not found"
        )

    # Add debugging information
    print(f"MCQ generation job status: {status_response.status}")
    print(f"MCQ generation job progress: {status_response.progress_percentage}%")
    print(f"MCQ generation job course name: {status_response.course_name}")
    print(f"MCQ generation job question count: {status_response.question_count}")

    if status_response.generated_question_ids:
        print(f"MCQ generation job has {len(status_response.generated_question_ids)} generated questions")
        # Check if the questions exist in the database
        from app.services import question_service
        for question_id in status_response.generated_question_ids[:5]:  # Check first 5 questions
            question = question_service.get(db, id=question_id)
            if question:
                print(f"Question {question_id} exists: {question.content[:50]}...")
            else:
                print(f"Question {question_id} does not exist in the database")
    else:
        print("MCQ generation job has no generated questions")

    return status_response


@router.get("/mcq/jobs", response_model=List[MCQGenerationJob])
def get_mcq_generation_jobs(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get all MCQ generation jobs for the current student.
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access MCQ generation jobs"
        )

    # Get jobs
    from app.crud.crud_student_tools import mcq_generation_job
    jobs = mcq_generation_job.get_by_student(db, student_id=current_user.id, skip=skip, limit=limit)
    return jobs


@router.delete("/mcq/job/{job_id}")
def delete_mcq_generation_job(
    job_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Delete an entire MCQ generation job and all its associated questions.

    - **job_id**: ID of the MCQ generation job to delete
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can delete MCQ generation jobs"
        )

    # Get the MCQ generation job
    from app.crud.crud_student_tools import mcq_generation_job
    job = mcq_generation_job.get(db, id=job_id)

    # Check if job exists and belongs to the current user
    if not job or job.student_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="MCQ generation job not found"
        )

    try:
        print(f"Attempting to delete MCQ generation job {job_id}")
        print(f"Job has generated_question_ids: {job.generated_question_ids}")

        # Get all MCQ IDs associated with this job
        if job.generated_question_ids and len(job.generated_question_ids) > 0:
            from app.crud.crud_questions import question as question_crud
            from app.crud.crud_student_tools import note_generated_mcq

            print(f"Deleting {len(job.generated_question_ids)} questions")

            for mcq_id in job.generated_question_ids:
                try:
                    print(f"Deleting MCQ {mcq_id}")
                    # Delete note associations for this MCQ
                    mcq_links = note_generated_mcq.get_by_question(db, question_id=mcq_id)
                    print(f"Found {len(mcq_links)} note links for MCQ {mcq_id}")
                    for link in mcq_links:
                        note_generated_mcq.remove(db, id=link.id)

                    # Delete the MCQ question itself
                    question_crud.remove(db, id=mcq_id)
                    print(f"Successfully deleted MCQ {mcq_id}")
                except Exception as mcq_error:
                    # Log the error but continue with other questions
                    print(f"Error deleting MCQ {mcq_id}: {str(mcq_error)}")
                    continue
        else:
            print("No generated questions to delete")

        # Delete the generation job
        print(f"Deleting generation job {job_id}")
        deleted_job = mcq_generation_job.remove(db, id=job_id)
        print(f"Successfully deleted generation job {job_id}")

        return {"message": "MCQ generation job and all associated questions deleted successfully", "job_id": job_id}

    except Exception as e:
        print(f"Error in delete_mcq_generation_job: {str(e)}")
        import traceback
        traceback.print_exc()
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting MCQ generation job: {str(e)}"
        )


@router.delete("/mcq/{mcq_id}")
def delete_mcq(
    mcq_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Delete a specific MCQ question.

    - **mcq_id**: ID of the MCQ question to delete
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can delete MCQs"
        )

    # Get the MCQ question
    from app.crud.crud_questions import question as question_crud
    mcq = question_crud.get(db, id=mcq_id)

    # Check if MCQ exists and belongs to the current user
    if not mcq or mcq.created_by_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="MCQ not found"
        )

    try:
        # Delete any note associations first
        from app.crud.crud_student_tools import note_generated_mcq
        mcq_links = note_generated_mcq.get_by_question(db, question_id=mcq_id)
        for link in mcq_links:
            note_generated_mcq.remove(db, id=link.id)

        # Delete the MCQ question itself
        deleted_mcq = question_crud.remove(db, id=mcq_id)

        return {"message": "MCQ deleted successfully", "mcq_id": mcq_id}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting MCQ: {str(e)}"
        )


@router.post("/exam/config", response_model=ExamConfigResponse)
def set_exam_config(
    config: ExamConfigRequest,
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Set exam configuration for time limit and question count.

    - **time_limit**: Time limit in minutes (default: 60)
    - **question_count**: Number of questions (default: 60)
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can configure exams"
        )

    # Validate time limit (between 5 minutes and 3 hours)
    if config.time_limit < 5 or config.time_limit > 180:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Time limit must be between 5 and 180 minutes"
        )

    # Validate question count (between 1 and 100)
    if config.question_count < 1 or config.question_count > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Question count must be between 1 and 100"
        )

    # Return the configuration
    return ExamConfigResponse(
        time_limit=config.time_limit,
        question_count=config.question_count
    )


@router.post("/flash-cards/generate", response_model=FlashCardGenerationResponse)
async def generate_flash_cards(
    request: FlashCardGenerationRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Generate flash cards from uploaded notes.

    - **note_ids**: List of note IDs to use for generation
    - **course_name**: Optional course name (if not provided, will use course name from notes)
    - **card_count**: Number of flash cards to generate (default: 30)
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can generate flash cards"
        )

    # Validate card count
    if request.card_count < 1 or request.card_count > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Card count must be between 1 and 100"
        )

    # Generate flash cards
    response = await student_tools_service.generate_flash_cards(
        request.note_ids,
        current_user.id,
        request.course_name,
        request.card_count,
        db
    )

    if response.job_id == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.message
        )

    return response




@router.get("/flash-cards/job/{job_id}", response_model=FlashCardGenerationStatusResponse)
async def get_flash_card_generation_status(
    job_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get the status of a flash card generation job.

    - **job_id**: ID of the flash card generation job
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access flash card generation jobs"
        )

    # Get job status
    status_response = await student_tools_service.get_flash_card_job_status(job_id, current_user.id, db)

    if not status_response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Flash card generation job not found"
        )

    # Add debugging information
    print(f"Flash card generation job status: {status_response.status}")
    print(f"Flash card generation job progress: {status_response.progress_percentage}%")
    print(f"Flash card generation job course name: {status_response.course_name}")
    print(f"Flash card generation job card count: {status_response.card_count}")

    if status_response.generated_card_ids:
        print(f"Flash card generation job has {len(status_response.generated_card_ids)} generated cards")
        # Check if the cards exist in the database
        from app.crud.crud_student_tools import flash_card
        for card_id in status_response.generated_card_ids[:5]:  # Check first 5 cards
            card = flash_card.get(db, id=card_id)
            if card:
                print(f"Card {card_id} exists: {card.front_content[:50]}...")
            else:
                print(f"Card {card_id} does not exist in the database")
    else:
        print("Flash card generation job has no generated cards")

    return status_response


@router.get("/flash-cards/jobs", response_model=List[FlashCardGenerationJob])
def get_flash_card_generation_jobs(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get all flash card generation jobs for the current student.
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access flash card generation jobs"
        )

    # Get jobs
    from app.crud.crud_student_tools import flash_card_generation_job
    jobs = flash_card_generation_job.get_by_student(db, student_id=current_user.id, skip=skip, limit=limit)
    return jobs


@router.get("/flash-cards", response_model=List[FlashCard])
def get_flash_cards(
    skip: int = 0,
    limit: int = 100,
    job_id: Optional[int] = None,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get flash cards for the current student.

    - **job_id**: Optional job ID to filter flash cards by generation job
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access flash cards"
        )

    # Get flash cards
    from app.crud.crud_student_tools import flash_card, flash_card_generation_job

    if job_id:
        # Get the job to verify it belongs to the current user
        job = flash_card_generation_job.get(db, id=job_id)
        if not job or job.student_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Flash card generation job not found"
            )

        # Check if the job is completed
        if job.status != "COMPLETED":
            return []

        # Get flash cards by job_id directly
        cards = flash_card.get_by_job(db, job_id=job_id, student_id=current_user.id)

        # Log the number of cards found for debugging
        print(f"Found {len(cards)} flash cards for job ID {job_id}")

        # If no cards were found, try to get them by course_name
        if not cards and job.course_name:
            print(f"No cards found by job_id, trying to get by course_name: {job.course_name}")
            cards = flash_card.get_by_course(db, course_name=job.course_name)
            print(f"Found {len(cards)} flash cards for course_name {job.course_name}")

            # Filter by student_id
            cards = [card for card in cards if card.student_id == current_user.id]
            print(f"After filtering by student_id, found {len(cards)} flash cards")

        return cards
    else:
        # Get all flash cards for the student
        cards = flash_card.get_by_student(db, student_id=current_user.id, skip=skip, limit=limit)
        return cards


@router.delete("/flash-cards/job/{job_id}")
def delete_flash_card_generation_job(
    job_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Delete an entire flash card generation job and all its associated cards.

    - **job_id**: ID of the flash card generation job to delete
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can delete flash card generation jobs"
        )

    # Get the flash card generation job
    from app.crud.crud_student_tools import flash_card_generation_job
    job = flash_card_generation_job.get(db, id=job_id)

    # Check if job exists and belongs to the current user
    if not job or job.student_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Flash card generation job not found"
        )

    try:
        # Get all flash card IDs associated with this job
        if job.generated_card_ids and len(job.generated_card_ids) > 0:
            from app.crud.crud_student_tools import flash_card as flash_card_crud, note_generated_flash_card

            for card_id in job.generated_card_ids:
                try:
                    # Delete note associations for this flash card
                    card_links = note_generated_flash_card.get_by_flash_card(db, flash_card_id=card_id)
                    for link in card_links:
                        note_generated_flash_card.remove(db, id=link.id)

                    # Delete the flash card itself
                    flash_card_crud.remove(db, id=card_id)
                except Exception as card_error:
                    # Log the error but continue with other cards
                    print(f"Error deleting flash card {card_id}: {str(card_error)}")
                    continue

        # Delete the generation job
        deleted_job = flash_card_generation_job.remove(db, id=job_id)

        return {"message": "Flash card generation job and all associated cards deleted successfully", "job_id": job_id}

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting flash card generation job: {str(e)}"
        )


@router.delete("/flash-cards/{card_id}")
def delete_flash_card(
    card_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Delete a specific flash card.

    - **card_id**: ID of the flash card to delete
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can delete flash cards"
        )

    # Get the flash card
    from app.crud.crud_student_tools import flash_card as flash_card_crud
    card = flash_card_crud.get(db, id=card_id)

    # Check if flash card exists and belongs to the current user
    if not card or card.student_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Flash card not found"
        )

    try:
        # Delete any note associations first
        from app.crud.crud_student_tools import note_generated_flash_card
        card_links = note_generated_flash_card.get_by_flash_card(db, flash_card_id=card_id)
        for link in card_links:
            note_generated_flash_card.remove(db, id=link.id)

        # Delete the flash card itself
        deleted_card = flash_card_crud.remove(db, id=card_id)

        return {"message": "Flash card deleted successfully", "card_id": card_id}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting flash card: {str(e)}"
        )


@router.post("/notes/{note_id}/summarize", response_model=SummaryGenerationResponse)
async def generate_summary_for_note(
    note_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Generate a comprehensive summary for a specific note using multiagent system.

    - **note_id**: ID of the note to summarize
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can generate summaries"
        )

    # Generate summary
    try:
        response = await student_tools_service.generate_summary_for_single_note(
            note_id=note_id,
            student_id=current_user.id,
            db=db
        )

        if response.job_id == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=response.message
            )

        return response
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error generating summary for note {note_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/summaries/generate", response_model=SummaryGenerationResponse)
async def generate_summaries(
    request: SummaryGenerationRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Generate summaries from multiple notes using multiagent system.

    - **note_ids**: List of note IDs to use for summary generation
    - **course_name**: Optional course name (if not provided, will use course name from notes)
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can generate summaries"
        )

    # Generate summaries
    response = await student_tools_service.generate_summary(
        note_ids=request.note_ids,
        student_id=current_user.id,
        course_name=request.course_name,
        db=db
    )

    if response.job_id == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=response.message
        )

    return response


@router.get("/summaries/status/{job_id}", response_model=SummaryGenerationStatusResponse)
async def get_summary_generation_status(
    job_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get the status of a summary generation job.

    - **job_id**: ID of the summary generation job
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can check summary generation status"
        )

    # Get job status
    status_response = await student_tools_service.get_summary_job_status(job_id, current_user.id, db)

    if not status_response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Summary generation job not found"
        )

    return status_response


@router.get("/summaries", response_model=List[NoteSummary])
def get_summaries(
    skip: int = 0,
    limit: int = 100,
    job_id: Optional[int] = None,
    course_name: Optional[str] = None,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get summaries for the current student.

    - **job_id**: Optional job ID to filter summaries by generation job
    - **course_name**: Optional course name to filter summaries
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access summaries"
        )

    # Get summaries
    from app.crud.crud_student_tools import note_summary

    if job_id:
        summaries = note_summary.get_by_job(db, job_id=job_id, student_id=current_user.id, skip=skip, limit=limit)
    elif course_name:
        summaries = note_summary.get_by_course(db, course_name=course_name, skip=skip, limit=limit)
        # Filter by student_id
        summaries = [summary for summary in summaries if summary.student_id == current_user.id]
    else:
        summaries = note_summary.get_by_student(db, student_id=current_user.id, skip=skip, limit=limit)

    return summaries


@router.get("/summaries/{summary_id}/download-pdf")
def download_summary_as_pdf(
    summary_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Download a summary as a PDF file.

    - **summary_id**: ID of the summary to download
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can download summaries"
        )

    # Get the summary
    from app.crud.crud_student_tools import note_summary

    summary = note_summary.get(db, id=summary_id)
    if not summary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Summary not found"
        )

    # Check if the summary belongs to the current user
    if summary.student_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )

    try:
        # Prepare summary data for PDF generation
        summary_data = {
            'id': summary.id,
            'title': summary.title,
            'content': summary.content,
            'key_concepts': summary.key_concepts,
            'structure': summary.structure,
            'course_name': summary.course_name,
            'topics': summary.topics,
            'created_at': summary.created_at.isoformat() if summary.created_at else None,
            'updated_at': summary.updated_at.isoformat() if summary.updated_at else None
        }

        # Generate PDF
        pdf_bytes = pdf_generation_service.generate_summary_pdf(summary_data)

        # Generate filename
        filename = pdf_generation_service.generate_filename(summary_data)

        # Return PDF as response
        return Response(
            content=pdf_bytes,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating PDF: {str(e)}"
        )


@router.delete("/summaries/{summary_id}")
def delete_summary(
    summary_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Delete a specific summary.

    - **summary_id**: ID of the summary to delete
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can delete summaries"
        )

    # Get the summary
    from app.crud.crud_student_tools import note_summary as summary_crud
    summary = summary_crud.get(db, id=summary_id)

    # Check if summary exists and belongs to the current user
    if not summary or summary.student_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Summary not found"
        )

    try:
        # Delete any note associations first
        from app.crud.crud_student_tools import note_generated_summary
        summary_links = note_generated_summary.get_by_summary(db, summary_id=summary_id)
        for link in summary_links:
            note_generated_summary.remove(db, id=link.id)

        # Delete the summary itself
        deleted_summary = summary_crud.remove(db, id=summary_id)

        return {"message": "Summary deleted successfully", "summary_id": summary_id}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting summary: {str(e)}"
        )
