from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app import schemas
from app.api import deps
from app.models.user import User, UserRole
from app.services.tutor_service import tutor_profile_service, tutor_review_service
from app.schemas.tutor import (
    TutorProfile, TutorProfileCreate, TutorProfileUpdate,
    TutorReview, TutorReviewCreate, TutorReviewUpdate,
    TutorDashboardStats, TutorSearchFilters, TutorListItem
)

router = APIRouter()


@router.get("/", response_model=List[TutorProfile])
def get_tutors(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve tutors.
    """
    tutors = tutor_profile_service.get_multi(db, skip=skip, limit=limit)
    return tutors


@router.post("/search", response_model=List[TutorProfile])
def search_tutors(
    *,
    db: Session = Depends(deps.get_db),
    filters: TutorSearchFilters,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Search tutors with filters.
    """
    tutors = tutor_profile_service.search_tutors(
        db, filters=filters, skip=skip, limit=limit
    )
    return tutors


@router.get("/me", response_model=TutorProfile)
def get_my_tutor_profile(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Get current tutor's profile.
    """
    tutor_profile = tutor_profile_service.get_by_user_id(db, user_id=current_user.id)
    if not tutor_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tutor profile not found"
        )
    return tutor_profile


@router.post("/me", response_model=TutorProfile)
def create_my_tutor_profile(
    *,
    db: Session = Depends(deps.get_db),
    profile_in: TutorProfileCreate,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Create tutor profile for current user.
    """
    # Check if profile already exists
    existing_profile = tutor_profile_service.get_by_user_id(db, user_id=current_user.id)
    if existing_profile:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tutor profile already exists"
        )
    
    tutor_profile = tutor_profile_service.create_for_user(
        db, obj_in=profile_in, user_id=current_user.id
    )
    return tutor_profile


@router.put("/me", response_model=TutorProfile)
def update_my_tutor_profile(
    *,
    db: Session = Depends(deps.get_db),
    profile_in: TutorProfileUpdate,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Update current tutor's profile.
    """
    tutor_profile = tutor_profile_service.get_by_user_id(db, user_id=current_user.id)
    if not tutor_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tutor profile not found"
        )
    
    tutor_profile = tutor_profile_service.update_with_specializations(
        db, db_obj=tutor_profile, obj_in=profile_in
    )
    return tutor_profile


@router.get("/me/dashboard", response_model=TutorDashboardStats)
def get_my_dashboard_stats(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Get dashboard statistics for current tutor.
    """
    tutor_profile = tutor_profile_service.get_by_user_id(db, user_id=current_user.id)
    if not tutor_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tutor profile not found"
        )
    
    stats = tutor_profile_service.get_dashboard_stats(db, tutor_profile.id)
    return stats


@router.get("/{id}", response_model=TutorProfile)
def get_tutor(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get tutor by ID.
    """
    tutor = tutor_profile_service.get_with_details(db, id=id)
    if not tutor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tutor not found"
        )
    return tutor


# Review endpoints
@router.post("/{tutor_id}/reviews", response_model=TutorReview)
def create_tutor_review(
    *,
    db: Session = Depends(deps.get_db),
    tutor_id: int,
    review_in: TutorReviewCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a review for a tutor.
    """
    # Ensure the tutor_profile_id matches the URL parameter
    if review_in.tutor_profile_id != tutor_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tutor ID mismatch"
        )
    
    # Only students can create reviews
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can create reviews"
        )
    
    # Check if tutor exists
    tutor = tutor_profile_service.get(db, id=tutor_id)
    if not tutor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tutor not found"
        )
    
    review = tutor_review_service.create_review(
        db, obj_in=review_in, student_id=current_user.id
    )
    return review


@router.get("/{tutor_id}/reviews", response_model=List[TutorReview])
def get_tutor_reviews(
    *,
    db: Session = Depends(deps.get_db),
    tutor_id: int,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get reviews for a tutor.
    """
    # Check if tutor exists
    tutor = tutor_profile_service.get(db, id=tutor_id)
    if not tutor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tutor not found"
        )
    
    reviews = tutor_review_service.get_reviews_for_tutor(
        db, tutor_profile_id=tutor_id, skip=skip, limit=limit
    )
    return reviews


# Admin endpoints
@router.put("/{id}", response_model=TutorProfile)
def update_tutor(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    profile_in: TutorProfileUpdate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Update tutor profile (admin only).
    """
    tutor = tutor_profile_service.get(db, id=id)
    if not tutor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tutor not found"
        )
    
    tutor = tutor_profile_service.update_with_specializations(
        db, db_obj=tutor, obj_in=profile_in
    )
    return tutor


@router.delete("/{id}", response_model=TutorProfile)
def delete_tutor(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Delete tutor profile (admin only).
    """
    tutor = tutor_profile_service.get(db, id=id)
    if not tutor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tutor not found"
        )
    
    tutor = tutor_profile_service.delete(db, id=id)
    return tutor
