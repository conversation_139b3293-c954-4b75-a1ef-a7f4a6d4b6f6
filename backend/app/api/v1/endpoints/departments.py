from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import schemas
from app.models.user import User
from app.api import deps
from app.services import department_service

router = APIRouter()


@router.get("/", response_model=List[schemas.Department])
def read_departments(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve departments.
    """
    departments = department_service.get_multi(db, skip=skip, limit=limit)
    return departments


@router.get("/school/{school_id}", response_model=List[schemas.Department])
def read_departments_by_school(
    school_id: int,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve departments by school.
    """
    departments = department_service.get_by_school(
        db, school_id=school_id, skip=skip, limit=limit
    )
    return departments


@router.get("/public/school/{school_id}", response_model=List[schemas.Department])
def read_departments_by_school_public(
    school_id: int,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Retrieve departments by school without authentication (for registration).
    """
    departments = department_service.get_by_school(
        db, school_id=school_id, skip=skip, limit=limit
    )
    return departments


@router.post("/", response_model=schemas.Department)
def create_department(
    *,
    db: Session = Depends(deps.get_db),
    department_in: schemas.DepartmentCreate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Create new department.
    """
    department = department_service.create(db, obj_in=department_in)
    return department


@router.get("/{id}", response_model=schemas.Department)
def read_department(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get department by ID.
    """
    department = department_service.get(db, id=id)
    if not department:
        raise HTTPException(status_code=404, detail="Department not found")
    return department


@router.put("/{id}", response_model=schemas.Department)
def update_department(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    department_in: schemas.DepartmentUpdate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Update a department.
    """
    department = department_service.get(db, id=id)
    if not department:
        raise HTTPException(status_code=404, detail="Department not found")
    department = department_service.update(db, db_obj=department, obj_in=department_in)
    return department


@router.delete("/{id}", response_model=schemas.Department)
def delete_department(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Delete a department.
    """
    department = department_service.get(db, id=id)
    if not department:
        raise HTTPException(status_code=404, detail="Department not found")
    department = department_service.delete(db, id=id)
    return department
