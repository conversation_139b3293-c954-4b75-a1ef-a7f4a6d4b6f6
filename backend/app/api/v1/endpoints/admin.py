from typing import Any, Dict, List
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from sqlalchemy import func

from app import schemas
from app.api import deps
from app.core import security
from app.core.config import settings
from app.services import user_service, school_service, department_service, course_service
from app.models.user import User, UserRole
from app.models.school import School
from app.models.department import Department
from app.models.course import Course
from app.models.question import Question

router = APIRouter()


@router.post("/login", response_model=schemas.Token)
def admin_login(
    db: Session = Depends(deps.get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    Admin-specific login endpoint with enhanced security checks
    """
    # First check if user exists
    user_exists = user_service.get_by_email(db, email=form_data.username)

    if not user_exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No admin account found with this email address.",
        )

    # Check if user is admin
    if user_exists.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied. Admin privileges required.",
        )

    # Authenticate
    user = user_service.authenticate(
        db, email=form_data.username, password=form_data.password
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid admin credentials.",
            headers={"WWW-Authenticate": "Bearer"},
        )
    elif not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Admin account has been deactivated."
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    return {
        "access_token": security.create_access_token(
            user.id, expires_delta=access_token_expires
        ),
        "token_type": "bearer",
    }


@router.get("/dashboard/stats", response_model=Dict[str, Any])
def get_admin_dashboard_stats(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Get comprehensive dashboard statistics for admin
    """
    # Get counts for all major entities
    total_users = db.query(func.count(User.id)).scalar()
    total_students = db.query(func.count(User.id)).filter(User.role == UserRole.STUDENT).scalar()
    total_tutors = db.query(func.count(User.id)).filter(User.role == UserRole.TUTOR).scalar()
    total_admins = db.query(func.count(User.id)).filter(User.role == UserRole.ADMIN).scalar()
    
    total_schools = db.query(func.count(School.id)).scalar()
    active_schools = db.query(func.count(School.id)).filter(School.is_active == True).scalar()
    
    total_departments = db.query(func.count(Department.id)).scalar()
    active_departments = db.query(func.count(Department.id)).filter(Department.is_active == True).scalar()
    
    total_courses = db.query(func.count(Course.id)).scalar()
    active_courses = db.query(func.count(Course.id)).filter(Course.is_active == True).scalar()
    
    total_questions = db.query(func.count(Question.id)).scalar()
    active_questions = db.query(func.count(Question.id)).filter(Question.is_active == True).scalar()

    # Get recent activity (users created in last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    recent_users = db.query(func.count(User.id)).filter(User.created_at >= thirty_days_ago).scalar()

    return {
        "users": {
            "total": total_users,
            "students": total_students,
            "tutors": total_tutors,
            "admins": total_admins,
            "recent": recent_users
        },
        "schools": {
            "total": total_schools,
            "active": active_schools,
            "inactive": total_schools - active_schools
        },
        "departments": {
            "total": total_departments,
            "active": active_departments,
            "inactive": total_departments - active_departments
        },
        "courses": {
            "total": total_courses,
            "active": active_courses,
            "inactive": total_courses - active_courses
        },
        "questions": {
            "total": total_questions,
            "active": active_questions,
            "inactive": total_questions - active_questions
        }
    }


@router.get("/users/bulk-actions", response_model=List[schemas.User])
def get_users_for_bulk_actions(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user),
    role: UserRole = None,
    is_active: bool = None,
    skip: int = 0,
    limit: int = 1000,
) -> Any:
    """
    Get users with filtering for bulk operations
    """
    query = db.query(User)
    
    if role:
        query = query.filter(User.role == role)
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    
    users = query.offset(skip).limit(limit).all()
    return users


@router.put("/users/bulk-update")
def bulk_update_users(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user),
    user_ids: List[int],
    update_data: schemas.UserUpdate,
) -> Any:
    """
    Bulk update multiple users
    """
    updated_count = 0
    errors = []
    
    for user_id in user_ids:
        try:
            user = user_service.get(db, id=user_id)
            if user:
                user_service.update(db, db_obj=user, obj_in=update_data)
                updated_count += 1
            else:
                errors.append(f"User {user_id} not found")
        except Exception as e:
            errors.append(f"Error updating user {user_id}: {str(e)}")
    
    return {
        "updated_count": updated_count,
        "errors": errors,
        "success": len(errors) == 0
    }
