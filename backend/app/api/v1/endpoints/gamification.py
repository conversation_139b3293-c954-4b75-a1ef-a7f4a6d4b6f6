from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import text

from app import schemas
from app.api import deps
from app.models.user import User, UserRole
from app.services import gamification_service

router = APIRouter()


@router.get("/badges", response_model=List[Dict[str, Any]])
def get_user_badges(
    *,
    db: Session = Depends(deps.get_db),
    user_id: Optional[int] = Query(None, description="User ID (defaults to current user)"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get badges earned by a user.
    """
    # If no user_id is provided, use the current user's ID
    if user_id is None:
        user_id = current_user.id
    # Only allow students to view their own badges
    elif current_user.role == UserRole.STUDENT and user_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Students can only view their own badges"
        )

    return gamification_service.get_user_badges(db, user_id=user_id)


@router.get("/points", response_model=int)
def get_user_points(
    *,
    db: Session = Depends(deps.get_db),
    user_id: Optional[int] = Query(None, description="User ID (defaults to current user)"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get total points for a user.
    """
    # If no user_id is provided, use the current user's ID
    if user_id is None:
        user_id = current_user.id
    # Only allow students to view their own points
    elif current_user.role == UserRole.STUDENT and user_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Students can only view their own points"
        )

    return gamification_service.get_user_points(db, user_id=user_id)


@router.get("/profile", response_model=schemas.UserGamificationProfile)
def get_gamification_profile(
    *,
    db: Session = Depends(deps.get_db),
    user_id: Optional[int] = Query(None, description="User ID (defaults to current user)"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get complete gamification profile for a user.
    """
    # If no user_id is provided, use the current user's ID
    if user_id is None:
        user_id = current_user.id
    # Only allow students to view their own profile
    elif current_user.role == UserRole.STUDENT and user_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Students can only view their own gamification profile"
        )

    # Get user's total points
    total_points = gamification_service.get_user_points(db, user_id=user_id)

    # Get user's current level
    current_level = gamification_service.get_level_for_points(total_points)

    # Get user's badges
    badges = gamification_service.get_user_badges(db, user_id=user_id)

    # Get user's streak
    streak = db.query(gamification_service.UserStreak).filter(
        gamification_service.UserStreak.user_id == user_id
    ).first()

    # Calculate next level and points needed
    next_level = None
    points_to_next_level = None

    for i, level in enumerate(gamification_service.LEVELS):
        if level["level_number"] == current_level["level_number"] and i < len(gamification_service.LEVELS) - 1:
            next_level = gamification_service.LEVELS[i + 1]
            points_to_next_level = next_level["min_points"] - total_points
            break

    return {
        "total_points": total_points,
        "current_level": current_level,
        "badges": badges,
        "streak": streak,
        "next_level": next_level,
        "points_to_next_level": points_to_next_level
    }


@router.get("/leaderboard", response_model=schemas.Leaderboard)
def get_leaderboard(
    *,
    db: Session = Depends(deps.get_db),
    limit: int = Query(10, description="Number of users to include in the leaderboard"),
    school_id: Optional[int] = Query(None, description="Filter by school ID"),
    department_id: Optional[int] = Query(None, description="Filter by department ID"),
    course_id: Optional[int] = Query(None, description="Filter by course ID"),
    time_period: Optional[str] = Query(None, description="Filter by time period ('daily', 'weekly', 'all_time')"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get the points leaderboard with optional filtering.

    - **limit**: Number of users to include in the leaderboard
    - **school_id**: Filter by school ID
    - **department_id**: Filter by department ID
    - **course_id**: Filter by course ID
    - **time_period**: Filter by time period ('daily', 'weekly', 'all_time')
    """
    # Check if there are any points transactions
    points_count = db.execute(text("SELECT COUNT(*) FROM pointstransaction")).scalar()
    print(f"Total points transactions: {points_count}")

    # If no points transactions exist, create some for testing
    if points_count == 0:
        print("No points transactions found, creating some for testing")
        # Get all student users
        students = db.query(User).filter((User.role == 'STUDENT') | (User.role == 'student')).all()

        # Create random points for each student
        import random
        from datetime import datetime, timezone
        from app.models.gamification import PointsTransaction

        for student in students:
            # Create 1-5 random point transactions
            for _ in range(random.randint(1, 5)):
                points = random.randint(5, 100)
                transaction = PointsTransaction(
                    user_id=student.id,
                    points=points,
                    description=f"Test points for leaderboard",
                    transaction_type="test",
                    created_at=datetime.now(timezone.utc)
                )
                db.add(transaction)

        # Also create some badges for users
        badge_count = db.execute(text("SELECT COUNT(*) FROM badge")).scalar()
        if badge_count == 0:
            print("No badges found, creating some for testing")
            from app.models.gamification import Badge, BadgeType, UserBadge, UserStreak

            # Create some badges
            badges = [
                Badge(name="First Steps", description="Started learning journey", badge_type=BadgeType.ACHIEVEMENT),
                Badge(name="Quick Learner", description="Completed 5 practice questions", badge_type=BadgeType.ACHIEVEMENT),
                Badge(name="Exam Master", description="Scored 100% on an exam", badge_type=BadgeType.ACHIEVEMENT),
                Badge(name="Consistent Learner", description="Maintained a 3-day streak", badge_type=BadgeType.STREAK),
            ]
            for badge in badges:
                db.add(badge)

            db.commit()

            # Assign random badges to students
            badges = db.query(Badge).all()
            for student in students:
                # Assign 0-3 random badges
                for badge in random.sample(badges, random.randint(0, min(3, len(badges)))):
                    user_badge = UserBadge(
                        user_id=student.id,
                        badge_id=badge.id,
                        awarded_at=datetime.now(timezone.utc)
                    )
                    db.add(user_badge)

            # Create streaks for students
            streak_count = db.execute(text("SELECT COUNT(*) FROM userstreak")).scalar()
            if streak_count == 0:
                print("No streaks found, creating some for testing")
                for student in students:
                    # Create a streak with random days (0-7)
                    streak_days = random.randint(0, 7)
                    user_streak = UserStreak(
                        user_id=student.id,
                        current_streak=streak_days,
                        longest_streak=max(streak_days, random.randint(1, 10)),
                        last_activity_date=datetime.now(timezone.utc)
                    )
                    db.add(user_streak)

        db.commit()
        points_count = db.execute(text("SELECT COUNT(*) FROM pointstransaction")).scalar()
        badge_count = db.execute(text("SELECT COUNT(*) FROM userbadge")).scalar()
        streak_count = db.execute(text("SELECT COUNT(*) FROM userstreak")).scalar()
        print(f"Created test data. Points: {points_count}, User badges: {badge_count}, Streaks: {streak_count}")

    # Check if there are any users with the student role
    student_count = db.execute(text("SELECT COUNT(*) FROM \"user\" WHERE role = 'STUDENT'")).scalar()
    print(f"Total students: {student_count}")

    # Print all users and their roles for debugging
    users = db.execute(text("SELECT id, full_name, email, role FROM \"user\"")).fetchall()
    print("All users:")
    for user in users:
        print(f"ID: {user.id}, Name: {user.full_name}, Email: {user.email}, Role: {user.role}")

    # Get leaderboard entries
    leaderboard_entries = gamification_service.get_leaderboard(
        db,
        limit=limit,
        school_id=school_id,
        department_id=department_id,
        course_id=course_id,
        time_period=time_period
    )

    print(f"Leaderboard entries: {len(leaderboard_entries)}")

    # If no entries, create a default entry for testing
    if not leaderboard_entries:
        print("No leaderboard entries found, creating a default entry for testing")
        # Get a student user (check both uppercase and lowercase)
        student = db.query(User).filter((User.role == 'STUDENT') | (User.role == 'student')).first()
        if student:
            # Create a default entry
            user_level = gamification_service.get_level_for_points(0)
            leaderboard_entries = [{
                "user_id": student.id,
                "full_name": student.full_name,
                "profile_picture_url": student.profile_picture_url,
                "total_points": 0,
                "badge_count": 0,
                "current_streak": 0,
                "level": user_level,
                "school_name": None,
                "department_name": None
            }]

    return {"entries": leaderboard_entries}


@router.post("/update-streak", response_model=schemas.UserStreak)
def update_user_streak(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a user's learning streak.
    This endpoint should be called whenever a user performs a learning activity.
    """
    return gamification_service.update_streak(db, user_id=current_user.id)
