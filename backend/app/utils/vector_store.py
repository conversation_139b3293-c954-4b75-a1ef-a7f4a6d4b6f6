import logging
import uuid
from typing import Dict, List, Optional, Union

import openai
from qdrant_client import QdrantClient
from qdrant_client.http import models as qdrant_models

from app.core.config import settings

logger = logging.getLogger(__name__)


class VectorStore:
    def __init__(self):
        # Set a very short timeout for faster response when vector store is unavailable
        self.client = QdrantClient(url=settings.QDRANT_URL, timeout=0.5)
        self.collection_name = settings.QDRANT_COLLECTION
        self._ensure_collection_exists()

    def _ensure_collection_exists(self) -> None:
        """Ensure that the collection exists, creating it if necessary."""
        try:
            collections = self.client.get_collections().collections
            collection_names = [collection.name for collection in collections]

            if self.collection_name not in collection_names:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=qdrant_models.VectorParams(
                        size=1536,  # OpenAI embedding dimension
                        distance=qdrant_models.Distance.COSINE,
                    ),
                )
                logger.info(f"Created collection: {self.collection_name}")
        except ConnectionError as e:
            # Handle connection error gracefully
            logger.error(f"Connection error to vector store: {str(e)}")
            logger.info("Vector store is not available, but the application will continue to function")
        except Exception as e:
            logger.error(f"Error ensuring collection exists: {str(e)}")

    def get_embedding(self, text: str) -> List[float]:
        """Get embedding for a text using OpenAI's embedding model."""
        try:
            # Limit text length to improve performance
            if len(text) > 1000:
                text = text[:1000]

            client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
            response = client.embeddings.create(
                model="text-embedding-3-small",
                input=text,
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error getting embedding: {str(e)}")
            # Return a dummy embedding instead of raising an exception
            # This allows the application to continue even if embedding fails
            return [0.0] * 1536  # Return zeros with the correct dimension

    def add_document(
        self,
        document_id: Union[str, uuid.UUID, int],
        text: str,
        metadata: Optional[Dict] = None
    ) -> bool:
        """
        Add a document to the vector store.

        Args:
            document_id: Unique identifier for the document (UUID, string, or int)
            text: Text content of the document
            metadata: Additional metadata for the document

        Returns:
            True if successful, False otherwise
        """
        try:
            embedding = self.get_embedding(text)

            # Convert document_id to a format Qdrant accepts (integer or string)
            if isinstance(document_id, uuid.UUID):
                # Convert UUID to string
                document_id = str(document_id)
            elif isinstance(document_id, str):
                # Check if it's a valid UUID string
                try:
                    # Just validate, but keep as string
                    _ = uuid.UUID(document_id)
                except ValueError:
                    # If not a valid UUID string, use a hash as an integer
                    document_id = abs(hash(document_id)) % (2**63 - 1)

            self.client.upsert(
                collection_name=self.collection_name,
                points=[
                    qdrant_models.PointStruct(
                        id=document_id,
                        vector=embedding,
                        payload={"text": text, **(metadata or {})},
                    )
                ],
            )
            return True
        except Exception as e:
            logger.error(f"Error adding document: {str(e)}")
            return False

    def search(
        self,
        query: str,
        limit: int = 5,
        filter_condition: Optional[Dict] = None
    ) -> List[Dict]:
        """
        Search for documents similar to the query.

        Args:
            query: Query text
            limit: Maximum number of results to return
            filter_condition: Filter condition for the search

        Returns:
            List of documents with their similarity scores
        """
        try:
            # Get embedding for the query
            query_embedding = self.get_embedding(query)

            try:
                # Try to search in the vector store
                search_result = self.client.search(
                    collection_name=self.collection_name,
                    query_vector=query_embedding,
                    limit=limit,
                    query_filter=qdrant_models.Filter(
                        must=[qdrant_models.FieldCondition(**cond) for cond in (filter_condition or [])]
                    ) if filter_condition else None,
                )

                return [
                    {
                        "id": str(result.id),
                        "score": result.score,
                        "text": result.payload.get("text", ""),
                        "metadata": {k: v for k, v in result.payload.items() if k != "text"},
                    }
                    for result in search_result
                ]
            except ConnectionError as e:
                # Handle connection error gracefully
                logger.error(f"Connection error to vector store: {str(e)}")
                logger.info("Continuing without vector store results")
                return []
            except Exception as e:
                # Handle other search errors
                logger.error(f"Error searching vector store: {str(e)}")
                return []
        except Exception as e:
            # Handle embedding errors
            logger.error(f"Error in search process: {str(e)}")
            return []

    def delete_document(self, document_id: Union[str, uuid.UUID, int]) -> bool:
        """
        Delete a document from the vector store.

        Args:
            document_id: ID of the document to delete (UUID, string, or int)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert document_id to a format Qdrant accepts (integer or string)
            if isinstance(document_id, uuid.UUID):
                # Convert UUID to string
                document_id = str(document_id)
            elif isinstance(document_id, str):
                # Check if it's a valid UUID string
                try:
                    # Just validate, but keep as string
                    _ = uuid.UUID(document_id)
                except ValueError:
                    # If not a valid UUID string, use a hash as an integer
                    document_id = abs(hash(document_id)) % (2**63 - 1)

            self.client.delete(
                collection_name=self.collection_name,
                points_selector=qdrant_models.PointIdsList(
                    points=[document_id],
                ),
            )
            return True
        except Exception as e:
            logger.error(f"Error deleting document: {str(e)}")
            return False


# Create a singleton instance
vector_store = VectorStore()
