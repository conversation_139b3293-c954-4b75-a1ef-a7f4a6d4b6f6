import logging
from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session

from app.core.celery_app import celery_app
from app.db.session import SessionLocal
from app.models.question import DifficultyLevel, QuestionType
from app.models.student_tools import ProcessingStatus
from app.crud.crud_student_tools import (
    student_note,
    note_generated_mcq,
    mcq_generation_job,
    flash_card,
    note_generated_flash_card,
    flash_card_generation_job,
    note_summary,
    note_generated_summary,
    summary_generation_job
)
from app.schemas.student_tools import (
    StudentNoteUpdate,
    MCQGenerationJobUpdate,
    FlashCardGenerationJobUpdate,
    SummaryGenerationJobUpdate
)
from app.services.ai_service import ai_service
from app.services.pdf_processing_service import pdf_processing_service
from app.services.mcq_generation_service import mcq_generation_service
from app.services.flash_card_generation_service import flash_card_generation_service
from app.services.summary_generation_service import summary_generation_service

logger = logging.getLogger(__name__)


@celery_app.task(name="app.tasks.process_pdf_for_questions")
def process_pdf_for_questions(
    file_path: str,
    course_id: int,
    question_type: str,
    difficulty: str,
    num_questions: int,
    created_by_id: int,
) -> List[int]:
    """
    Process a PDF file and generate questions from it.

    Args:
        file_path: Path to the uploaded PDF file
        course_id: ID of the course for which to generate questions
        question_type: Type of questions to generate
        difficulty: Difficulty level of questions to generate
        num_questions: Number of questions to generate
        created_by_id: ID of the user who created the questions

    Returns:
        List of IDs of the created questions
    """
    # This is a placeholder. In a real implementation, you would:
    # 1. Read the PDF file
    # 2. Extract text from it
    # 3. Use AI to generate questions from the text
    # 4. Save the questions to the database

    logger.info(f"Processing PDF file: {file_path}")

    # For now, we'll just return an empty list
    return []


@celery_app.task(name="app.tasks.generate_ai_questions")
async def generate_ai_questions(
    topic: str,
    course_id: int,
    question_type: str,
    difficulty: str,
    num_questions: int,
    created_by_id: int,
) -> List[int]:
    """
    Generate questions using AI based on the provided parameters.

    Args:
        topic: Topic for which to generate questions
        course_id: ID of the course for which to generate questions
        question_type: Type of questions to generate
        difficulty: Difficulty level of questions to generate
        num_questions: Number of questions to generate
        created_by_id: ID of the user who created the questions

    Returns:
        List of IDs of the created questions
    """
    logger.info(f"Generating AI questions for topic: {topic}")

    db = SessionLocal()
    try:
        # Generate questions using the AI service
        questions = await ai_service.generate_questions(
            topic=topic,
            question_type=QuestionType(question_type),
            difficulty=DifficultyLevel(difficulty),
            num_questions=num_questions
        )

        # Save questions to the database
        from app.models.question import Question

        question_ids = []
        for q in questions:
            db_question = Question(
                content=q.content,
                question_type=q.question_type,
                difficulty=q.difficulty,
                options=q.options,
                answer=q.answer,
                explanation=q.explanation,
                course_id=course_id,
                created_by_id=created_by_id
            )
            db.add(db_question)
            db.flush()  # Flush to get the ID
            question_ids.append(db_question.id)

        db.commit()
        return question_ids
    except Exception as e:
        logger.error(f"Error generating AI questions: {str(e)}")
        db.rollback()
        return []
    finally:
        db.close()


@celery_app.task(name="app.tasks.process_note")
def process_note(note_id: int) -> Dict[str, Any]:
    """
    Process a student note in the background.

    Args:
        note_id: ID of the note to process

    Returns:
        Dictionary with processing results
    """
    db = SessionLocal()
    result = {"success": False, "note_id": note_id}

    try:
        # Get the note
        note = student_note.get(db, id=note_id)
        if not note:
            logger.error(f"Note {note_id} not found")
            result["error"] = "Note not found"
            return result

        # Update status to processing
        note = student_note.update(
            db,
            db_obj=note,
            obj_in=StudentNoteUpdate(status=ProcessingStatus.PROCESSING)
        )

        # Extract text from PDF
        text = pdf_processing_service.extract_text_from_pdf_sync(note.file_path)

        # Update processing metadata
        metadata = note.processing_metadata or {}
        metadata["text_length"] = len(text)
        note = student_note.update(
            db,
            db_obj=note,
            obj_in=StudentNoteUpdate(processing_metadata=metadata)
        )

        # Detect course and topics
        course_id, topics = mcq_generation_service.detect_course_sync(text, db)

        # If no course detected, try to get the first course
        if course_id is None:
            try:
                from app.services import course_service
                courses = course_service.get_multi(db, limit=1)
                if courses:
                    course_id = courses[0].id
                    logger.info(f"No course detected, using first course: {course_id}")
            except Exception as e:
                logger.error(f"Error getting first course: {str(e)}")

        # Update note with detected topics only (not course)
        note = student_note.update(
            db,
            db_obj=note,
            obj_in=StudentNoteUpdate(
                detected_topics=topics
            )
        )

        # Update status to embedding
        note = student_note.update(
            db,
            db_obj=note,
            obj_in=StudentNoteUpdate(status=ProcessingStatus.EMBEDDING)
        )

        # Chunk text
        chunks = pdf_processing_service.chunk_text_sync(text)

        # Create embeddings
        embedding_ids = pdf_processing_service.create_embeddings_sync(chunks, note.id)

        # Update note with embedding info
        metadata["chunk_count"] = len(chunks)
        metadata["embedding_count"] = len(embedding_ids)
        note = student_note.update(
            db,
            db_obj=note,
            obj_in=StudentNoteUpdate(
                embedding_id=embedding_ids[0] if embedding_ids else None,
                processing_metadata=metadata,
                status=ProcessingStatus.COMPLETED
            )
        )

        result["success"] = True
        result["embedding_count"] = len(embedding_ids)
        return result

    except Exception as e:
        logger.error(f"Error processing note {note_id}: {str(e)}")
        # Update note status to failed
        student_note.update(
            db,
            db_obj=note,
            obj_in=StudentNoteUpdate(
                status=ProcessingStatus.FAILED,
                processing_metadata={
                    **(note.processing_metadata or {}),
                    "error": str(e)
                }
            )
        )
        result["error"] = str(e)
        return result
    finally:
        db.close()


@celery_app.task(name="app.tasks.generate_mcqs")
def generate_mcqs(job_id: int) -> Dict[str, Any]:
    """
    Generate MCQs in the background.

    Args:
        job_id: ID of the MCQ generation job

    Returns:
        Dictionary with generation results
    """
    db = SessionLocal()
    result = {"success": False, "job_id": job_id}

    try:
        # Get the job
        job = mcq_generation_job.get(db, id=job_id)
        if not job:
            logger.error(f"Job {job_id} not found")
            result["error"] = "Job not found"
            return result

        # Update status to processing
        job = mcq_generation_job.update(
            db,
            db_obj=job,
            obj_in=MCQGenerationJobUpdate(
                status=ProcessingStatus.PROCESSING,
                progress_percentage=10
            )
        )

        # Get notes
        note_contents = []
        for note_id in job.note_ids:
            note = student_note.get(db, id=note_id)
            if note and note.status == ProcessingStatus.COMPLETED:
                # Extract text from PDF
                text = pdf_processing_service.extract_text_from_pdf_sync(note.file_path)
                note_contents.append(text)

        # Update progress
        job = mcq_generation_job.update(
            db,
            db_obj=job,
            obj_in=MCQGenerationJobUpdate(progress_percentage=30)
        )

        try:
            # Use the course name from the job
            course_name = job.course_name

            # Always use multiagent approach for all question counts
            logger.info(f"Using multiagent approach for {job.question_count} questions")
            questions = mcq_generation_service.generate_mcqs_multiagent_sync(
                note_contents,
                course_name,
                job.question_count,
                db,
                use_multiagent=True
            )

            # Validate questions
            if not questions or len(questions) == 0:
                raise ValueError("No questions were generated from the notes content")

            # Log success
            logger.info(f"Successfully generated {len(questions)} questions for job {job_id}")

            # Update progress
            job = mcq_generation_job.update(
                db,
                db_obj=job,
                obj_in=MCQGenerationJobUpdate(progress_percentage=70)
            )
        except Exception as e:
            logger.error(f"Error generating MCQs for job {job_id}: {str(e)}")
            # Update job status to failed and return
            mcq_generation_job.update(
                db,
                db_obj=job,
                obj_in=MCQGenerationJobUpdate(
                    status=ProcessingStatus.FAILED,
                    error_message=f"Failed to generate questions: {str(e)}"
                )
            )
            result["error"] = str(e)
            return result

        # Create question objects
        question_ids = mcq_generation_service.create_question_objects_sync(
            questions,
            course_name,  # Use the course_name from the job
            job.student_id,
            db,
            job.id  # Pass the job ID to link questions to the job
        )

        # Create NoteGeneratedMCQ links
        for note_id in job.note_ids:
            for question_id in question_ids:
                link_data = {
                    "note_id": note_id,
                    "question_id": question_id
                }
                note_generated_mcq.create(db, obj_in=link_data)

        # Update job as completed
        logger.info(f"Updating job {job_id} status to completed with {len(question_ids)} question IDs: {question_ids}")

        try:
            # Create a new database session to avoid any potential issues with the existing one
            new_db = SessionLocal()

            # Get the job from the database again
            job_to_update = mcq_generation_job.get(new_db, id=job_id)

            if job_to_update:
                # Update the job status
                updated_job = mcq_generation_job.update(
                    new_db,
                    db_obj=job_to_update,
                    obj_in=MCQGenerationJobUpdate(
                        status=ProcessingStatus.COMPLETED,
                        progress_percentage=100,
                        generated_question_ids=question_ids
                    )
                )

                # Commit the changes
                new_db.commit()

                logger.info(f"Job {job_id} updated successfully: status={updated_job.status}, progress={updated_job.progress_percentage}%, question_ids={updated_job.generated_question_ids}")
            else:
                logger.error(f"Could not find job {job_id} in the database")

            # Close the new database session
            new_db.close()
        except Exception as e:
            logger.error(f"Error updating job {job_id} status: {str(e)}")
            # Try one more time with the original session
            try:
                job = mcq_generation_job.update(
                    db,
                    db_obj=job,
                    obj_in=MCQGenerationJobUpdate(
                        status=ProcessingStatus.COMPLETED,
                        progress_percentage=100,
                        generated_question_ids=question_ids
                    )
                )
                db.commit()
                logger.info(f"Job {job_id} updated successfully with original session")
            except Exception as e2:
                logger.error(f"Error updating job {job_id} status with original session: {str(e2)}")

        result["success"] = True
        result["question_count"] = len(question_ids)
        return result

    except Exception as e:
        logger.error(f"Error generating MCQs for job {job_id}: {str(e)}")
        # Update job status to failed
        mcq_generation_job.update(
            db,
            db_obj=job,
            obj_in=MCQGenerationJobUpdate(
                status=ProcessingStatus.FAILED,
                error_message=str(e)
            )
        )
        result["error"] = str(e)
        return result
    finally:
        db.close()


@celery_app.task(name="app.tasks.generate_flash_cards")
def generate_flash_cards(job_id: int) -> Dict[str, Any]:
    """
    Generate flash cards in the background.

    Args:
        job_id: ID of the flash card generation job

    Returns:
        Dictionary with generation results
    """
    db = SessionLocal()
    result = {"success": False, "job_id": job_id}

    try:
        # Get the job
        job = flash_card_generation_job.get(db, id=job_id)
        if not job:
            logger.error(f"Job {job_id} not found")
            result["error"] = "Job not found"
            return result

        # Update status to processing
        job = flash_card_generation_job.update(
            db,
            db_obj=job,
            obj_in=FlashCardGenerationJobUpdate(
                status=ProcessingStatus.PROCESSING,
                progress_percentage=10
            )
        )

        # Get notes
        note_contents = []
        for note_id in job.note_ids:
            note = student_note.get(db, id=note_id)
            if note and note.status == ProcessingStatus.COMPLETED:
                # Extract text from PDF
                text = pdf_processing_service.extract_text_from_pdf_sync(note.file_path)
                note_contents.append(text)

        # Update progress
        job = flash_card_generation_job.update(
            db,
            db_obj=job,
            obj_in=FlashCardGenerationJobUpdate(progress_percentage=30)
        )

        try:
            # Use the course name from the job
            course_name = job.course_name

            # Always use multiagent approach for all card counts
            logger.info(f"Using multiagent approach for {job.card_count} flashcards")
            cards = flash_card_generation_service.generate_flash_cards_multiagent_sync(
                note_contents,
                course_name,
                job.card_count,
                db,
                use_multiagent=True
            )

            # Validate cards
            if not cards or len(cards) == 0:
                raise ValueError("No flash cards were generated from the notes content")

            # Log success
            logger.info(f"Successfully generated {len(cards)} flash cards for job {job_id}")

            # Update progress
            job = flash_card_generation_job.update(
                db,
                db_obj=job,
                obj_in=FlashCardGenerationJobUpdate(progress_percentage=70)
            )
        except Exception as e:
            logger.error(f"Error generating flash cards for job {job_id}: {str(e)}")
            # Update job status to failed and return
            flash_card_generation_job.update(
                db,
                db_obj=job,
                obj_in=FlashCardGenerationJobUpdate(
                    status=ProcessingStatus.FAILED,
                    error_message=f"Failed to generate flash cards: {str(e)}"
                )
            )
            result["error"] = str(e)
            return result

        # Create flash card objects
        card_ids = flash_card_generation_service.create_flash_card_objects_sync(
            cards,
            course_name,  # Use the course_name from the job
            job.student_id,
            db,
            job.id  # Pass the job ID to link cards to the job
        )

        # Create NoteGeneratedFlashCard links
        for note_id in job.note_ids:
            for card_id in card_ids:
                link_data = {
                    "note_id": note_id,
                    "flash_card_id": card_id
                }
                note_generated_flash_card.create(db, obj_in=link_data)

        # Update job as completed
        logger.info(f"Updating job {job_id} status to completed with {len(card_ids)} card IDs: {card_ids}")

        try:
            # Create a new database session to avoid any potential issues with the existing one
            new_db = SessionLocal()

            # Get the job from the database again
            job_to_update = flash_card_generation_job.get(new_db, id=job_id)

            if job_to_update:
                # Update the job status
                updated_job = flash_card_generation_job.update(
                    new_db,
                    db_obj=job_to_update,
                    obj_in=FlashCardGenerationJobUpdate(
                        status=ProcessingStatus.COMPLETED,
                        progress_percentage=100,
                        generated_card_ids=card_ids
                    )
                )

                # Commit the changes
                new_db.commit()

                logger.info(f"Job {job_id} updated successfully: status={updated_job.status}, progress={updated_job.progress_percentage}%, card_ids={updated_job.generated_card_ids}")
            else:
                logger.error(f"Could not find job {job_id} in the database")

            # Close the new database session
            new_db.close()
        except Exception as e:
            logger.error(f"Error updating job {job_id} status: {str(e)}")
            # Try one more time with the original session
            try:
                job = flash_card_generation_job.update(
                    db,
                    db_obj=job,
                    obj_in=FlashCardGenerationJobUpdate(
                        status=ProcessingStatus.COMPLETED,
                        progress_percentage=100,
                        generated_card_ids=card_ids
                    )
                )
                db.commit()
                logger.info(f"Job {job_id} updated successfully with original session")
            except Exception as e2:
                logger.error(f"Error updating job {job_id} status with original session: {str(e2)}")

        result["success"] = True
        result["card_count"] = len(card_ids)
        return result

    except Exception as e:
        logger.error(f"Error generating flash cards for job {job_id}: {str(e)}")
        # Update job status to failed
        flash_card_generation_job.update(
            db,
            db_obj=job,
            obj_in=FlashCardGenerationJobUpdate(
                status=ProcessingStatus.FAILED,
                error_message=str(e)
            )
        )
        result["error"] = str(e)
        return result
    finally:
        db.close()



@celery_app.task(name="app.tasks.generate_summary")
def generate_summary(job_id: int) -> Dict[str, Any]:
    """
    Generate summary from student notes using multiagent system.

    Args:
        job_id: ID of the summary generation job

    Returns:
        Dictionary with generation results
    """
    db = SessionLocal()
    result = {"success": False, "job_id": job_id}

    try:
        # Get the job
        job = summary_generation_job.get(db, id=job_id)
        if not job:
            logger.error(f"Summary job {job_id} not found")
            result["error"] = "Job not found"
            return result

        logger.info(f"🚀 Starting lightning-fast summary generation for job {job_id}")
        logger.info(f"📊 Notes to process: {job.note_ids}")
        logger.info(f"📚 Course: {job.course_name or 'Not specified'}")

        # Update status to processing
        job = summary_generation_job.update(
            db,
            db_obj=job,
            obj_in=SummaryGenerationJobUpdate(
                status=ProcessingStatus.PROCESSING,
                progress_percentage=10
            )
        )

        # Generate summary using the multiagent service
        import asyncio
        import time
        start_time = time.time()

        # Run the async summary generation in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            summary_result = loop.run_until_complete(
                summary_generation_service.generate_summary_from_notes(
                    note_ids=job.note_ids,
                    student_id=job.student_id,
                    course_name=job.course_name,
                    db=db,
                    job_id=job.id
                )
            )
        finally:
            loop.close()

        generation_time = time.time() - start_time
        logger.info(f"⚡ Summary generation completed in {generation_time:.2f} seconds")

        if "error" in summary_result:
            # Update job status to failed
            summary_generation_job.update(
                db,
                db_obj=job,
                obj_in=SummaryGenerationJobUpdate(
                    status=ProcessingStatus.FAILED,
                    error_message=summary_result["error"]
                )
            )
            result["error"] = summary_result["error"]
            return result

        # Job should already be updated by the summary service
        logger.info(f"Summary generation job {job_id} completed successfully")

        result["success"] = True
        result["summary_id"] = summary_result.get("summary_id")
        result["execution_time"] = summary_result.get("execution_time")
        return result

    except Exception as e:
        logger.error(f"Error generating summary for job {job_id}: {str(e)}")
        # Update job status to failed
        try:
            job = summary_generation_job.get(db, id=job_id)
            if job:
                summary_generation_job.update(
                    db,
                    db_obj=job,
                    obj_in=SummaryGenerationJobUpdate(
                        status=ProcessingStatus.FAILED,
                        error_message=str(e)
                    )
                )
        except Exception as update_error:
            logger.error(f"Error updating job status: {str(update_error)}")

        result["error"] = str(e)
        return result
    finally:
        db.close()