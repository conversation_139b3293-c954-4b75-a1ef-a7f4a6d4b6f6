#!/usr/bin/env python3

import sqlite3
import os

def fix_database():
    """Add missing columns to the tutorprofile table"""
    db_path = 'campuspq.db'
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found!")
        return
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Connected to database successfully")

        # List all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"Available tables: {[table[0] for table in tables]}")

        # Check current table structure
        cursor.execute("PRAGMA table_info(tutorprofile)")
        columns = cursor.fetchall()
        existing_columns = [col[1] for col in columns]
        print(f"Existing columns: {existing_columns}")
        
        # Add missing columns
        columns_to_add = [
            ('location', 'VARCHAR'),
            ('gender', 'VARCHAR'),
            ('languages', 'VARCHAR')
        ]
        
        for column_name, column_type in columns_to_add:
            if column_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE tutorprofile ADD COLUMN {column_name} {column_type}")
                    print(f"Added column: {column_name}")
                except sqlite3.Error as e:
                    print(f"Error adding column {column_name}: {e}")
            else:
                print(f"Column {column_name} already exists")
        
        # Commit changes
        conn.commit()
        
        # Verify the changes
        cursor.execute("PRAGMA table_info(tutorprofile)")
        columns = cursor.fetchall()
        new_columns = [col[1] for col in columns]
        print(f"Updated columns: {new_columns}")
        
        print("Database update completed successfully!")
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    fix_database()
