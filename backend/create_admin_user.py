#!/usr/bin/env python3

import os
import sys
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.user import User, UserRole
from app.core.security import get_password_hash
from app.db.base_class import Base

def create_admin_user():
    """Create an admin user in the database"""
    
    # Create database engine
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # Create tables if they don't exist
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    
    try:
        # Admin user details
        admin_email = "<EMAIL>"
        admin_password = "admin123"  # Change this in production!
        admin_name = "System Administrator"
        
        # Check if admin user already exists
        existing_admin = db.query(User).filter(User.email == admin_email).first()
        
        if existing_admin:
            print(f"Admin user already exists:")
            print(f"  ID: {existing_admin.id}")
            print(f"  Email: {existing_admin.email}")
            print(f"  Role: {existing_admin.role}")
            print(f"  Active: {existing_admin.is_active}")
            print(f"  Email verified: {existing_admin.email_verified}")
            
            # Update admin user to ensure proper settings
            existing_admin.is_active = True
            existing_admin.email_verified = True
            existing_admin.profile_completed = True
            existing_admin.role = UserRole.ADMIN
            
            db.commit()
            print("Updated admin user settings")
            
        else:
            # Create new admin user
            hashed_password = get_password_hash(admin_password)
            
            admin_user = User(
                email=admin_email,
                hashed_password=hashed_password,
                full_name=admin_name,
                role=UserRole.ADMIN,
                is_active=True,
                email_verified=True,
                profile_completed=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)
            
            print(f"Created new admin user with ID: {admin_user.id}")
        
        print(f"\n=== ADMIN USER READY ===")
        print(f"Email: {admin_email}")
        print(f"Password: {admin_password}")
        print(f"Role: admin")
        print(f"Email verified: True")
        print(f"Profile completed: True")
        
        print(f"\n=== ADMIN LOGIN TEST ===")
        print(f"Try logging in at: http://localhost:5174/admin/login")
        print(f"Or test with curl:")
        print(f"curl -X POST 'http://localhost:8000/api/v1/admin/login' \\")
        print(f"  -H 'Content-Type: application/x-www-form-urlencoded' \\")
        print(f"  -d 'username={admin_email}&password={admin_password}'")
        
        # Also create a test student user for testing
        student_email = "<EMAIL>"
        student_password = "student123"
        
        existing_student = db.query(User).filter(User.email == student_email).first()
        
        if not existing_student:
            student_user = User(
                email=student_email,
                hashed_password=get_password_hash(student_password),
                full_name="Test Student",
                role=UserRole.STUDENT,
                is_active=True,
                email_verified=True,
                profile_completed=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.add(student_user)
            db.commit()
            
            print(f"\n=== TEST STUDENT USER CREATED ===")
            print(f"Email: {student_email}")
            print(f"Password: {student_password}")
            print(f"Role: student")
        
        # Create a test tutor user
        tutor_email = "<EMAIL>"
        tutor_password = "tutor123"
        
        existing_tutor = db.query(User).filter(User.email == tutor_email).first()
        
        if not existing_tutor:
            tutor_user = User(
                email=tutor_email,
                hashed_password=get_password_hash(tutor_password),
                full_name="Test Tutor",
                role=UserRole.TUTOR,
                is_active=True,
                email_verified=True,
                profile_completed=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.add(tutor_user)
            db.commit()
            
            print(f"\n=== TEST TUTOR USER CREATED ===")
            print(f"Email: {tutor_email}")
            print(f"Password: {tutor_password}")
            print(f"Role: tutor")
        
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()
