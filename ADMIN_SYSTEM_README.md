# CampusPQ Admin Management System

## Overview

The CampusPQ Admin Management System provides a comprehensive administrative interface for managing all aspects of the platform including users, schools, departments, courses, and questions.

## Features

### 🔐 Secure Admin Authentication
- Dedicated admin login portal at `/admin/login`
- Enhanced security checks for admin-only access
- Separate authentication flow from student/tutor login

### 📊 Admin Dashboard
- Real-time platform statistics
- User distribution analytics
- Quick access to all management features
- Platform health monitoring

### 👥 User Management
- View all users with filtering by role and status
- Individual user details and editing
- Bulk user operations (activate/deactivate, role changes)
- User creation and management

### 🏫 Institution Management
- **Schools**: Create, edit, and manage educational institutions
- **Departments**: Organize departments within schools
- **Courses**: Manage course offerings and assignments

### ❓ Content Management
- **Questions**: Create and manage question banks
- **Sessions**: Oversee tutoring sessions
- Content moderation and quality control

### 📈 Analytics & Reporting
- Platform usage statistics
- User engagement metrics
- Content performance analytics
- System health monitoring

## Getting Started

### 1. Create Admin User

Run the admin user creation script:

```bash
cd backend
python create_admin_user.py
```

This creates:
- **Admin User**: `<EMAIL>` / `admin123`
- **Test Student**: `<EMAIL>` / `student123`
- **Test Tutor**: `<EMAIL>` / `tutor123`

### 2. Access Admin Portal

1. Navigate to: `http://localhost:5174/admin/login`
2. Login with admin credentials
3. Access the admin dashboard

### 3. Admin Navigation

The admin portal includes:
- **Dashboard**: Overview and quick stats
- **Users**: User management and bulk operations
- **Schools**: Institution management
- **Departments**: Department organization
- **Courses**: Course management
- **Questions**: Content management
- **Analytics**: Platform insights
- **Settings**: System configuration

## API Endpoints

### Admin Authentication
```
POST /api/v1/admin/login
```

### Dashboard Statistics
```
GET /api/v1/admin/dashboard/stats
```

### Bulk User Operations
```
GET /api/v1/admin/users/bulk-actions
PUT /api/v1/admin/users/bulk-update
```

## Security Features

- **Role-based Access Control**: Only users with `admin` role can access
- **Enhanced Authentication**: Separate login flow with additional security checks
- **Protected Routes**: All admin routes require admin privileges
- **Audit Trail**: All admin actions are logged (future enhancement)

## File Structure

```
frontend/src/
├── pages/admin/
│   ├── AdminLogin.tsx          # Admin login page
│   ├── AdminDashboard.tsx      # Main dashboard
│   ├── AdminAnalytics.tsx      # Analytics page
│   └── BulkUserManagement.tsx  # Bulk operations
├── components/Layout/
│   └── AdminLayout.tsx         # Admin layout with navigation
├── routes/
│   └── AdminRoutes.tsx         # Admin routing
└── api/
    └── admin.ts                # Admin API client

backend/app/
├── api/v1/endpoints/
│   └── admin.py                # Admin endpoints
└── create_admin_user.py        # Admin user creation script
```

## Usage Examples

### Bulk User Management
1. Navigate to **Users** → **Bulk Management**
2. Filter users by role or status
3. Select multiple users
4. Choose bulk action (activate, deactivate, change role)
5. Confirm and apply changes

### Platform Analytics
1. Go to **Analytics** dashboard
2. View user distribution charts
3. Monitor platform health metrics
4. Track engagement rates

### Content Management
1. Access **Schools**, **Departments**, or **Courses**
2. Create new entities or edit existing ones
3. Manage relationships between entities
4. Monitor content status and activity

## Security Notes

⚠️ **Important Security Considerations:**

1. **Change Default Password**: Update the admin password from `admin123` in production
2. **Environment Variables**: Store sensitive credentials in environment variables
3. **HTTPS**: Always use HTTPS in production
4. **Access Logs**: Monitor admin access and actions
5. **Regular Audits**: Periodically review admin user accounts

## Development

### Adding New Admin Features

1. **Backend**: Add endpoints to `backend/app/api/v1/endpoints/admin.py`
2. **Frontend**: Create components in `frontend/src/pages/admin/`
3. **Routing**: Update `frontend/src/routes/AdminRoutes.tsx`
4. **Navigation**: Add to `frontend/src/components/Layout/AdminLayout.tsx`

### Testing

Test admin functionality:
```bash
# Test admin login
curl -X POST 'http://localhost:8000/api/v1/admin/login' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'username=<EMAIL>&password=admin123'

# Test dashboard stats (with valid token)
curl -X GET 'http://localhost:8000/api/v1/admin/dashboard/stats' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'
```

## Future Enhancements

- [ ] Advanced analytics and reporting
- [ ] System configuration management
- [ ] Audit logging and activity tracking
- [ ] Email notification management
- [ ] Backup and restore functionality
- [ ] Multi-admin role management
- [ ] Advanced user import/export
- [ ] Content moderation tools

## Support

For admin system support or feature requests, please contact the development team or create an issue in the project repository.
