#!/bin/bash

# CampusPQ Complete Startup Script
# This script starts all services: Backend, Frontend, Celery, Database, Redis, and Qdrant

set -e  # Exit on any error

echo "🚀 Starting CampusPQ Application Stack..."
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to wait for a service to be ready
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service_name to be ready on $host:$port..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z $host $port 2>/dev/null; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Check prerequisites
print_status "Checking prerequisites..."

if ! command_exists docker; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command_exists node; then
    print_error "Node.js is not installed. Please install Node.js first."
    print_warning "You can install Node.js using:"
    echo "  curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -"
    echo "  sudo apt-get install -y nodejs"
    exit 1
fi

if ! command_exists npm; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_success "All prerequisites are available"

# Check if ports are available
print_status "Checking port availability..."

PORTS_TO_CHECK=(3000 5173 8000 5432 6379 6333)
PORTS_IN_USE=()

for port in "${PORTS_TO_CHECK[@]}"; do
    if port_in_use $port; then
        PORTS_IN_USE+=($port)
    fi
done

if [ ${#PORTS_IN_USE[@]} -gt 0 ]; then
    print_warning "The following ports are already in use: ${PORTS_IN_USE[*]}"
    print_warning "You may need to stop other services or the startup might fail"
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "Startup cancelled by user"
        exit 1
    fi
fi

# Start backend services with Docker Compose
print_status "Starting backend services (Database, Redis, Qdrant, API, Celery)..."
cd backend

if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found in backend directory"
    exit 1
fi

# Start services in detached mode
docker compose up -d --build

print_success "Backend services started"

# Wait for services to be ready
wait_for_service localhost 5432 "PostgreSQL Database"
wait_for_service localhost 6379 "Redis"
wait_for_service localhost 6333 "Qdrant"
wait_for_service localhost 8000 "Backend API"

cd ..

# Install and start frontend
print_status "Setting up frontend..."
cd frontend

if [ ! -f "package.json" ]; then
    print_error "package.json not found in frontend directory"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    npm install
fi

# Start frontend in background
print_status "Starting frontend development server..."
npm run dev &
FRONTEND_PID=$!

cd ..

# Wait for frontend to be ready
wait_for_service localhost 5173 "Frontend Development Server"

# Display status
echo
echo "🎉 CampusPQ is now running!"
echo "=========================="
echo
print_success "Backend API: http://localhost:8000"
print_success "Frontend: http://localhost:5173"
print_success "Database (PostgreSQL): localhost:5432"
print_success "Redis: localhost:6379"
print_success "Qdrant Vector DB: http://localhost:6333"
echo
print_status "Backend services are running in Docker containers"
print_status "Frontend is running as a development server (PID: $FRONTEND_PID)"
echo
print_warning "To stop all services:"
echo "  - Frontend: kill $FRONTEND_PID"
echo "  - Backend: cd backend && docker compose down"
echo
print_warning "To view logs:"
echo "  - Backend: cd backend && docker compose logs -f"
echo "  - Frontend: Check the terminal where this script is running"
echo

# Keep script running and show logs
print_status "Showing frontend logs (Ctrl+C to stop)..."
wait $FRONTEND_PID
