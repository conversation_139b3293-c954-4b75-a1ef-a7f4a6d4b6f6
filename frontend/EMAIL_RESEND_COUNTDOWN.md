# Email Resend Countdown Feature

## Overview

This feature implements a countdown timer that prevents users from spamming email resend requests. It provides a user-friendly rate limiting mechanism for email verification and password reset functionality.

## Features

- **60-second cooldown** after each email send attempt
- **Visual countdown timer** showing remaining seconds
- **Persistent state** using localStorage (survives page refresh)
- **Automatic cleanup** when countdown expires
- **Graceful error handling** for edge cases

## Implementation

### Custom Hook: `useEmailResendCountdown`

Located at: `frontend/src/hooks/useEmailResendCountdown.ts`

#### Parameters:
- `email` (optional): Email address for unique storage key
- `cooldownDuration` (optional): Countdown duration in seconds (default: 60)

#### Returns:
- `canResend`: <PERSON><PERSON><PERSON> indicating if user can send email
- `remainingTime`: Number of seconds remaining in countdown
- `startCountdown()`: Function to start the countdown
- `resetCountdown()`: Function to reset/clear the countdown

### Usage Example

```typescript
import { useEmailResendCountdown } from '../hooks/useEmailResendCountdown';

const { canResend, remainingTime, startCountdown } = useEmailResendCountdown({
  email: '<EMAIL>',
  cooldownDuration: 60
});

const handleResendEmail = async () => {
  if (!canResend) return;
  
  try {
    await resendEmail();
    startCountdown(); // Start countdown after successful send
  } catch (error) {
    // Handle error
  }
};

// Button with countdown display
<Button 
  disabled={!canResend}
  onClick={handleResendEmail}
>
  {!canResend ? `Resend Email (${remainingTime}s)` : 'Resend Email'}
</Button>
```

## Updated Components

### 1. EmailVerification.tsx
- Added countdown for resend verification email
- Button shows countdown: "Resend Email (45s)"
- Disabled during countdown period

### 2. EmailVerificationPending.tsx  
- Added countdown for resend verification email
- Consistent behavior with EmailVerification

### 3. ForgotPassword.tsx
- Added countdown for password reset requests
- Prevents rapid successive reset requests
- Shows countdown in button text

## Storage Strategy

The countdown state is persisted in localStorage using keys like:
- `<EMAIL>`

This ensures:
- Countdown persists across page refreshes
- Each email address has independent countdown
- Automatic cleanup when countdown expires

## User Experience

### Before Countdown:
- User clicks "Resend Email"
- Email is sent immediately
- User can click again immediately (potential spam)

### After Countdown:
- User clicks "Resend Email" 
- Email is sent successfully
- Button becomes disabled with countdown: "Resend Email (60s)"
- Countdown decreases every second: "Resend Email (59s)", "Resend Email (58s)", etc.
- After 60 seconds, button becomes enabled again: "Resend Email"

## Benefits

1. **Prevents Email Spam**: Users cannot flood the system with email requests
2. **Better UX**: Clear visual feedback about when they can try again
3. **Persistent**: Works even if user refreshes page or navigates away
4. **Configurable**: Easy to adjust countdown duration
5. **Reusable**: Single hook used across multiple components

## Testing

Test file: `frontend/src/hooks/__tests__/useEmailResendCountdown.test.ts`

Tests cover:
- Initial state
- Starting countdown
- Countdown progression
- localStorage persistence
- Cleanup behavior
- Edge cases (missing email, expired countdown)

## Configuration

To change the countdown duration, modify the `cooldownDuration` parameter:

```typescript
const { canResend, remainingTime, startCountdown } = useEmailResendCountdown({
  email: email,
  cooldownDuration: 120 // 2 minutes instead of 60 seconds
});
```

## Browser Compatibility

- Uses localStorage (supported in all modern browsers)
- Uses setInterval for countdown (universal support)
- Gracefully handles localStorage unavailability

## Future Enhancements

Potential improvements:
- Server-side rate limiting integration
- Different countdown durations for different email types
- Visual progress bar instead of just text
- Sound/notification when countdown completes
- Analytics tracking for resend patterns
