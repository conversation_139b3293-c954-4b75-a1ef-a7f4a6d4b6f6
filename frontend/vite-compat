#!/bin/bash

# This script is a direct replacement for the Vite executable
# It runs Vite with the --experimental-modules flag to enable ES module support in Node.js v18

# Get the directory of this script
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Path to the original Vite CLI
VITE_CLI="$DIR/node_modules/vite/dist/node/cli.js"

# Run Vite with experimental modules flag
node --experimental-modules "$VITE_CLI" "$@"
