import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    fs: {
      // Allow serving files from one level up to the project root
      allow: [
        // Allow serving files from the project root
        path.resolve(__dirname, '..'),
      ],
    },
  },
  optimizeDeps: {
    include: ['katex'],
  },
  build: {
    commonjsOptions: {
      include: [/katex/],
    },
  },
})
