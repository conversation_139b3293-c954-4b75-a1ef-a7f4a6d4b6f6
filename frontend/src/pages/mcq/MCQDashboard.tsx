import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Card,
  CardContent,
  CardActions,
  Chip,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  SwipeableDrawer
} from '@mui/material';
import {
  School as SchoolIcon,
  Timer as TimerIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  PlayArrow as PlayArrowIcon,
  Assessment as AssessmentIcon,
  History as HistoryIcon,
  TrendingUp as TrendingUpIcon,
  Menu as MenuIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';

import { useAuth } from '../../contexts/AuthContext';
import { getCourses, Course } from '../../api/courses';
import { getQuestions, Question, DifficultyLevel } from '../../api/questions';
import { getBookmarks, createBookmark, deleteBookmark, Bookmark } from '../../api/studentProgress';

// Import custom components
import CourseSelectionCard from '../../components/MCQ/CourseSelectionCard';
import EnhancedCourseSelection from '../../components/MCQ/EnhancedCourseSelection';
import StatsCard from '../../components/MCQ/StatsCard';
import QuestionCard from '../../components/MCQ/QuestionCard';
import BookmarkCard from '../../components/MCQ/BookmarkCard';
import PerformanceCard from '../../components/MCQ/PerformanceCard';
import MCQBottomNavigation from '../../components/MCQ/MCQBottomNavigation';
import MCQFloatingActionButton from '../../components/MCQ/MCQFloatingActionButton';
import { getDepartments, Department } from '../../api/departments';
import { getSchools, School } from '../../api/schools';

// Create motion components
const MotionPaper = motion(Paper);
const MotionCard = motion(Card);

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`mcq-tabpanel-${index}`}
      aria-labelledby={`mcq-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const MCQDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<DifficultyLevel | ''>('');
  const [tabValue, setTabValue] = useState(0);
  const [mobileTabValue, setMobileTabValue] = useState(0);
  const [drawerOpen, setDrawerOpen] = useState(false);

  // Fetch courses with enrolled courses prioritized
  const {
    data: courses = [],
    isLoading: isLoadingCourses
  } = useQuery({
    queryKey: ['courses', 'prioritizeEnrolled'],
    queryFn: () => getCourses(undefined, undefined, false, true)
  });

  // Fetch departments
  const {
    data: departments = [],
    isLoading: isLoadingDepartments
  } = useQuery({
    queryKey: ['departments'],
    queryFn: () => getDepartments()
  });

  // Fetch schools
  const {
    data: schools = [],
    isLoading: isLoadingSchools
  } = useQuery({
    queryKey: ['schools'],
    queryFn: () => getSchools()
  });

  // We no longer fetch questions directly for display
  // This improves security by not exposing questions outside of practice/exam modes

  // Fetch bookmarks
  const {
    data: bookmarks = [],
    isLoading: isLoadingBookmarks,
    refetch: refetchBookmarks
  } = useQuery({
    queryKey: ['bookmarks', user?.id, selectedCourse],
    queryFn: () => getBookmarks(
      user!.id,
      selectedCourse ? parseInt(selectedCourse) : undefined
    ),
    enabled: !!user
  });

  // Handle course selection change
  const handleCourseChange = (event: SelectChangeEvent) => {
    setSelectedCourse(event.target.value);
  };

  // Handle difficulty selection change
  const handleDifficultyChange = (event: SelectChangeEvent) => {
    setSelectedDifficulty(event.target.value as DifficultyLevel | '');
  };

  // Handle tab change for desktop
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle tab change for mobile
  const handleMobileTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setMobileTabValue(newValue);
  };

  // Toggle drawer
  const toggleDrawer = (open: boolean) => {
    setDrawerOpen(open);
  };

  // Check if a question is bookmarked
  const isBookmarked = (questionId: number): boolean => {
    return bookmarks.some(bookmark => bookmark.question_id === questionId);
  };

  // Get bookmark ID for a question
  const getBookmarkId = (questionId: number): number | undefined => {
    const bookmark = bookmarks.find(b => b.question_id === questionId);
    return bookmark?.id;
  };

  // Toggle bookmark
  const toggleBookmark = async (questionId: number) => {
    if (!user) return;

    try {
      if (isBookmarked(questionId)) {
        const bookmarkId = getBookmarkId(questionId);
        if (bookmarkId) {
          await deleteBookmark(bookmarkId);
        }
      } else {
        await createBookmark({
          student_id: user.id,
          question_id: questionId
        });
      }

      // Refetch bookmarks
      refetchBookmarks();
    } catch (error) {
      console.error('Error toggling bookmark:', error);
    }
  };

  // Start practice mode
  const startPractice = (courseId: number) => {
    navigate(`/mcq/practice/${courseId}`);
  };

  // Start exam mode with optional configuration
  const startExam = (courseId: number, examConfig?: { timeLimit: number; questionCount: number }) => {
    if (examConfig) {
      // Store exam configuration in sessionStorage to pass to the exam page
      sessionStorage.setItem('examConfig', JSON.stringify(examConfig));
    }
    navigate(`/mcq/exam/${courseId}`);
  };

  // Get course name by ID
  const getCourseName = (courseId: number): string => {
    const course = courses.find(c => c.id === courseId);
    return course ? course.name : 'Unknown Course';
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: DifficultyLevel) => {
    switch (difficulty) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'default';
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };

  return (
    <Box sx={{ maxWidth: '100%' }}>
      {/* Header with menu button for mobile */}
      <Box
        sx={{
          mb: isMobile ? 2 : 4,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        <Box>
          <Typography
            variant={isMobile ? "h5" : "h4"}
            component="h1"
            fontWeight="bold"
            gutterBottom
          >
            MCQ Dashboard
          </Typography>
          <Typography
            variant={isMobile ? "body1" : "h6"}
            color="text.secondary"
            gutterBottom
          >
            Practice and test your knowledge with multiple-choice questions
          </Typography>
        </Box>

        {isMobile && (
          <IconButton
            onClick={() => toggleDrawer(true)}
            sx={{
              bgcolor: theme.palette.background.paper,
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              '&:hover': {
                bgcolor: theme.palette.background.paper,
              }
            }}
          >
            <MenuIcon />
          </IconButton>
        )}
      </Box>

      {/* Mobile Drawer for Course Selection */}
      {isMobile && (
        <SwipeableDrawer
          anchor="right"
          open={drawerOpen}
          onClose={() => toggleDrawer(false)}
          onOpen={() => toggleDrawer(true)}
          sx={{
            '& .MuiDrawer-paper': {
              width: '85%',
              maxWidth: '350px',
              boxSizing: 'border-box',
              p: 2
            }
          }}
        >
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Course Selection
            </Typography>
            <EnhancedCourseSelection
              courses={courses}
              departments={departments}
              schools={schools}
              selectedCourse={selectedCourse}
              selectedDifficulty={selectedDifficulty}
              isLoadingCourses={isLoadingCourses || isLoadingDepartments || isLoadingSchools}
              onCourseChange={handleCourseChange}
              onDifficultyChange={handleDifficultyChange}
              onPracticeClick={(courseId) => {
                startPractice(courseId);
                toggleDrawer(false);
              }}
              onExamClick={(courseId, examConfig) => {
                startExam(courseId, examConfig);
                toggleDrawer(false);
              }}
            />
          </Box>
        </SwipeableDrawer>
      )}

      {/* Main Content */}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : '350px 1fr',
          gap: 3,
          pb: isMobile ? '80px' : 0 // Add padding for bottom navigation on mobile
        }}
      >
        {/* Left Column - Course Selection and Stats (hidden on mobile) */}
        {!isMobile && (
          <Box>
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Course Selection Card */}
              <EnhancedCourseSelection
                courses={courses}
                departments={departments}
                schools={schools}
                selectedCourse={selectedCourse}
                selectedDifficulty={selectedDifficulty}
                isLoadingCourses={isLoadingCourses || isLoadingDepartments || isLoadingSchools}
                onCourseChange={handleCourseChange}
                onDifficultyChange={handleDifficultyChange}
                onPracticeClick={startPractice}
                onExamClick={startExam}
              />

              {/* Stats Card */}
              <StatsCard
                bookmarks={bookmarks}
                totalQuestions={0}
                totalExams={0}
                correctAnswers={0}
                averageScore={0}
              />
            </motion.div>
          </Box>
        )}

        {/* Right Column - Questions */}
        <Box>
          {/* Desktop Tabs */}
          {!isMobile && (
            <Paper sx={{ borderRadius: 2, maxWidth: '100%', mb: 3 }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="fullWidth"
                sx={{ borderBottom: 1, borderColor: 'divider' }}
              >
                <Tab label="Study Options" icon={<SchoolIcon />} iconPosition="start" />
                <Tab label="Your Bookmarks" icon={<BookmarkIcon />} iconPosition="start" />
                <Tab label="Performance" icon={<TrendingUpIcon />} iconPosition="start" />
              </Tabs>

            <TabPanel value={tabValue} index={0}>
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>
                  Ready to study?
                </Typography>
                <Typography variant="body1" color="text.secondary" paragraph>
                  Select a course and choose a study mode to begin.
                </Typography>

                {selectedCourse ? (
                  <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', sm: 'row' },
                    gap: 2,
                    justifyContent: 'center',
                    mt: 3
                  }}>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<PlayArrowIcon />}
                      onClick={() => startPractice(parseInt(selectedCourse))}
                      sx={{ minWidth: '180px' }}
                    >
                      Practice Mode
                    </Button>
                    <Button
                      variant="contained"
                      color="secondary"
                      startIcon={<AssessmentIcon />}
                      onClick={() => startExam(parseInt(selectedCourse))}
                      sx={{ minWidth: '180px' }}
                    >
                      Exam Mode
                    </Button>
                  </Box>
                ) : (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    Please select a course to begin studying.
                  </Alert>
                )}
              </Box>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              {isLoadingBookmarks ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                  <CircularProgress />
                </Box>
              ) : bookmarks.length === 0 ? (
                <Alert severity="info">
                  No bookmarked questions yet. Bookmark questions to review them later.
                </Alert>
              ) : (
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 2
                    }}
                  >
                    {bookmarks.map((bookmark) => (
                      <BookmarkCard
                        key={bookmark.id}
                        bookmark={bookmark}
                        onRemoveBookmark={() => toggleBookmark(bookmark.question_id)}
                        onViewQuestion={() => navigate(`/questions/${bookmark.question_id}`)}
                        getCourseName={getCourseName}
                      />
                    ))}
                  </Box>
                </motion.div>
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <PerformanceCard
                hasData={false}
                selectedCourse={selectedCourse}
                onPracticeClick={startPractice}
                onExamClick={startExam}
              />
            </TabPanel>
          </Paper>
          )}

          {/* Mobile Content */}
          {isMobile && (
            <Box>
              {/* Exam Tab */}
              {mobileTabValue === 1 && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <AssessmentIcon sx={{ fontSize: 60, color: 'secondary.main', opacity: 0.5, mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Ready for an Exam?
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Test your knowledge with a timed exam.
                  </Typography>

                  {selectedCourse ? (
                    <Button
                      variant="contained"
                      color="secondary"
                      startIcon={<AssessmentIcon />}
                      onClick={() => startExam(parseInt(selectedCourse))}
                      sx={{ borderRadius: 1.5 }}
                    >
                      Start Exam
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      color="secondary"
                      onClick={() => toggleDrawer(true)}
                      sx={{ borderRadius: 1.5 }}
                    >
                      Select Course
                    </Button>
                  )}
                </Box>
              )}

              {/* Practice Tab */}
              {mobileTabValue === 2 && (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <PlayArrowIcon sx={{ fontSize: 60, color: 'primary.main', opacity: 0.5, mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Ready to Practice?
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Select a course and start practicing to improve your skills.
                  </Typography>

                  {selectedCourse ? (
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<PlayArrowIcon />}
                      onClick={() => startPractice(parseInt(selectedCourse))}
                      sx={{ borderRadius: 1.5 }}
                    >
                      Start Practice
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => toggleDrawer(true)}
                      sx={{ borderRadius: 1.5 }}
                    >
                      Select Course
                    </Button>
                  )}
                </Box>
              )}

              {/* Bookmarks Tab */}
              {mobileTabValue === 3 && (
                <Box>
                  {isLoadingBookmarks ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                      <CircularProgress />
                    </Box>
                  ) : bookmarks.length === 0 ? (
                    <Alert severity="info" sx={{ mb: 2 }}>
                      No bookmarked questions yet. Bookmark questions to review them later.
                    </Alert>
                  ) : (
                    <motion.div
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 2
                        }}
                      >
                        {bookmarks.map((bookmark) => (
                          <BookmarkCard
                            key={bookmark.id}
                            bookmark={bookmark}
                            onRemoveBookmark={() => toggleBookmark(bookmark.question_id)}
                            onViewQuestion={() => navigate(`/questions/${bookmark.question_id}`)}
                            getCourseName={getCourseName}
                          />
                        ))}
                      </Box>
                    </motion.div>
                  )}
                </Box>
              )}

              {/* Stats Tab */}
              {mobileTabValue === 4 && (
                <PerformanceCard
                  hasData={false}
                  selectedCourse={selectedCourse}
                  onPracticeClick={startPractice}
                  onExamClick={startExam}
                />
              )}

              {/* Courses Tab (default) */}
              {mobileTabValue === 0 && (
                <Box>
                  <StatsCard
                    bookmarks={bookmarks}
                    totalQuestions={0}
                    totalExams={0}
                    correctAnswers={0}
                    averageScore={0}
                  />

                  {selectedCourse && (
                    <Box sx={{ mb: 2 }}>
                      <Paper
                        sx={{
                          p: 2,
                          borderRadius: 2,
                          border: `1px solid ${theme.palette.divider}`
                        }}
                      >
                        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                          Selected Course: {getCourseName(parseInt(selectedCourse))}
                        </Typography>

                        <Box sx={{
                          mt: 2,
                          display: 'flex',
                          gap: 1.5,
                          flexDirection: 'column'
                        }}>
                          <Button
                            variant="contained"
                            color="primary"
                            fullWidth
                            startIcon={<PlayArrowIcon />}
                            onClick={() => startPractice(parseInt(selectedCourse))}
                            sx={{ borderRadius: 1.5 }}
                          >
                            Practice Mode
                          </Button>
                          <Button
                            variant="contained"
                            color="secondary"
                            fullWidth
                            startIcon={<AssessmentIcon />}
                            onClick={() => startExam(parseInt(selectedCourse))}
                            sx={{ borderRadius: 1.5 }}
                          >
                            Exam Mode
                          </Button>
                        </Box>
                      </Paper>
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          )}
        </Box>
      </Box>

      {/* Mobile Bottom Navigation */}
      {isMobile && (
        <>
          <MCQBottomNavigation
            value={mobileTabValue}
            onChange={handleMobileTabChange}
            bookmarksCount={bookmarks.length}
          />
          <MCQFloatingActionButton
            courseId={selectedCourse ? parseInt(selectedCourse) : undefined}
            onPracticeClick={startPractice}
            onExamClick={startExam}
            onBookmarksClick={() => setMobileTabValue(3)}
            onCourseSelectClick={() => toggleDrawer(true)}
          />
        </>
      )}
    </Box>
  );
};

export default MCQDashboard;
