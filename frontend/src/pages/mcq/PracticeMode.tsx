import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  Divider,
  Stepper,
  Step,
  StepLabel,
  IconButton,
  Tooltip,
  useTheme,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Slider,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
} from '@mui/icons-material';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';

import { useAuth } from '../../contexts/AuthContext';
import { getQuestions, getQuestion, Question } from '../../api/questions';
import { getCourse } from '../../api/courses';
import {
  createQuestionAttempt,
  getBookmarks,
  createBookmark,
  deleteBookmark,
  QuestionAttemptCreate,
} from '../../api/studentProgress';
import { getMCQGenerationStatus } from '../../api/studentTools';
import { ProcessingStatus } from '../../types/studentTools';
import ExamCalculator from '../../components/ExamCalculator';
import MathMarkdown from '../../components/common/MathMarkdown';
import { PracticeHelpButton } from '../../components/AIAssistant';

const MotionPaper = motion(Paper);
const MotionCard = motion(Card);

const PracticeMode: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { courseId } = useParams<{ courseId: string }>();
  const [searchParams] = useSearchParams();
  const specificQuestionId = searchParams.get('question');
  const noteGenerated = searchParams.get('noteGenerated') === 'true';
  const jobId = searchParams.get('jobId');
  const { user } = useAuth();

  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [isAnswerSubmitted, setIsAnswerSubmitted] = useState(false);
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [explanation, setExplanation] = useState<string>('');
  const [completedQuestions, setCompletedQuestions] = useState<number[]>([]);

  // State for question selection
  const [showQuestionSelector, setShowQuestionSelector] = useState(true);
  const [selectedQuestionCount, setSelectedQuestionCount] = useState<number>(20);
  const [allQuestions, setAllQuestions] = useState<Question[]>([]);
  const [practiceQuestions, setPracticeQuestions] = useState<Question[]>([]);

  // Fetch course
  const {
    data: course,
    isLoading: isLoadingCourse,
    error: courseError,
  } = useQuery({
    queryKey: ['course', courseId],
    queryFn: () => getCourse(parseInt(courseId!)),
    enabled: !!courseId && courseId !== 'note-generated',
  });

  // Fetch all questions - either from course or from note-generated job
  const {
    data: fetchedQuestions = [],
    isLoading: isLoadingQuestions,
    error: questionsError,
  } = useQuery({
    queryKey: ['questions', courseId, 'multiple_choice', noteGenerated, jobId],
    queryFn: async () => {
      if (jobId) {
        // If this is a note-generated MCQ session, get the job status to get the questions
        const jobStatus = await getMCQGenerationStatus(parseInt(jobId));
        console.log('Job status:', jobStatus);

        if (jobStatus.status === ProcessingStatus.COMPLETED && jobStatus.generated_question_ids) {
          // Fetch each question individually
          const questionPromises = jobStatus.generated_question_ids.map(id => getQuestion(id));
          const questions = await Promise.all(questionPromises);
          console.log(`Fetched ${questions.length} questions for job ID ${jobId}`);
          return questions;
        }
        console.warn(`Job ${jobId} is not completed or has no questions`);
        return [];
      } else if (courseId && courseId !== 'note-generated') {
        // Regular course questions
        console.log(`Fetching questions for course ID ${courseId}`);
        const questions = await getQuestions(
          parseInt(courseId!),
          undefined,
          'multiple_choice'
        );
        console.log(`Fetched ${questions.length} questions for course ID ${courseId}`);
        return questions;
      } else {
        console.warn('No valid course ID or job ID provided');
        return [];
      }
    },
    enabled: !!(courseId || jobId),
  });

  // Store all questions when fetched
  useEffect(() => {
    if (fetchedQuestions.length > 0) {
      setAllQuestions(fetchedQuestions);
      // If we have a specific question ID, don't show the selector
      if (specificQuestionId) {
        setPracticeQuestions(fetchedQuestions);
        setShowQuestionSelector(false);
      }
    }
  }, [fetchedQuestions, specificQuestionId]);

  // Create practice questions when count is selected
  const createPracticeSession = () => {
    if (allQuestions.length === 0) return;

    const maxQuestions = Math.min(selectedQuestionCount, allQuestions.length);
    const shuffled = [...allQuestions].sort(() => Math.random() - 0.5);
    const selected = shuffled.slice(0, maxQuestions);

    setPracticeQuestions(selected);
    setShowQuestionSelector(false);
    setCurrentQuestionIndex(0);
    setCompletedQuestions([]);
  };

  // Use practice questions for the component
  const questions = practiceQuestions;

  // Fetch bookmarks
  const {
    data: bookmarks = [],
    isLoading: isLoadingBookmarks,
    refetch: refetchBookmarks,
  } = useQuery({
    queryKey: ['bookmarks', user?.id, courseId],
    queryFn: () => getBookmarks(
      user!.id,
      courseId && courseId !== 'note-generated' ? parseInt(courseId) : undefined
    ),
    enabled: !!user && !!courseId && courseId !== 'note-generated',
  });

  // Create question attempt mutation
  const createAttemptMutation = useMutation({
    mutationFn: (data: QuestionAttemptCreate) => createQuestionAttempt(data),
  });

  // Create bookmark mutation
  const createBookmarkMutation = useMutation({
    mutationFn: (data: { student_id: number; question_id: number }) => createBookmark(data),
    onSuccess: () => refetchBookmarks(),
  });

  // Delete bookmark mutation
  const deleteBookmarkMutation = useMutation({
    mutationFn: (id: number) => deleteBookmark(id),
    onSuccess: () => refetchBookmarks(),
  });

  // If a specific question ID is provided, find its index
  useEffect(() => {
    if (specificQuestionId && questions.length > 0) {
      const index = questions.findIndex(q => q.id === parseInt(specificQuestionId));
      if (index !== -1) {
        setCurrentQuestionIndex(index);
      }
    }
  }, [specificQuestionId, questions]);

  // Get current question
  const currentQuestion = questions[currentQuestionIndex];

  // Handle case when questions are loading or not available
  useEffect(() => {
    if (!isLoadingQuestions && questions.length === 0) {
      console.log("No questions available for this course");
    } else if (!isLoadingQuestions && questions.length > 0) {
      console.log(`Loaded ${questions.length} questions for practice mode`);
    }
  }, [isLoadingQuestions, questions]);

  // Check if a question is bookmarked
  const isBookmarked = (questionId: number): boolean => {
    return bookmarks.some(bookmark => bookmark.question_id === questionId);
  };

  // Get bookmark ID for a question
  const getBookmarkId = (questionId: number): number | undefined => {
    const bookmark = bookmarks.find(b => b.question_id === questionId);
    return bookmark?.id;
  };

  // Toggle bookmark
  const toggleBookmark = async (questionId: number) => {
    if (!user) return;

    try {
      if (isBookmarked(questionId)) {
        const bookmarkId = getBookmarkId(questionId);
        if (bookmarkId) {
          await deleteBookmarkMutation.mutateAsync(bookmarkId);
        }
      } else {
        await createBookmarkMutation.mutateAsync({
          student_id: user.id,
          question_id: questionId,
        });
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
    }
  };

  // Handle answer selection
  const handleAnswerSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!isAnswerSubmitted) {
      setSelectedAnswer(event.target.value);
    }
  };

  // Submit answer
  const handleSubmitAnswer = async () => {
    if (!selectedAnswer || !currentQuestion || !user) return;

    console.log('Selected answer:', selectedAnswer);
    console.log('Correct answer:', currentQuestion.answer);

    // Find the option key (A, B, C, D) that matches the selected answer
    let selectedKey = '';
    if (currentQuestion.options) {
      for (const [key, value] of Object.entries(currentQuestion.options)) {
        if (value === selectedAnswer) {
          selectedKey = key;
          break;
        }
      }
    }

    console.log('Selected key:', selectedKey);

    // Check if the answer is correct by comparing either the value or the key
    const isAnswerCorrect =
      selectedAnswer === currentQuestion.answer || // Compare values
      selectedKey === currentQuestion.answer;      // Compare keys (A, B, C, D)

    console.log('Is answer correct:', isAnswerCorrect);

    setIsCorrect(isAnswerCorrect);
    setIsAnswerSubmitted(true);
    setExplanation(currentQuestion.explanation || 'No explanation provided.');

    // Add to completed questions
    if (!completedQuestions.includes(currentQuestion.id)) {
      setCompletedQuestions([...completedQuestions, currentQuestion.id]);
    }

    // Save attempt to backend
    try {
      await createAttemptMutation.mutateAsync({
        student_id: user.id,
        question_id: currentQuestion.id,
        is_correct: isAnswerCorrect,
        selected_answer: selectedAnswer,
      });
      console.log('Attempt saved successfully');
    } catch (error) {
      console.error('Error saving attempt:', error);
    }
  };

  // Move to next question
  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      resetQuestion();
    }
  };

  // Move to previous question
  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      resetQuestion();
    }
  };

  // Reset current question state
  const resetQuestion = () => {
    setSelectedAnswer(null);
    setIsAnswerSubmitted(false);
    setIsCorrect(null);
    setExplanation('');
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'default';
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
    exit: { opacity: 0 },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
      },
    },
  };

  // Loading state
  if (isLoadingCourse || isLoadingQuestions) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (courseError || questionsError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading practice session. Please try again.
      </Alert>
    );
  }

  // No questions state - only show this if we've finished loading and have no questions
  if (!isLoadingQuestions && allQuestions.length === 0) {
    return (
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
          <IconButton onClick={() => navigate('/mcq')} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Practice Mode
          </Typography>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          No questions available for this course. Please select a different course.
        </Alert>

        <Button
          variant="contained"
          startIcon={<HomeIcon />}
          onClick={() => navigate('/mcq')}
        >
          Back to MCQ Dashboard
        </Button>
      </Box>
    );
  }

  // Don't render the main content if we're still showing the question selector or have no practice questions
  if (showQuestionSelector || questions.length === 0) {
    return (
      <Box>
        {/* Question Count Selection Dialog */}
        <Dialog
          open={showQuestionSelector}
          onClose={() => {}}
          disableEscapeKeyDown
          maxWidth="sm"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 2,
              boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            }
          }}
        >
          <DialogTitle sx={{
            bgcolor: theme.palette.primary.main,
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <HomeIcon />
            Practice Session Setup
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Customize your practice session for {jobId ? 'Generated Questions' : course?.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Select how many questions you'd like to practice with. Questions will be randomly selected.
            </Typography>

            <Box sx={{ mt: 4, mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1" fontWeight="medium">
                  Number of Questions: {selectedQuestionCount}
                </Typography>
              </Box>
              <Slider
                value={selectedQuestionCount}
                onChange={(_, value) => setSelectedQuestionCount(value as number)}
                min={5}
                max={Math.min(60, allQuestions.length)}
                step={5}
                marks={[
                  { value: 5, label: '5' },
                  { value: 20, label: '20' },
                  { value: 40, label: '40' },
                  { value: Math.min(60, allQuestions.length), label: `${Math.min(60, allQuestions.length)}` },
                ]}
                valueLabelDisplay="auto"
                color="primary"
              />
              <Typography variant="caption" color="text.secondary">
                Available questions: {allQuestions.length}
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions sx={{ px: 3, py: 2 }}>
            <Button onClick={() => navigate('/mcq')} variant="outlined">
              Cancel
            </Button>
            <Button
              onClick={createPracticeSession}
              variant="contained"
              color="primary"
              disabled={allQuestions.length === 0}
            >
              Start Practice
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <IconButton onClick={() => navigate('/mcq')} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Practice Mode: {jobId ? 'Generated Questions' : course?.name}
        </Typography>
      </Box>

      {/* Progress Stepper */}
      <Paper
        sx={{
          p: { xs: 1.5, sm: 2 },
          mb: 3,
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          bgcolor: theme.palette.background.paper
        }}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          flexDirection: { xs: 'column', sm: 'row' },
          gap: { xs: 1, sm: 0 },
          mb: 2
        }}>
          <Typography variant="subtitle1" fontWeight="bold" sx={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}>
            Question {currentQuestionIndex + 1} of {questions.length}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              size="small"
              startIcon={<RefreshIcon />}
              onClick={resetQuestion}
              disabled={!isAnswerSubmitted}
              sx={{
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                px: { xs: 1, sm: 2 }
              }}
            >
              Reset
            </Button>
            <Button
              variant="outlined"
              size="small"
              startIcon={<HomeIcon />}
              onClick={() => navigate('/mcq')}
              sx={{
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                px: { xs: 1, sm: 2 }
              }}
            >
              Exit
            </Button>
          </Box>
        </Box>

        {/* Question Navigation */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          overflowX: 'auto',
          pb: { xs: 1, sm: 0 }
        }}>
          <IconButton
            onClick={handlePreviousQuestion}
            disabled={currentQuestionIndex === 0}
            size="small"
            sx={{
              minWidth: 'auto',
              p: { xs: 0.5, sm: 1 }
            }}
          >
            <ArrowBackIcon fontSize="small" />
          </IconButton>

          {/* Question Indicators */}
          <Box sx={{
            display: 'flex',
            gap: { xs: 0.5, sm: 1 },
            overflowX: 'auto',
            flex: 1,
            py: 1,
            '&::-webkit-scrollbar': {
              height: 4,
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: 'rgba(0,0,0,0.1)',
              borderRadius: 2,
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: theme.palette.primary.main,
              borderRadius: 2,
            },
          }}>
            {questions.map((question, index) => (
              <Box
                key={question.id}
                onClick={() => {
                  setCurrentQuestionIndex(index);
                  resetQuestion();
                }}
                sx={{
                  minWidth: { xs: 32, sm: 40 },
                  height: { xs: 32, sm: 40 },
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  fontWeight: 'bold',
                  border: `2px solid ${
                    index === currentQuestionIndex
                      ? theme.palette.primary.main
                      : completedQuestions.includes(question.id)
                        ? theme.palette.success.main
                        : theme.palette.divider
                  }`,
                  bgcolor:
                    index === currentQuestionIndex
                      ? theme.palette.primary.main
                      : completedQuestions.includes(question.id)
                        ? theme.palette.success.main
                        : 'transparent',
                  color:
                    index === currentQuestionIndex || completedQuestions.includes(question.id)
                      ? 'white'
                      : theme.palette.text.primary,
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    transform: 'scale(1.1)',
                    boxShadow: `0 2px 8px ${theme.palette.primary.main}40`,
                  },
                }}
              >
                {index + 1}
              </Box>
            ))}
          </Box>

          <IconButton
            onClick={handleNextQuestion}
            disabled={currentQuestionIndex === questions.length - 1}
            size="small"
            sx={{
              minWidth: 'auto',
              p: { xs: 0.5, sm: 1 }
            }}
          >
            <ArrowForwardIcon fontSize="small" />
          </IconButton>
        </Box>
      </Paper>

      <AnimatePresence mode="wait">
        <motion.div
          key={currentQuestionIndex}
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          {/* Question Card */}
          <MotionCard
            variants={itemVariants}
            sx={{ mb: 3, borderRadius: 2 }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Chip
                  label={currentQuestion.difficulty}
                  color={getDifficultyColor(currentQuestion.difficulty)}
                  size="small"
                />

                <Tooltip title={isBookmarked(currentQuestion.id) ? "Remove bookmark" : "Bookmark question"}>
                  <IconButton
                    onClick={() => toggleBookmark(currentQuestion.id)}
                    color="primary"
                  >
                    {isBookmarked(currentQuestion.id) ? <BookmarkIcon /> : <BookmarkBorderIcon />}
                  </IconButton>
                </Tooltip>
              </Box>

              <Box sx={{ mb: 2 }}>
                <MathMarkdown>{currentQuestion.content}</MathMarkdown>
              </Box>

              <FormControl component="fieldset" sx={{ width: '100%', mt: 2 }}>
                <FormLabel component="legend">Select your answer:</FormLabel>
                <RadioGroup
                  value={selectedAnswer || ''}
                  onChange={handleAnswerSelect}
                >
                  {currentQuestion.options && Object.entries(currentQuestion.options).map(([key, value]) => (
                    <FormControlLabel
                      key={key}
                      value={value}
                      control={<Radio />}
                      label={
                        <Box component="span" sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                          <Typography component="span" fontWeight="bold">{key}:</Typography>
                          <Box component="span" sx={{ flex: 1 }}>
                            <MathMarkdown>{value}</MathMarkdown>
                          </Box>
                        </Box>
                      }
                      disabled={isAnswerSubmitted}
                      sx={{
                        p: 1,
                        borderRadius: 1,
                        mb: 1,
                        ...(isAnswerSubmitted && value === currentQuestion.answer
                          ? {
                              bgcolor: 'success.light',
                              color: 'success.contrastText',
                            }
                          : {}),
                        ...(isAnswerSubmitted && selectedAnswer === value && value !== currentQuestion.answer
                          ? {
                              bgcolor: 'error.light',
                              color: 'error.contrastText',
                            }
                          : {}),
                      }}
                    />
                  ))}
                </RadioGroup>
              </FormControl>

              {!isAnswerSubmitted ? (
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  sx={{ mt: 3 }}
                  onClick={handleSubmitAnswer}
                  disabled={!selectedAnswer}
                >
                  Submit Answer
                </Button>
              ) : (
                <Box sx={{ mt: 3 }}>
                  <Alert
                    severity={isCorrect ? 'success' : 'error'}
                    icon={isCorrect ? <CheckIcon /> : <CloseIcon />}
                    sx={{ mb: 2 }}
                  >
                    {isCorrect
                      ? 'Correct! Well done.'
                      : `Incorrect. The correct answer is: ${currentQuestion.answer}`}
                  </Alert>

                  {explanation && (
                    <Paper
                      elevation={0}
                      sx={{
                        p: 2,
                        bgcolor: theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.02)' : 'rgba(255, 255, 255, 0.02)',
                        borderRadius: 2,
                        border: `1px solid ${theme.palette.divider}`,
                      }}
                    >
                      <Typography variant="subtitle2" gutterBottom>
                        Explanation:
                      </Typography>
                      <Box>
                        <MathMarkdown>{explanation}</MathMarkdown>
                      </Box>

                      {/* AI Help Button */}
                      <PracticeHelpButton questionId={currentQuestion.id} />
                    </Paper>
                  )}

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                    <Button
                      variant="outlined"
                      startIcon={<RefreshIcon />}
                      onClick={resetQuestion}
                    >
                      Try Again
                    </Button>

                    <Button
                      variant="contained"
                      endIcon={<ArrowForwardIcon />}
                      onClick={handleNextQuestion}
                      disabled={currentQuestionIndex === questions.length - 1}
                    >
                      Next Question
                    </Button>
                  </Box>
                </Box>
              )}
            </CardContent>
          </MotionCard>
        </motion.div>
      </AnimatePresence>

      {/* Calculator */}
      <ExamCalculator position="right" />
    </Box>
  );
};

export default PracticeMode;
