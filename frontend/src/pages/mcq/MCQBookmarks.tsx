import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  Divider,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  PlayArrow as PlayArrowIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import { motion } from 'framer-motion';

import { useAuth } from '../../contexts/AuthContext';
import { getCourses } from '../../api/courses';
import { getBookmarks, deleteBookmark, Bookmark } from '../../api/studentProgress';
import { getQuestion } from '../../api/questions';
import Calculator from '../../components/Calculator';

// Create motion components
const MotionPaper = motion(Paper);
const MotionCard = motion(Card);

const MCQBookmarks: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();

  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [questionDetails, setQuestionDetails] = useState<Record<number, any>>({});
  const [loadingQuestions, setLoadingQuestions] = useState<Record<number, boolean>>({});

  // Fetch courses with enrolled courses prioritized
  const {
    data: courses = [],
    isLoading: isLoadingCourses
  } = useQuery({
    queryKey: ['courses', 'prioritizeEnrolled'],
    queryFn: () => getCourses(undefined, undefined, false, true)
  });

  // Fetch bookmarks
  const {
    data: bookmarks = [],
    isLoading: isLoadingBookmarks,
    refetch: refetchBookmarks
  } = useQuery({
    queryKey: ['bookmarks', user?.id, selectedCourse],
    queryFn: () => getBookmarks(
      user!.id,
      selectedCourse ? parseInt(selectedCourse) : undefined
    ),
    enabled: !!user
  });

  // Delete bookmark mutation
  const deleteBookmarkMutation = useMutation({
    mutationFn: (id: number) => deleteBookmark(id),
    onSuccess: () => refetchBookmarks()
  });

  // Handle course selection change
  const handleCourseChange = (event: SelectChangeEvent) => {
    setSelectedCourse(event.target.value);
  };

  // Handle search query change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Remove bookmark
  const removeBookmark = async (bookmarkId: number) => {
    try {
      await deleteBookmarkMutation.mutateAsync(bookmarkId);
    } catch (error) {
      console.error('Error removing bookmark:', error);
    }
  };

  // Load question details
  const loadQuestionDetails = async (questionId: number) => {
    if (questionDetails[questionId]) return;

    setLoadingQuestions(prev => ({ ...prev, [questionId]: true }));

    try {
      const question = await getQuestion(questionId);
      setQuestionDetails(prev => ({ ...prev, [questionId]: question }));
    } catch (error) {
      console.error(`Error loading question ${questionId}:`, error);
    } finally {
      setLoadingQuestions(prev => ({ ...prev, [questionId]: false }));
    }
  };

  // Filter bookmarks based on search query
  const filteredBookmarks = bookmarks.filter(bookmark => {
    const question = questionDetails[bookmark.question_id];
    if (!searchQuery) return true;

    const searchLower = searchQuery.toLowerCase();

    // If question details are loaded, search in content
    if (question) {
      return question.content.toLowerCase().includes(searchLower);
    }

    // Otherwise, just include all bookmarks when searching
    return true;
  });

  // Get course name by ID
  const getCourseName = (courseId: number): string => {
    const course = courses.find(c => c.id === courseId);
    return course ? course.name : 'Unknown Course';
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };

  // Load question details for visible bookmarks
  React.useEffect(() => {
    bookmarks.forEach(bookmark => {
      loadQuestionDetails(bookmark.question_id);
    });
  }, [bookmarks]);

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <IconButton onClick={() => navigate('/mcq')} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Bookmarked Questions
        </Typography>
      </Box>

      <Paper sx={{ p: 3, mb: 4, borderRadius: 2 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel id="course-filter-label">Filter by Course</InputLabel>
              <Select
                labelId="course-filter-label"
                id="course-filter"
                value={selectedCourse}
                label="Filter by Course"
                onChange={handleCourseChange}
                disabled={isLoadingCourses}
              >
                <MenuItem value="">
                  <em>All Courses</em>
                </MenuItem>
                {courses.map((course) => (
                  <MenuItem key={course.id} value={course.id.toString()}>
                    {course.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              placeholder="Search bookmarked questions..."
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            {filteredBookmarks.length} bookmarked question{filteredBookmarks.length !== 1 ? 's' : ''}
          </Typography>

          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={() => setSearchQuery('')}
            disabled={!searchQuery}
          >
            Clear Filters
          </Button>
        </Box>
      </Paper>

      {isLoadingBookmarks ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : filteredBookmarks.length === 0 ? (
        <Alert severity="info">
          {searchQuery
            ? 'No bookmarks match your search criteria. Try a different search term or clear the filters.'
            : 'You have no bookmarked questions yet. Bookmark questions while practicing to save them for later review.'}
        </Alert>
      ) : (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3}>
            {filteredBookmarks.map((bookmark) => {
              const question = questionDetails[bookmark.question_id];
              const isLoading = loadingQuestions[bookmark.question_id];

              return (
                <Grid item xs={12} md={6} key={bookmark.id}>
                  <MotionCard
                    variants={itemVariants}
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      transition: 'transform 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                      },
                    }}
                  >
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="caption" color="text.secondary">
                          Bookmarked on {new Date(bookmark.created_at).toLocaleDateString()}
                        </Typography>
                        <Tooltip title="Remove bookmark">
                          <IconButton
                            onClick={() => removeBookmark(bookmark.id)}
                            color="primary"
                            size="small"
                          >
                            <BookmarkIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>

                      {isLoading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                          <CircularProgress size={24} />
                        </Box>
                      ) : question ? (
                        <>
                          <Typography variant="subtitle1" gutterBottom>
                            Bookmarked Question
                          </Typography>

                          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
                            {question.course_id && (
                              <Chip
                                label={getCourseName(question.course_id)}
                                size="small"
                                color="primary"
                                variant="outlined"
                              />
                            )}
                            {question.difficulty && (
                              <Chip
                                label={question.difficulty}
                                size="small"
                                color={
                                  question.difficulty === 'easy' ? 'success' :
                                  question.difficulty === 'medium' ? 'warning' : 'error'
                                }
                              />
                            )}
                            {question.topic && (
                              <Chip
                                label={question.topic}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        </>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          Question #{bookmark.question_id}
                        </Typography>
                      )}

                      {bookmark.notes && (
                        <Box sx={{ mt: 2 }}>
                          <Divider sx={{ my: 1 }} />
                          <Typography variant="caption" color="text.secondary">
                            Notes:
                          </Typography>
                          <Typography variant="body2">
                            {bookmark.notes}
                          </Typography>
                        </Box>
                      )}
                    </CardContent>

                    <CardActions>
                      {question && (
                        <Button
                          fullWidth
                          variant="contained"
                          color="primary"
                          startIcon={<PlayArrowIcon />}
                          onClick={() => navigate(`/mcq/practice/${question.course_id}?question=${question.id}`)}
                        >
                          Practice This Question
                        </Button>
                      )}
                    </CardActions>
                  </MotionCard>
                </Grid>
              );
            })}
          </Grid>
        </motion.div>
      )}

      {/* Calculator */}
      <Calculator position="right" />
    </Box>
  );
};

export default MCQBookmarks;
