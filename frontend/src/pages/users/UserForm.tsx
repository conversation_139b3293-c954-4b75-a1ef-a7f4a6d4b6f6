import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  TextField, 
  Button, 
  Grid,
  FormControlLabel,
  Switch,
  CircularProgress,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';
import { 
  Save as SaveIcon, 
  ArrowBack as ArrowBackIcon 
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getUser, updateUser, UserUpdate } from '../../api/users';
import { getSchools } from '../../api/schools';
import { useContextualNavigation } from '../../hooks/useAdminContext';

// Validation schema
const validationSchema = Yup.object({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  username: Yup.string()
    .required('Username is required')
    .min(3, 'Username must be at least 3 characters'),
  full_name: Yup.string()
    .required('Full name is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .nullable(),
  is_active: Yup.boolean(),
  school_id: Yup.number()
    .nullable()
    .when('role', {
      is: 'student',
      then: (schema) => schema.required('School is required for students'),
      otherwise: (schema) => schema.nullable()
    }),
});

const UserForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const userId = parseInt(id || '0');
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { getPath } = useContextualNavigation();
  
  // State for success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch user data
  const { 
    data: user, 
    isLoading: isLoadingUser, 
    error: userError 
  } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => getUser(userId),
    enabled: !!userId
  });

  // Fetch schools for dropdown
  const { 
    data: schools = [], 
    isLoading: isLoadingSchools 
  } = useQuery({
    queryKey: ['schools'],
    queryFn: getSchools
  });

  // Update user mutation
  const updateMutation = useMutation({
    mutationFn: (data: UserUpdate) => updateUser(userId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user', userId] });
      setSuccessMessage('User updated successfully');
      setTimeout(() => {
        navigate(getPath(`/users/${userId}`));
      }, 1500);
    }
  });

  // Setup formik
  const formik = useFormik({
    initialValues: {
      email: '',
      username: '',
      full_name: '',
      password: '',
      is_active: true,
      school_id: null as number | null,
    },
    validationSchema,
    onSubmit: (values) => {
      // Only include password if it's not empty
      const userData: UserUpdate = {
        email: values.email,
        username: values.username,
        full_name: values.full_name,
        is_active: values.is_active,
        school_id: values.school_id,
      };
      
      if (values.password) {
        userData.password = values.password;
      }
      
      updateMutation.mutate(userData);
    },
    enableReinitialize: true
  });

  // Update form values when user data is loaded
  useEffect(() => {
    if (user) {
      formik.setValues({
        email: user.email,
        username: user.username,
        full_name: user.full_name,
        password: '', // Don't populate password
        is_active: user.is_active,
        school_id: user.school_id || null,
      });
    }
  }, [user]);

  // Handle loading and error states
  if (isLoadingUser) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (userError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading user: {(userError as Error).message}
      </Alert>
    );
  }

  if (!user) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        User not found
      </Alert>
    );
  }

  const isSubmitting = updateMutation.isPending;
  const error = updateMutation.error;

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate(getPath(`/users/${userId}`))}
          sx={{ mr: 2 }}
        >
          Back to User
        </Button>
        
        <Typography variant="h4" component="h1">
          Edit User
        </Typography>
      </Box>
      
      <Paper sx={{ p: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {(error as Error).message || 'An error occurred. Please try again.'}
          </Alert>
        )}
        
        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="full_name"
                name="full_name"
                label="Full Name"
                variant="outlined"
                value={formik.values.full_name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.full_name && Boolean(formik.errors.full_name)}
                helperText={formik.touched.full_name && formik.errors.full_name}
                disabled={isSubmitting}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="username"
                name="username"
                label="Username"
                variant="outlined"
                value={formik.values.username}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.username && Boolean(formik.errors.username)}
                helperText={formik.touched.username && formik.errors.username}
                disabled={isSubmitting}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="email"
                name="email"
                label="Email"
                variant="outlined"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.email && Boolean(formik.errors.email)}
                helperText={formik.touched.email && formik.errors.email}
                disabled={isSubmitting}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="password"
                name="password"
                label="Password (leave blank to keep current)"
                type="password"
                variant="outlined"
                value={formik.values.password}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.password && Boolean(formik.errors.password)}
                helperText={
                  (formik.touched.password && formik.errors.password) ||
                  "Leave blank to keep the current password"
                }
                disabled={isSubmitting}
              />
            </Grid>
            
            {user.role === 'student' && (
              <Grid item xs={12}>
                <FormControl 
                  fullWidth 
                  error={formik.touched.school_id && Boolean(formik.errors.school_id)}
                  disabled={isLoadingSchools || isSubmitting}
                >
                  <InputLabel id="school-label">School</InputLabel>
                  <Select
                    labelId="school-label"
                    id="school_id"
                    name="school_id"
                    value={formik.values.school_id || ''}
                    label="School"
                    onChange={(e) => {
                      const value = e.target.value;
                      formik.setFieldValue('school_id', value === '' ? null : Number(value));
                    }}
                    onBlur={formik.handleBlur}
                  >
                    <MenuItem value="">None</MenuItem>
                    {schools.map((school) => (
                      <MenuItem key={school.id} value={school.id}>
                        {school.name}
                      </MenuItem>
                    ))}
                  </Select>
                  {formik.touched.school_id && formik.errors.school_id && (
                    <FormHelperText>{formik.errors.school_id as string}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            )}
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    id="is_active"
                    name="is_active"
                    checked={formik.values.is_active}
                    onChange={formik.handleChange}
                    disabled={isSubmitting}
                  />
                }
                label="Active"
              />
            </Grid>
            
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button
                variant="outlined"
                onClick={() => navigate(getPath(`/users/${userId}`))}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              
              <Button
                type="submit"
                variant="contained"
                color="primary"
                startIcon={isSubmitting ? <CircularProgress size={24} /> : <SaveIcon />}
                disabled={isSubmitting}
              >
                Update User
              </Button>
            </Grid>
          </Grid>
        </form>
      </Paper>
      
      {/* Success message */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={3000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setSuccessMessage(null)}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UserForm;
