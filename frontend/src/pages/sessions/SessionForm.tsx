import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  TextField, 
  Button, 
  Grid,
  FormControlLabel,
  Switch,
  CircularProgress,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Divider,
  InputAdornment
} from '@mui/material';
import { 
  Save as SaveIcon, 
  ArrowBack as ArrowBackIcon,
  Videocam as VideocamIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useFormik, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  getSession, 
  createSession, 
  updateSession, 
  SessionCreate, 
  SessionUpdate,
  SessionType,
  SessionStatus
} from '../../api/sessions';
import { getCourses } from '../../api/courses';
import { useAuth } from '../../contexts/AuthContext';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// Validation schema
const validationSchema = Yup.object({
  title: Yup.string()
    .required('Title is required')
    .max(100, 'Title must be at most 100 characters'),
  description: Yup.string()
    .required('Description is required')
    .max(1000, 'Description must be at most 1000 characters'),
  session_type: Yup.string()
    .required('Session type is required')
    .oneOf(['online', 'in_person'], 'Invalid session type'),
  status: Yup.string()
    .required('Status is required')
    .oneOf(['scheduled', 'in_progress', 'completed', 'cancelled'], 'Invalid status'),
  start_time: Yup.date()
    .required('Start time is required'),
  end_time: Yup.date()
    .required('End time is required')
    .min(
      Yup.ref('start_time'),
      'End time must be after start time'
    ),
  location: Yup.string()
    .when('session_type', {
      is: 'in_person',
      then: (schema) => schema.required('Location is required for in-person sessions'),
      otherwise: (schema) => schema.nullable()
    }),
  video_link: Yup.string()
    .when('session_type', {
      is: 'online',
      then: (schema) => schema.url('Must be a valid URL').required('Video link is required for online sessions'),
      otherwise: (schema) => schema.nullable()
    }),
  max_students: Yup.number()
    .required('Maximum number of students is required')
    .min(1, 'At least 1 student is required')
    .max(100, 'Maximum 100 students allowed'),
  is_recurring: Yup.boolean(),
  recurrence_pattern: Yup.string()
    .when('is_recurring', {
      is: true,
      then: (schema) => schema.required('Recurrence pattern is required for recurring sessions'),
      otherwise: (schema) => schema.nullable()
    }),
  course_id: Yup.number()
    .required('Course is required'),
});

const SessionForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const isEditMode = !!id;
  const sessionId = parseInt(id || '0');
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  // Get courseId from location state if available
  const initialCourseId = location.state?.courseId;
  
  // State for success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch session data if in edit mode
  const { 
    data: session, 
    isLoading: isLoadingSession, 
    error: sessionError 
  } = useQuery({
    queryKey: ['session', sessionId],
    queryFn: () => getSession(sessionId),
    enabled: isEditMode
  });

  // Fetch courses for dropdown
  const { 
    data: courses = [], 
    isLoading: isLoadingCourses 
  } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  // Create session mutation
  const createMutation = useMutation({
    mutationFn: (data: SessionCreate) => createSession(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      setSuccessMessage('Session created successfully');
      setTimeout(() => {
        navigate(`/sessions/${data.id}`);
      }, 1500);
    }
  });

  // Update session mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: SessionUpdate }) => 
      updateSession(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      queryClient.invalidateQueries({ queryKey: ['session', sessionId] });
      setSuccessMessage('Session updated successfully');
      setTimeout(() => {
        navigate(`/sessions/${sessionId}`);
      }, 1500);
    }
  });

  // Setup formik
  const formik = useFormik({
    initialValues: {
      title: '',
      description: '',
      session_type: 'online' as SessionType,
      status: 'scheduled' as SessionStatus,
      start_time: new Date(new Date().setHours(new Date().getHours() + 1, 0, 0, 0)),
      end_time: new Date(new Date().setHours(new Date().getHours() + 2, 0, 0, 0)),
      location: '',
      video_link: '',
      max_students: 10,
      is_recurring: false,
      recurrence_pattern: '',
      course_id: initialCourseId || '',
      tutor_id: user?.id || 0,
    },
    validationSchema,
    onSubmit: (values, { setSubmitting }: FormikHelpers<any>) => {
      const sessionData = {
        ...values,
        course_id: Number(values.course_id),
        tutor_id: Number(values.tutor_id),
        start_time: values.start_time.toISOString(),
        end_time: values.end_time.toISOString(),
      };
      
      if (isEditMode) {
        updateMutation.mutate({ id: sessionId, data: sessionData });
      } else {
        createMutation.mutate(sessionData);
      }
    },
    enableReinitialize: true
  });

  // Update form values when session data is loaded
  useEffect(() => {
    if (session) {
      formik.setValues({
        title: session.title,
        description: session.description,
        session_type: session.session_type,
        status: session.status,
        start_time: new Date(session.start_time),
        end_time: new Date(session.end_time),
        location: session.location || '',
        video_link: session.video_link || '',
        max_students: session.max_students,
        is_recurring: session.is_recurring,
        recurrence_pattern: session.recurrence_pattern || '',
        course_id: session.course_id.toString(),
        tutor_id: session.tutor_id,
      });
    }
  }, [session]);

  // Handle loading and error states
  if (isEditMode && isLoadingSession) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (isEditMode && sessionError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading session: {(sessionError as Error).message}
      </Alert>
    );
  }

  const isSubmitting = createMutation.isPending || updateMutation.isPending;
  const error = createMutation.error || updateMutation.error;

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate(isEditMode ? `/sessions/${sessionId}` : '/sessions')}
            sx={{ mr: 2 }}
          >
            {isEditMode ? 'Back to Session' : 'Back to Sessions'}
          </Button>
          
          <Typography variant="h4" component="h1">
            {isEditMode ? 'Edit Session' : 'Create New Session'}
          </Typography>
        </Box>
        
        <Paper sx={{ p: 3 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {(error as Error).message || 'An error occurred. Please try again.'}
            </Alert>
          )}
          
          <form onSubmit={formik.handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="title"
                  name="title"
                  label="Session Title"
                  variant="outlined"
                  value={formik.values.title}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.title && Boolean(formik.errors.title)}
                  helperText={formik.touched.title && formik.errors.title}
                  disabled={isSubmitting}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="description"
                  name="description"
                  label="Description"
                  variant="outlined"
                  multiline
                  rows={4}
                  value={formik.values.description}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.description && Boolean(formik.errors.description)}
                  helperText={formik.touched.description && formik.errors.description}
                  disabled={isSubmitting}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl 
                  fullWidth 
                  error={formik.touched.course_id && Boolean(formik.errors.course_id)}
                  disabled={isLoadingCourses || isSubmitting}
                >
                  <InputLabel id="course-label">Course</InputLabel>
                  <Select
                    labelId="course-label"
                    id="course_id"
                    name="course_id"
                    value={formik.values.course_id}
                    label="Course"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  >
                    {courses.map((course) => (
                      <MenuItem key={course.id} value={course.id.toString()}>
                        {course.name} ({course.code})
                      </MenuItem>
                    ))}
                  </Select>
                  {formik.touched.course_id && formik.errors.course_id && (
                    <FormHelperText>{formik.errors.course_id as string}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl 
                  fullWidth 
                  error={formik.touched.session_type && Boolean(formik.errors.session_type)}
                  disabled={isSubmitting}
                >
                  <InputLabel id="type-label">Session Type</InputLabel>
                  <Select
                    labelId="type-label"
                    id="session_type"
                    name="session_type"
                    value={formik.values.session_type}
                    label="Session Type"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    startAdornment={
                      <InputAdornment position="start">
                        {formik.values.session_type === 'online' ? <VideocamIcon /> : <LocationIcon />}
                      </InputAdornment>
                    }
                  >
                    <MenuItem value="online">Online</MenuItem>
                    <MenuItem value="in_person">In-Person</MenuItem>
                  </Select>
                  {formik.touched.session_type && formik.errors.session_type && (
                    <FormHelperText>{formik.errors.session_type as string}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <DateTimePicker
                  label="Start Time"
                  value={formik.values.start_time}
                  onChange={(value) => formik.setFieldValue('start_time', value)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      variant: 'outlined',
                      error: formik.touched.start_time && Boolean(formik.errors.start_time),
                      helperText: formik.touched.start_time && formik.errors.start_time as string,
                      onBlur: () => formik.setFieldTouched('start_time', true),
                    },
                  }}
                  disabled={isSubmitting}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <DateTimePicker
                  label="End Time"
                  value={formik.values.end_time}
                  onChange={(value) => formik.setFieldValue('end_time', value)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      variant: 'outlined',
                      error: formik.touched.end_time && Boolean(formik.errors.end_time),
                      helperText: formik.touched.end_time && formik.errors.end_time as string,
                      onBlur: () => formik.setFieldTouched('end_time', true),
                    },
                  }}
                  disabled={isSubmitting}
                />
              </Grid>
              
              {formik.values.session_type === 'online' ? (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="video_link"
                    name="video_link"
                    label="Video Link"
                    variant="outlined"
                    value={formik.values.video_link}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.video_link && Boolean(formik.errors.video_link)}
                    helperText={
                      (formik.touched.video_link && formik.errors.video_link) || 
                      "Enter the URL for the video meeting (Zoom, Google Meet, etc.)"
                    }
                    disabled={isSubmitting}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <VideocamIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              ) : (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="location"
                    name="location"
                    label="Location"
                    variant="outlined"
                    value={formik.values.location}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.location && Boolean(formik.errors.location)}
                    helperText={
                      (formik.touched.location && formik.errors.location) || 
                      "Enter the physical location for the session"
                    }
                    disabled={isSubmitting}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LocationIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              )}
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="max_students"
                  name="max_students"
                  label="Maximum Students"
                  variant="outlined"
                  type="number"
                  value={formik.values.max_students}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.max_students && Boolean(formik.errors.max_students)}
                  helperText={formik.touched.max_students && formik.errors.max_students}
                  disabled={isSubmitting}
                  InputProps={{
                    inputProps: { min: 1, max: 100 }
                  }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl 
                  fullWidth 
                  error={formik.touched.status && Boolean(formik.errors.status)}
                  disabled={isSubmitting}
                >
                  <InputLabel id="status-label">Status</InputLabel>
                  <Select
                    labelId="status-label"
                    id="status"
                    name="status"
                    value={formik.values.status}
                    label="Status"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  >
                    <MenuItem value="scheduled">Scheduled</MenuItem>
                    <MenuItem value="in_progress">In Progress</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                  </Select>
                  {formik.touched.status && formik.errors.status && (
                    <FormHelperText>{formik.errors.status as string}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      id="is_recurring"
                      name="is_recurring"
                      checked={formik.values.is_recurring}
                      onChange={formik.handleChange}
                      disabled={isSubmitting}
                    />
                  }
                  label="Recurring Session"
                />
              </Grid>
              
              {formik.values.is_recurring && (
                <Grid item xs={12}>
                  <FormControl 
                    fullWidth 
                    error={formik.touched.recurrence_pattern && Boolean(formik.errors.recurrence_pattern)}
                    disabled={isSubmitting}
                  >
                    <InputLabel id="recurrence-label">Recurrence Pattern</InputLabel>
                    <Select
                      labelId="recurrence-label"
                      id="recurrence_pattern"
                      name="recurrence_pattern"
                      value={formik.values.recurrence_pattern}
                      label="Recurrence Pattern"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    >
                      <MenuItem value="daily">Daily</MenuItem>
                      <MenuItem value="weekly">Weekly</MenuItem>
                      <MenuItem value="biweekly">Bi-weekly</MenuItem>
                      <MenuItem value="monthly">Monthly</MenuItem>
                    </Select>
                    {formik.touched.recurrence_pattern && formik.errors.recurrence_pattern && (
                      <FormHelperText>{formik.errors.recurrence_pattern as string}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              )}
              
              <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <Button
                  variant="outlined"
                  onClick={() => navigate(isEditMode ? `/sessions/${sessionId}` : '/sessions')}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  startIcon={isSubmitting ? <CircularProgress size={24} /> : <SaveIcon />}
                  disabled={isSubmitting}
                >
                  {isEditMode ? 'Update Session' : 'Create Session'}
                </Button>
              </Grid>
            </Grid>
          </form>
        </Paper>
        
        {/* Success message */}
        <Snackbar
          open={!!successMessage}
          autoHideDuration={3000}
          onClose={() => setSuccessMessage(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert severity="success" onClose={() => setSuccessMessage(null)}>
            {successMessage}
          </Alert>
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
};

export default SessionForm;
