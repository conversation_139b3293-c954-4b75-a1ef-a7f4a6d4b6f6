import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  FormControlLabel,
  Switch,
  CircularProgress,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  IconButton,
  Divider,
  Card,
  CardContent,
  Tab,
  Tabs
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Upload as UploadIcon,
  AutoAwesome as AIIcon
} from '@mui/icons-material';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useFormik, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  getQuestion,
  createQuestion,
  updateQuestion,
  QuestionCreate,
  QuestionUpdate,
  QuestionType,
  DifficultyLevel,
  generateAIQuestions
} from '../../api/questions';
import { getCourses } from '../../api/courses';
import { useAuth } from '../../contexts/AuthContext';
import { useContextualNavigation } from '../../hooks/useAdminContext';
import QuestionImageUpload from '../../components/Questions/QuestionImageUpload';

// Interface for option
interface Option {
  key: string;
  value: string;
}

// Tab panel component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`question-form-tabpanel-${index}`}
      aria-labelledby={`question-form-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Validation schema
const validationSchema = Yup.object({
  content: Yup.string()
    .required('Question content is required')
    .max(1000, 'Question content must be at most 1000 characters'),
  question_type: Yup.string()
    .required('Question type is required')
    .oneOf(['multiple_choice', 'flashcard', 'open_ended'], 'Invalid question type'),
  difficulty: Yup.string()
    .required('Difficulty is required')
    .oneOf(['easy', 'medium', 'hard'], 'Invalid difficulty level'),
  topic: Yup.string()
    .max(100, 'Topic must be at most 100 characters'),
  answer: Yup.string()
    .required('Answer is required'),
  explanation: Yup.string()
    .max(1000, 'Explanation must be at most 1000 characters'),
  media_url: Yup.string()
    .url('Must be a valid URL')
    .nullable(),
  is_active: Yup.boolean(),
  course_id: Yup.number()
    .required('Course is required'),
});

const QuestionForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const isEditMode = !!id;
  const questionId = parseInt(id || '0');
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { getPath } = useContextualNavigation();

  // Get courseId from location state if available
  const initialCourseId = location.state?.courseId;

  // State for success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // State for options (for multiple choice questions)
  const [options, setOptions] = useState<Option[]>([
    { key: 'A', value: '' },
    { key: 'B', value: '' },
    { key: 'C', value: '' },
    { key: 'D', value: '' }
  ]);

  // State for tabs
  const [tabValue, setTabValue] = useState(0);

  // State for AI generation
  const [aiTopic, setAiTopic] = useState('');
  const [aiNumQuestions, setAiNumQuestions] = useState(3);
  const [aiGeneratedQuestions, setAiGeneratedQuestions] = useState<any[]>([]);

  // Fetch question data if in edit mode
  const {
    data: question,
    isLoading: isLoadingQuestion,
    error: questionError
  } = useQuery({
    queryKey: ['question', questionId],
    queryFn: () => getQuestion(questionId),
    enabled: isEditMode
  });

  // Fetch courses for dropdown
  const {
    data: courses = [],
    isLoading: isLoadingCourses
  } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  // Create question mutation
  const createMutation = useMutation({
    mutationFn: (data: QuestionCreate) => createQuestion(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['questions'] });
      setSuccessMessage('Question created successfully');
      setTimeout(() => {
        navigate(getPath(`/questions/${data.id}`));
      }, 1500);
    }
  });

  // Update question mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: QuestionUpdate }) =>
      updateQuestion(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['questions'] });
      queryClient.invalidateQueries({ queryKey: ['question', questionId] });
      setSuccessMessage('Question updated successfully');
      setTimeout(() => {
        navigate(getPath(`/questions/${questionId}`));
      }, 1500);
    }
  });

  // AI generate questions mutation
  const generateAIMutation = useMutation({
    mutationFn: (data: {
      course_id: number;
      question_type: QuestionType;
      difficulty: DifficultyLevel;
      topic: string;
      num_questions: number;
    }) => generateAIQuestions(data),
    onSuccess: (data) => {
      setAiGeneratedQuestions(data);
    }
  });

  // Setup formik
  const formik = useFormik({
    initialValues: {
      content: '',
      question_type: 'multiple_choice' as QuestionType,
      difficulty: 'medium' as DifficultyLevel,
      topic: '',
      answer: '',
      explanation: '',
      media_url: '',
      is_active: true,
      course_id: initialCourseId || '',
    },
    validationSchema,
    onSubmit: (values, { setSubmitting }: FormikHelpers<any>) => {
      // Convert options to object format for API
      let optionsObj = {};
      if (values.question_type === 'multiple_choice') {
        optionsObj = options.reduce((acc, option) => {
          if (option.value.trim()) {
            acc[option.key] = option.value;
          }
          return acc;
        }, {} as Record<string, string>);

        // Validate that we have at least 2 options
        if (Object.keys(optionsObj).length < 2) {
          formik.setFieldError('options', 'At least 2 options are required');
          setSubmitting(false);
          return;
        }

        // Validate that answer is one of the option keys
        if (!Object.keys(optionsObj).includes(values.answer)) {
          formik.setFieldError('answer', 'Answer must be one of the option keys');
          setSubmitting(false);
          return;
        }
      }

      const questionData = {
        ...values,
        course_id: Number(values.course_id),
        options: values.question_type === 'multiple_choice' ? optionsObj : undefined,
      };

      if (isEditMode) {
        updateMutation.mutate({ id: questionId, data: questionData });
      } else {
        createMutation.mutate(questionData);
      }
    },
    enableReinitialize: true
  });

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle option change
  const handleOptionChange = (index: number, field: 'key' | 'value', value: string) => {
    const newOptions = [...options];
    newOptions[index][field] = value;
    setOptions(newOptions);
  };

  // Add new option
  const handleAddOption = () => {
    // Generate next option key (A, B, C, ... Z)
    const lastKey = options[options.length - 1].key;
    const nextKey = String.fromCharCode(lastKey.charCodeAt(0) + 1);

    setOptions([...options, { key: nextKey, value: '' }]);
  };

  // Remove option
  const handleRemoveOption = (index: number) => {
    if (options.length <= 2) {
      return; // Keep at least 2 options
    }

    const newOptions = [...options];
    newOptions.splice(index, 1);
    setOptions(newOptions);
  };

  // Generate AI questions
  const handleGenerateAI = () => {
    if (!aiTopic || !formik.values.course_id) return;

    generateAIMutation.mutate({
      course_id: Number(formik.values.course_id),
      question_type: formik.values.question_type as QuestionType,
      difficulty: formik.values.difficulty as DifficultyLevel,
      topic: aiTopic,
      num_questions: aiNumQuestions
    });
  };

  // Use AI generated question
  const handleUseAIQuestion = (question: any) => {
    // Update form with AI generated question
    formik.setValues({
      ...formik.values,
      content: question.content,
      answer: question.answer,
      explanation: question.explanation || '',
    });

    // If it's a multiple choice question, update options
    if (question.question_type === 'multiple_choice' && question.options) {
      const optionEntries = Object.entries(question.options);
      const newOptions = optionEntries.map(([key, value]) => ({ key, value: value as string }));
      setOptions(newOptions);
    }

    // Switch back to form tab
    setTabValue(0);
  };

  // Update form values when question data is loaded
  useEffect(() => {
    if (question) {
      formik.setValues({
        content: question.content,
        question_type: question.question_type,
        difficulty: question.difficulty,
        topic: question.topic || '',
        answer: question.answer,
        explanation: question.explanation || '',
        media_url: question.media_url || '',
        is_active: question.is_active,
        course_id: question.course_id.toString(),
      });

      // Set options for multiple choice questions
      if (question.question_type === 'multiple_choice' && question.options) {
        const optionEntries = Object.entries(question.options);
        const newOptions = optionEntries.map(([key, value]) => ({ key, value }));
        setOptions(newOptions);
      }
    }
  }, [question]);

  // Handle loading and error states
  if (isEditMode && isLoadingQuestion) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (isEditMode && questionError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading question: {(questionError as Error).message}
      </Alert>
    );
  }

  const isSubmitting = createMutation.isPending || updateMutation.isPending;
  const error = createMutation.error || updateMutation.error;

  return (
    <Box sx={{ px: { xs: 1, sm: 2 }, py: { xs: 1, sm: 2 } }}>
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        mb: 3,
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 1, sm: 0 }
      }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          width: { xs: '100%', sm: 'auto' }
        }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate(isEditMode ? getPath(`/questions/${questionId}`) : getPath('/questions'))}
            sx={{ mr: 2 }}
            size="medium"
          >
            <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>
              {isEditMode ? 'Back to Question' : 'Back to Questions'}
            </Box>
            <Box component="span" sx={{ display: { xs: 'inline', sm: 'none' } }}>
              Back
            </Box>
          </Button>

          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontSize: { xs: '1.5rem', sm: '2.125rem' }
            }}
          >
            {isEditMode ? 'Edit Question' : 'Add New Question'}
          </Typography>
        </Box>
      </Box>

      <Paper sx={{ mb: 4, mx: { xs: 0, sm: 0 } }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="question form tabs"
            variant={{ xs: 'fullWidth', sm: 'standard' }}
            sx={{
              '& .MuiTab-root': {
                fontSize: { xs: '0.875rem', sm: '0.875rem' },
                minWidth: { xs: 'auto', sm: 160 }
              }
            }}
          >
            <Tab
              label="Question Form"
              id="question-form-tab-0"
              sx={{
                fontSize: { xs: '0.8rem', sm: '0.875rem' }
              }}
            />
            <Tab
              label="AI Generator"
              id="question-form-tab-1"
              disabled={!formik.values.course_id || isEditMode}
              sx={{
                fontSize: { xs: '0.8rem', sm: '0.875rem' }
              }}
            />
          </Tabs>
        </Box>

        {/* Question Form Tab */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ p: { xs: 2, sm: 3 } }}>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {(error as Error).message || 'An error occurred. Please try again.'}
              </Alert>
            )}

            <form onSubmit={formik.handleSubmit}>
              <Grid container spacing={{ xs: 2, sm: 3 }}>
                <Grid item xs={12} md={6}>
                  <FormControl
                    fullWidth
                    error={formik.touched.course_id && Boolean(formik.errors.course_id)}
                    disabled={isLoadingCourses || isSubmitting}
                  >
                    <InputLabel id="course-label">Course</InputLabel>
                    <Select
                      labelId="course-label"
                      id="course_id"
                      name="course_id"
                      value={formik.values.course_id}
                      label="Course"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    >
                      {courses.map((course) => (
                        <MenuItem key={course.id} value={course.id.toString()}>
                          {course.name} ({course.code})
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.course_id && formik.errors.course_id && (
                      <FormHelperText>{formik.errors.course_id as string}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <FormControl
                        fullWidth
                        error={formik.touched.question_type && Boolean(formik.errors.question_type)}
                        disabled={isSubmitting || isEditMode} // Don't allow changing type in edit mode
                      >
                        <InputLabel id="type-label">Question Type</InputLabel>
                        <Select
                          labelId="type-label"
                          id="question_type"
                          name="question_type"
                          value={formik.values.question_type}
                          label="Question Type"
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        >
                          <MenuItem value="multiple_choice">Multiple Choice</MenuItem>
                          <MenuItem value="flashcard">Flashcard</MenuItem>
                          <MenuItem value="open_ended">Open Ended</MenuItem>
                        </Select>
                        {formik.touched.question_type && formik.errors.question_type && (
                          <FormHelperText>{formik.errors.question_type as string}</FormHelperText>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid item xs={6}>
                      <FormControl
                        fullWidth
                        error={formik.touched.difficulty && Boolean(formik.errors.difficulty)}
                        disabled={isSubmitting}
                      >
                        <InputLabel id="difficulty-label">Difficulty</InputLabel>
                        <Select
                          labelId="difficulty-label"
                          id="difficulty"
                          name="difficulty"
                          value={formik.values.difficulty}
                          label="Difficulty"
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        >
                          <MenuItem value="easy">Easy</MenuItem>
                          <MenuItem value="medium">Medium</MenuItem>
                          <MenuItem value="hard">Hard</MenuItem>
                        </Select>
                        {formik.touched.difficulty && formik.errors.difficulty && (
                          <FormHelperText>{formik.errors.difficulty as string}</FormHelperText>
                        )}
                      </FormControl>
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    id="topic"
                    name="topic"
                    label="Topic"
                    variant="outlined"
                    value={formik.values.topic}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.topic && Boolean(formik.errors.topic)}
                    helperText={(formik.touched.topic && formik.errors.topic) || "The topic this question belongs to"}
                    disabled={isSubmitting}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="content"
                    name="content"
                    label="Question Content"
                    variant="outlined"
                    multiline
                    rows={4}
                    value={formik.values.content}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.content && Boolean(formik.errors.content)}
                    helperText={formik.touched.content && formik.errors.content}
                    disabled={isSubmitting}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    Question Image (optional)
                  </Typography>
                  {isEditMode && questionId ? (
                    <QuestionImageUpload
                      questionId={questionId}
                      currentImageUrl={formik.values.media_url}
                      onImageUploaded={(imageUrl) => {
                        formik.setFieldValue('media_url', imageUrl);
                      }}
                      onImageRemoved={() => {
                        formik.setFieldValue('media_url', '');
                      }}
                    />
                  ) : (
                    <Alert severity="info">
                      Save the question first to upload an image.
                    </Alert>
                  )}
                </Grid>

                {formik.values.question_type === 'multiple_choice' && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" gutterBottom>
                      Options
                    </Typography>

                    {options.map((option, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          mb: 2,
                          gap: 2
                        }}
                      >
                        <TextField
                          label="Key"
                          value={option.key}
                          onChange={(e) => handleOptionChange(index, 'key', e.target.value)}
                          sx={{ width: '80px' }}
                          disabled={isSubmitting}
                        />

                        <TextField
                          fullWidth
                          label={`Option ${option.key}`}
                          value={option.value}
                          onChange={(e) => handleOptionChange(index, 'value', e.target.value)}
                          disabled={isSubmitting}
                        />

                        <IconButton
                          color="error"
                          onClick={() => handleRemoveOption(index)}
                          disabled={options.length <= 2 || isSubmitting}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    ))}

                    <Button
                      startIcon={<AddIcon />}
                      onClick={handleAddOption}
                      disabled={options.length >= 26 || isSubmitting} // Limit to A-Z
                    >
                      Add Option
                    </Button>

                    {formik.errors.options && (
                      <FormHelperText error>{formik.errors.options as string}</FormHelperText>
                    )}
                  </Grid>
                )}

                <Grid item xs={12}>
                  {formik.values.question_type === 'multiple_choice' ? (
                    <FormControl
                      fullWidth
                      error={formik.touched.answer && Boolean(formik.errors.answer)}
                      disabled={isSubmitting}
                    >
                      <InputLabel id="answer-label">Correct Answer</InputLabel>
                      <Select
                        labelId="answer-label"
                        id="answer"
                        name="answer"
                        value={formik.values.answer}
                        label="Correct Answer"
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                      >
                        {options.map((option) => (
                          <MenuItem key={option.key} value={option.key}>
                            {option.key}: {option.value.substring(0, 30)}{option.value.length > 30 ? '...' : ''}
                          </MenuItem>
                        ))}
                      </Select>
                      {formik.touched.answer && formik.errors.answer && (
                        <FormHelperText>{formik.errors.answer as string}</FormHelperText>
                      )}
                    </FormControl>
                  ) : (
                    <TextField
                      fullWidth
                      id="answer"
                      name="answer"
                      label={formik.values.question_type === 'flashcard' ? 'Answer' : 'Sample Answer'}
                      variant="outlined"
                      multiline
                      rows={3}
                      value={formik.values.answer}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.answer && Boolean(formik.errors.answer)}
                      helperText={formik.touched.answer && formik.errors.answer}
                      disabled={isSubmitting}
                    />
                  )}
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="explanation"
                    name="explanation"
                    label="Explanation (optional)"
                    variant="outlined"
                    multiline
                    rows={3}
                    value={formik.values.explanation}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.explanation && Boolean(formik.errors.explanation)}
                    helperText={formik.touched.explanation && formik.errors.explanation}
                    disabled={isSubmitting}
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        id="is_active"
                        name="is_active"
                        checked={formik.values.is_active}
                        onChange={formik.handleChange}
                        disabled={isSubmitting}
                      />
                    }
                    label="Active"
                  />
                </Grid>

                <Grid item xs={12} sx={{
                  display: 'flex',
                  justifyContent: { xs: 'stretch', sm: 'flex-end' },
                  gap: 2,
                  flexDirection: { xs: 'column', sm: 'row' }
                }}>
                  <Button
                    variant="outlined"
                    onClick={() => navigate(isEditMode ? getPath(`/questions/${questionId}`) : getPath('/questions'))}
                    disabled={isSubmitting}
                    fullWidth={{ xs: true, sm: false }}
                  >
                    Cancel
                  </Button>

                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    startIcon={isSubmitting ? <CircularProgress size={24} /> : <SaveIcon />}
                    disabled={isSubmitting}
                    fullWidth={{ xs: true, sm: false }}
                  >
                    {isEditMode ? 'Update Question' : 'Create Question'}
                  </Button>
                </Grid>
              </Grid>
            </form>
          </Box>
        </TabPanel>

        {/* AI Generator Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Generate Questions with AI
            </Typography>

            <Typography variant="body2" color="text.secondary" paragraph>
              Let AI help you create questions based on a topic. The questions will be generated for the selected course, question type, and difficulty level.
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Topic"
                  value={aiTopic}
                  onChange={(e) => setAiTopic(e.target.value)}
                  helperText="Enter a specific topic for the questions"
                  disabled={generateAIMutation.isPending}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth disabled={generateAIMutation.isPending}>
                  <InputLabel id="ai-num-questions-label">Number of Questions</InputLabel>
                  <Select
                    labelId="ai-num-questions-label"
                    value={aiNumQuestions}
                    label="Number of Questions"
                    onChange={(e) => setAiNumQuestions(Number(e.target.value))}
                  >
                    <MenuItem value={1}>1</MenuItem>
                    <MenuItem value={3}>3</MenuItem>
                    <MenuItem value={5}>5</MenuItem>
                    <MenuItem value={10}>10</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <Button
                  fullWidth
                  variant="contained"
                  color="primary"
                  startIcon={generateAIMutation.isPending ? <CircularProgress size={24} /> : <AIIcon />}
                  onClick={handleGenerateAI}
                  disabled={!aiTopic || generateAIMutation.isPending}
                  sx={{ height: '56px' }}
                >
                  Generate Questions
                </Button>
              </Grid>

              {generateAIMutation.error && (
                <Grid item xs={12}>
                  <Alert severity="error">
                    {(generateAIMutation.error as Error).message || 'Failed to generate questions. Please try again.'}
                  </Alert>
                </Grid>
              )}

              {aiGeneratedQuestions.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                    Generated Questions
                  </Typography>

                  <Typography variant="body2" color="text.secondary" paragraph>
                    Click "Use This Question" to fill the form with the generated question.
                  </Typography>

                  <Grid container spacing={2}>
                    {aiGeneratedQuestions.map((question, index) => (
                      <Grid item xs={12} key={index}>
                        <Card variant="outlined">
                          <CardContent>
                            <Typography variant="subtitle1" gutterBottom>
                              Question {index + 1}:
                            </Typography>
                            <Typography variant="body1" paragraph>
                              {question.content}
                            </Typography>

                            <Typography variant="subtitle2" gutterBottom>
                              Answer:
                            </Typography>
                            <Typography variant="body2" paragraph>
                              {question.answer}
                            </Typography>

                            {question.explanation && (
                              <>
                                <Typography variant="subtitle2" gutterBottom>
                                  Explanation:
                                </Typography>
                                <Typography variant="body2" paragraph>
                                  {question.explanation}
                                </Typography>
                              </>
                            )}

                            <Button
                              variant="contained"
                              color="primary"
                              onClick={() => handleUseAIQuestion(question)}
                            >
                              Use This Question
                            </Button>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Grid>
              )}
            </Grid>
          </Box>
        </TabPanel>
      </Paper>

      {/* Success message */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={3000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setSuccessMessage(null)}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default QuestionForm;
