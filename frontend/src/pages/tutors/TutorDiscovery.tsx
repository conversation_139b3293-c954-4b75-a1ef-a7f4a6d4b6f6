import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Avatar,
  Rating,
  Chip,
  Button,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Search,
  FilterList,
  ExpandMore,
  LocationOn,
  AttachMoney,
  Event,
  Star,
  School,
  Person,
  Language,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { getTutors, searchTutors, TutorProfile, TutorSearchFilters } from '../../api/tutors';
import { getCourses } from '../../api/courses';

const TutorDiscovery: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<TutorSearchFilters>({
    is_available: true,
  });
  const [priceRange, setPriceRange] = useState<number[]>([0, 100]);
  const [showFilters, setShowFilters] = useState(false);

  // Fetch courses for filter options
  const { data: courses } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
  });

  // Fetch tutors
  const {
    data: tutors,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['tutors', filters],
    queryFn: () => searchTutors(filters),
  });

  const handleFilterChange = (key: keyof TutorSearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handlePriceRangeChange = (event: Event, newValue: number | number[]) => {
    setPriceRange(newValue as number[]);
    setFilters(prev => ({
      ...prev,
      max_hourly_rate: (newValue as number[])[1],
    }));
  };

  const TutorCard = ({ tutor }: { tutor: TutorProfile }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        '&:hover': {
          boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
          transform: 'translateY(-2px)',
          transition: 'all 0.3s ease'
        }
      }}>
        <CardContent sx={{ flex: 1, p: { xs: 2, sm: 3 } }}>
          {/* Tutor Header */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
            <Avatar
              src={tutor.user?.profile_picture_url}
              sx={{
                width: { xs: 50, sm: 60 },
                height: { xs: 50, sm: 60 },
                mr: 2,
                border: '2px solid',
                borderColor: 'primary.light'
              }}
            >
              {tutor.user?.full_name?.charAt(0)}
            </Avatar>
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  fontSize: { xs: '1rem', sm: '1.25rem' },
                  fontWeight: 600,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {tutor.user?.full_name}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, flexWrap: 'wrap', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Rating value={tutor.average_rating || 0} readOnly size="small" />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
                    ({tutor.total_reviews})
                  </Typography>
                </Box>
                <Chip
                  label={tutor.is_available ? 'Available' : 'Unavailable'}
                  color={tutor.is_available ? 'success' : 'default'}
                  size="small"
                />
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                {tutor.experience_years} years experience
              </Typography>
            </Box>
          </Box>

          {/* Bio */}
          <Typography
            variant="body2"
            sx={{
              mb: 2,
              fontSize: { xs: '0.875rem', sm: '1rem' },
              lineHeight: 1.4,
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}
          >
            {tutor.bio || 'No bio available'}
          </Typography>

          {/* Specializations */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="body2"
              color="text.secondary"
              gutterBottom
              sx={{ fontWeight: 500, fontSize: '0.875rem' }}
            >
              Specializations:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {tutor.specializations && tutor.specializations.length > 0 ? (
                <>
                  {tutor.specializations.slice(0, 3).map((spec) => (
                    <Chip
                      key={spec.id}
                      label={spec.name}
                      size="small"
                      variant="outlined"
                      sx={{ fontSize: '0.75rem' }}
                    />
                  ))}
                  {tutor.specializations.length > 3 && (
                    <Chip
                      label={`+${tutor.specializations.length - 3} more`}
                      size="small"
                      variant="outlined"
                      color="primary"
                      sx={{ fontSize: '0.75rem' }}
                    />
                  )}
                </>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                  No specializations listed
                </Typography>
              )}
            </Box>
          </Box>

          {/* Details Grid */}
          <Box sx={{
            display: 'grid',
            gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
            gap: 1,
            mb: 2
          }}>
            {/* Price */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AttachMoney sx={{ fontSize: 16, mr: 0.5, color: 'primary.main' }} />
              <Typography variant="h6" color="primary" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                ${tutor.hourly_rate ? Number(tutor.hourly_rate).toFixed(2) : 'N/A'}/hour
              </Typography>
            </Box>

            {/* Session Type */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <LocationOn sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                {tutor.preferred_session_type === 'online' ? 'Online Only' :
                 tutor.preferred_session_type === 'in_person' ? 'In-Person Only' :
                 'Online & In-Person'}
              </Typography>
            </Box>
          </Box>

          {/* Additional Info */}
          {(tutor.location || tutor.languages) && (
            <Box sx={{ mb: 2 }}>
              {tutor.location && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                  <LocationOn sx={{ fontSize: 14, mr: 0.5, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                    {tutor.location}
                  </Typography>
                </Box>
              )}
              {tutor.languages && tutor.languages.length > 0 && (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Language sx={{ fontSize: 14, mr: 0.5, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                    {tutor.languages.slice(0, 2).join(', ')}
                    {tutor.languages.length > 2 && ` +${tutor.languages.length - 2} more`}
                  </Typography>
                </Box>
              )}
            </Box>
          )}
        </CardContent>

        {/* Actions */}
        <Box sx={{ p: { xs: 2, sm: 3 }, pt: 0 }}>
          <Box sx={{
            display: 'flex',
            gap: 1,
            flexDirection: { xs: 'column', sm: 'row' }
          }}>
            <Button
              fullWidth
              variant="outlined"
              size="medium"
              onClick={() => navigate(`/tutors/${tutor.id}`)}
              sx={{
                minHeight: { xs: '40px', sm: '36px' },
                fontSize: { xs: '0.875rem', sm: '0.875rem' }
              }}
            >
              View Profile
            </Button>
            <Button
              fullWidth
              variant="contained"
              size="medium"
              disabled={!tutor.is_available}
              onClick={() => {/* Handle book session */}}
              sx={{
                minHeight: { xs: '40px', sm: '36px' },
                fontSize: { xs: '0.875rem', sm: '0.875rem' }
              }}
            >
              Book Session
            </Button>
          </Box>
        </Box>
      </Card>
    </motion.div>
  );

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading tutors: {error.message}
      </Alert>
    );
  }

  return (
    <Box sx={{ px: { xs: 1, sm: 2, md: 0 }, py: { xs: 1, sm: 2 } }}>
      <Typography
        variant="h4"
        gutterBottom
        sx={{
          fontSize: { xs: '1.75rem', sm: '2.125rem' },
          fontWeight: 600,
          textAlign: { xs: 'center', sm: 'left' }
        }}
      >
        Find a Tutor
      </Typography>
      <Typography
        variant="body1"
        color="text.secondary"
        sx={{
          mb: 3,
          textAlign: { xs: 'center', sm: 'left' },
          fontSize: { xs: '0.875rem', sm: '1rem' }
        }}
      >
        Connect with experienced tutors to boost your academic performance
      </Typography>

      {/* Search and Filters */}
      <Paper sx={{ p: { xs: 2, sm: 3 }, mb: 3, borderRadius: 2 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          gap: 2,
          alignItems: 'stretch'
        }}>
          <TextField
            fullWidth
            placeholder="Search by name, subject, or keyword..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiInputBase-root': {
                fontSize: { xs: '0.875rem', sm: '1rem' }
              }
            }}
          />
          <Button
            variant="outlined"
            startIcon={<FilterList />}
            onClick={() => setShowFilters(!showFilters)}
            sx={{
              minWidth: { xs: 'auto', md: '120px' },
              minHeight: { xs: '48px', sm: '56px' }
            }}
          >
            Filters
          </Button>
        </Box>

        {/* Advanced Filters */}
        {showFilters && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
              Advanced Filters
            </Typography>
            <Box sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
              gap: 3
            }}>
              <FormControl fullWidth size="small">
                <InputLabel>Subject</InputLabel>
                <Select
                  value={filters.course_ids?.[0] || ''}
                  label="Subject"
                  onChange={(e) => handleFilterChange('course_ids', e.target.value ? [Number(e.target.value)] : undefined)}
                  sx={{
                    '& .MuiSelect-select': {
                      fontSize: { xs: '0.875rem', sm: '1rem' }
                    }
                  }}
                >
                  <MenuItem value="">All Subjects</MenuItem>
                  {courses?.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth size="small">
                <InputLabel>Session Type</InputLabel>
                <Select
                  value={filters.session_type || ''}
                  label="Session Type"
                  onChange={(e) => handleFilterChange('session_type', e.target.value || undefined)}
                  sx={{
                    '& .MuiSelect-select': {
                      fontSize: { xs: '0.875rem', sm: '1rem' }
                    }
                  }}
                >
                  <MenuItem value="">Any</MenuItem>
                  <MenuItem value="online">Online</MenuItem>
                  <MenuItem value="in_person">In-Person</MenuItem>
                  <MenuItem value="both">Both</MenuItem>
                </Select>
              </FormControl>

              <Box>
                <Typography variant="body2" gutterBottom sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                  Min Rating
                </Typography>
                <Slider
                  value={filters.min_rating || 0}
                  onChange={(e, value) => handleFilterChange('min_rating', value)}
                  min={0}
                  max={5}
                  step={0.5}
                  marks
                  valueLabelDisplay="auto"
                  sx={{ mt: 1 }}
                />
              </Box>

              <Box>
                <Typography variant="body2" gutterBottom sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                  Price Range ($/hour)
                </Typography>
                <Slider
                  value={priceRange}
                  onChange={handlePriceRangeChange}
                  min={0}
                  max={200}
                  step={5}
                  valueLabelDisplay="auto"
                  sx={{ mt: 1 }}
                />
              </Box>
            </Box>
          </Box>
        )}
      </Paper>

      {/* Results */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography
          variant="h6"
          sx={{
            fontSize: { xs: '1.1rem', sm: '1.25rem' },
            fontWeight: 500
          }}
        >
          {tutors?.length || 0} tutors found
        </Typography>
        {tutors && tutors.length > 0 && (
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
            Showing all results
          </Typography>
        )}
      </Box>

      {/* Tutors Grid */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: {
          xs: '1fr',
          sm: 'repeat(2, 1fr)',
          md: 'repeat(3, 1fr)',
          lg: 'repeat(4, 1fr)'
        },
        gap: { xs: 2, sm: 3 },
        mb: 3
      }}>
        {tutors?.map((tutor) => (
          <TutorCard key={tutor.id} tutor={tutor} />
        ))}
      </Box>

      {tutors?.length === 0 && (
        <Paper sx={{
          p: { xs: 3, sm: 4 },
          textAlign: 'center',
          borderRadius: 2,
          bgcolor: 'grey.50'
        }}>
          <Typography
            variant="h6"
            color="text.secondary"
            gutterBottom
            sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}
          >
            No tutors found
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}
          >
            Try adjusting your search criteria or filters
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default TutorDiscovery;
