import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Chip,
  Divider,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  TextField,
  Snackbar,
  Tab,
  Tabs,
  LinearProgress,
  IconButton,
  Container,
  useTheme,
  useMediaQuery,
  Badge,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Switch,
  Card,
  CardContent,
  CardActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Pagination
} from '@mui/material';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  School as SchoolIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Book as BookIcon,
  Event as EventIcon,
  QuestionAnswer as QuestionIcon,
  Lock as LockIcon,
  PhotoCamera as PhotoCameraIcon,
  CloudUpload as CloudUploadIcon,
  Phone as PhoneIcon,
  Wc as GenderIcon,
  LocationOn as LocationIcon,
  Class as ClassIcon,
  Cake as BirthdayIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  AccountCircle as AccountIcon,
  VerifiedUser as VerifiedIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Delete as DeleteIcon,
  Bookmark as BookmarkIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  History as HistoryIcon,
  Timer as TimerIcon,
  Score as ScoreIcon,
  PlayArrow as PlayArrowIcon,
  BarChart as BarChartIcon,
  DonutLarge as DonutLargeIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { updateCurrentUser, uploadProfilePicture, UserUpdate } from '../../api/users';
import { useAuth } from '../../contexts/AuthContext';
import { User } from '../../api/auth';
import { nigerianUniversities } from '../../data/nigerianUniversities';
import {
  getExams,
  getQuestionAttempts,
  getBookmarks
} from '../../api/studentProgress';
import { getCourses, Course } from '../../api/courses';

// Extended User interface for profile page
interface ExtendedUser extends User {
  institution?: {
    id: number;
    name: string;
  };
  courses?: Course[];
  sessions?: {
    id: number;
    title: string;
    start_time: string;
  }[];
  questions?: {
    id: number;
    content: string;
    created_at: string;
  }[];
}

// Nigerian states
const nigerianStates = [
  'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno',
  'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT', 'Gombe', 'Imo',
  'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara', 'Lagos', 'Nasarawa',
  'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba',
  'Yobe', 'Zamfara'
];

// Education levels
const educationLevels = ['100', '200', '300', '400', '500', '600'];

// Comprehensive list of academic departments
const commonDepartments = [
  // Arts & Humanities
  'African Studies', 'American Studies', 'Anthropology', 'Archaeology', 'Architecture', 'Art History',
  'Arts Management', 'Asian Studies', 'Classical Studies', 'Communication Studies', 'Comparative Literature',
  'Creative Writing', 'Criminal Justice', 'Dance', 'Design', 'Digital Media', 'Drama', 'English Language',
  'English Literature', 'Environmental Studies', 'Ethnic Studies', 'European Studies', 'Film Studies',
  'Fine Arts', 'Foreign Languages', 'French', 'Gender Studies', 'Geography', 'German', 'Graphic Design',
  'History', 'Human Rights', 'Humanities', 'International Relations', 'Italian', 'Japanese', 'Jewish Studies',
  // Business & Economics
  'Accounting', 'Actuarial Science', 'Advertising', 'Agricultural Economics', 'Banking', 'Business Administration',
  'Business Analytics', 'Business Economics', 'Business Law', 'Consumer Economics', 'Corporate Finance', 'E-commerce',
  'Economics', 'Entrepreneurship', 'Finance', 'Financial Management', 'Human Resources', 'Industrial Relations',
  'International Business', 'Investment Management', 'Logistics', 'Management', 'Marketing', 'Operations Management',
  'Organizational Behavior', 'Project Management', 'Public Relations', 'Real Estate', 'Risk Management', 'Supply Chain Management',
  'Taxation', 'Tourism Management',
  // Science & Technology
  'Aerospace Engineering', 'Agricultural Engineering', 'Applied Mathematics', 'Applied Physics', 'Artificial Intelligence',
  'Astronomy', 'Astrophysics', 'Biochemistry', 'Bioengineering', 'Biology', 'Biomedical Engineering', 'Biotechnology',
  'Chemical Engineering', 'Chemistry', 'Civil Engineering', 'Computer Engineering', 'Computer Science', 'Cybersecurity',
  'Data Science', 'Electrical Engineering', 'Environmental Engineering', 'Food Science', 'Genetics', 'Geology',
  'Industrial Engineering', 'Information Systems', 'Information Technology', 'Marine Biology', 'Materials Science',
  'Mathematics', 'Mechanical Engineering', 'Mechatronics', 'Meteorology', 'Microbiology', 'Mining Engineering',
  'Molecular Biology', 'Nanotechnology', 'Nuclear Engineering', 'Petroleum Engineering', 'Pharmaceutical Sciences',
  'Physics', 'Robotics', 'Software Engineering', 'Statistics', 'Systems Engineering', 'Telecommunications',
  // Health & Medicine
  'Anatomy', 'Anesthesiology', 'Cardiology', 'Dentistry', 'Dermatology', 'Emergency Medicine', 'Epidemiology',
  'Exercise Science', 'Family Medicine', 'Health Administration', 'Health Informatics', 'Health Sciences',
  'Immunology', 'Kinesiology', 'Medical Laboratory Science', 'Medicine', 'Neurology', 'Neuroscience', 'Nursing',
  'Nutrition', 'Obstetrics', 'Occupational Therapy', 'Ophthalmology', 'Optometry', 'Orthopedics', 'Pathology',
  'Pediatrics', 'Pharmacy', 'Physical Therapy', 'Physician Assistant Studies', 'Physiology', 'Psychiatry',
  'Psychology', 'Public Health', 'Radiology', 'Rehabilitation', 'Speech Therapy', 'Sports Medicine', 'Surgery',
  'Veterinary Medicine',
  // Social Sciences & Education
  'Counseling', 'Criminology', 'Early Childhood Education', 'Education', 'Educational Administration',
  'Educational Psychology', 'Elementary Education', 'International Development', 'Law', 'Library Science',
  'Linguistics', 'Political Science', 'Secondary Education', 'Social Work', 'Sociology', 'Special Education',
  'Urban Planning'
];

// Helper function to format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Helper function to get role color
const getRoleColor = (role: string) => {
  switch (role) {
    case 'admin':
      return 'error';
    case 'tutor':
      return 'primary';
    case 'student':
      return 'success';
    default:
      return 'default';
  }
};

// Helper function to get avatar color
const getAvatarColor = (role: string) => {
  switch (role) {
    case 'admin':
      return '#f44336';
    case 'tutor':
      return '#2196f3';
    case 'student':
      return '#4caf50';
    default:
      return '#9e9e9e';
  }
};

// Helper function to get initials from full name
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

// Helper function to calculate profile completion percentage
const calculateProfileCompletion = (user: any): number => {
  if (!user) return 0;

  const requiredFields = [
    'full_name',
    'email',
    'gender',
    'phone_number',
    'department',
    'level',
    'date_of_birth',
    'state_of_origin',
    'profile_picture_url'
  ];

  const completedFields = requiredFields.filter(field => !!user[field]);
  return Math.round((completedFields.length / requiredFields.length) * 100);
};

// Validation schema for profile update
const profileValidationSchema = Yup.object({
  full_name: Yup.string()
    .required('Full name is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  other_name: Yup.string()
    .optional()
    .nullable(),
  gender: Yup.string()
    .oneOf(['male', 'female', 'other'], 'Please select a valid gender')
    .required('Gender is required'),
  phone_number: Yup.string()
    .matches(/^[0-9+\-\s]+$/, 'Invalid phone number format')
    .min(10, 'Phone number must be at least 10 digits')
    .required('Phone number is required'),
  department: Yup.string()
    .required('Department is required'),
  level: Yup.string()
    .oneOf(['100', '200', '300', '400', '500', '600'], 'Please select a valid level')
    .required('Level is required'),
  date_of_birth: Yup.date()
    .max(new Date(), 'Date of birth cannot be in the future')
    .required('Date of birth is required'),
  state_of_origin: Yup.string()
    .required('State of origin is required'),
  institution_id: Yup.number()
    .nullable()
    .optional(),
});

// Validation schema for password change
const passwordValidationSchema = Yup.object({
  current_password: Yup.string()
    .required('Current password is required'),
  new_password: Yup.string()
    .required('New password is required')
    .min(8, 'Password must be at least 8 characters'),
  confirm_password: Yup.string()
    .required('Confirm password is required')
    .oneOf([Yup.ref('new_password')], 'Passwords must match'),
});

// Interface for TabPanel props
interface TabPanelProps {
  children?: React.ReactNode;
  value: number;
  index: number;
}

// TabPanel component
const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  // Use a state to force re-render when the tab becomes active
  const [rendered, setRendered] = useState(false);

  useEffect(() => {
    if (value === index && !rendered) {
      setRendered(true);
    }
  }, [value, index, rendered]);

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
    >
      {(value === index || rendered) && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const Profile: React.FC = () => {
  const { user: authUser } = useAuth();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Cast user to ExtendedUser to handle additional properties
  const user = authUser as unknown as ExtendedUser;

  // State for edit mode
  const [editMode, setEditMode] = useState(false);

  // State for password change
  const [passwordChangeMode, setPasswordChangeMode] = useState(false);

  // State for success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // State for profile picture upload
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadingPicture, setUploadingPicture] = useState(false);

  // State for tabs
  const [activeTab, setActiveTab] = useState(0);

  // State for account settings
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [accountDeleteConfirm, setAccountDeleteConfirm] = useState(false);

  // State for activity pagination
  const [examPage, setExamPage] = useState(1);
  const [practiceAttemptPage, setPracticeAttemptPage] = useState(1);
  const [bookmarkPage, setBookmarkPage] = useState(1);
  const itemsPerPage = 5;

  // Theme for responsive design
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Fetch user's exams
  const {
    data: exams = [],
    isLoading: isLoadingExams
  } = useQuery({
    queryKey: ['exams', user?.id],
    queryFn: () => user ? getExams(user.id) : Promise.resolve([]),
    enabled: !!user
  });

  // Fetch user's practice attempts
  const {
    data: attempts = [],
    isLoading: isLoadingAttempts
  } = useQuery({
    queryKey: ['attempts', user?.id],
    queryFn: () => user ? getQuestionAttempts(user.id) : Promise.resolve([]),
    enabled: !!user
  });

  // Fetch user's bookmarks
  const {
    data: bookmarks = [],
    isLoading: isLoadingBookmarks
  } = useQuery({
    queryKey: ['bookmarks', user?.id],
    queryFn: () => user ? getBookmarks(user.id) : Promise.resolve([]),
    enabled: !!user
  });

  // Fetch courses
  const {
    data: courses = []
  } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
    enabled: !!user
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: (data: UserUpdate) => updateCurrentUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users/me'] });
      setEditMode(false);
      setSuccessMessage('Profile updated successfully');
    }
  });

  // Change password mutation
  const changePasswordMutation = useMutation({
    mutationFn: (data: { password: string }) => updateCurrentUser(data),
    onSuccess: () => {
      passwordFormik.resetForm();
      setPasswordChangeMode(false);
      setSuccessMessage('Password changed successfully');
    }
  });

  // Profile picture upload mutation
  const uploadPictureMutation = useMutation({
    mutationFn: (file: File) => uploadProfilePicture(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users/me'] });
      setUploadingPicture(false);
      setSelectedFile(null);
      setPreviewUrl(null);
      setSuccessMessage('Profile picture updated successfully');
    },
    onError: (error) => {
      console.error('Error uploading profile picture:', error);
      setUploadingPicture(false);
    }
  });

  // Profile update form
  const profileFormik = useFormik({
    initialValues: {
      full_name: user?.full_name || '',
      email: user?.email || '',
      other_name: user?.other_name || '',
      gender: user?.gender || '',
      phone_number: user?.phone_number || '',
      department: user?.department || '',
      level: user?.level || '',
      date_of_birth: user?.date_of_birth ? new Date(user.date_of_birth).toISOString().split('T')[0] : '',
      state_of_origin: user?.state_of_origin || '',
      institution_id: user?.institution_id || null,
    },
    validationSchema: profileValidationSchema,
    onSubmit: (values) => {
      updateProfileMutation.mutate(values);
    },
    enableReinitialize: true
  });

  // Password change form
  const passwordFormik = useFormik({
    initialValues: {
      current_password: '',
      new_password: '',
      confirm_password: '',
    },
    validationSchema: passwordValidationSchema,
    onSubmit: (values) => {
      // In a real app, you would verify the current password on the server
      changePasswordMutation.mutate({ password: values.new_password });
    }
  });

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedFile(file);

      // Create a preview URL
      const fileReader = new FileReader();
      fileReader.onload = () => {
        setPreviewUrl(fileReader.result as string);
      };
      fileReader.readAsDataURL(file);
    }
  };

  // Handle file upload
  const handleUploadPicture = () => {
    if (selectedFile) {
      setUploadingPicture(true);
      uploadPictureMutation.mutate(selectedFile);
    }
  };

  // Cancel file upload
  const handleCancelUpload = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
  };

  if (!user) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Calculate profile completion
  const profileCompletionPercentage = calculateProfileCompletion(user);

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    console.log('Changing tab from', activeTab, 'to', newValue);

    // Force a re-render by setting the state
    setActiveTab(newValue);

    // Log the current tab content
    console.log('Tab content for tab', newValue, 'is being rendered');

    // Force a re-render of the tab content after a short delay
    setTimeout(() => {
      console.log('Current tab after timeout:', newValue);

      // Trigger a re-render by setting the same state again
      setActiveTab(prev => {
        console.log('Re-setting active tab to', prev);
        return prev;
      });
    }, 100);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{
        position: 'relative',
        mb: 6,
        pt: 2,
        pb: 4,
        borderRadius: 2,
        background: `linear-gradient(135deg, ${theme.palette.primary.light}, ${theme.palette.primary.main})`,
        color: 'white',
        boxShadow: 3
      }}>
        <Typography variant="h4" component="h1" sx={{ mb: 1, textAlign: 'center', fontWeight: 'bold' }}>
          My Profile
        </Typography>
        <Typography variant="subtitle1" sx={{ mb: 3, textAlign: 'center', opacity: 0.9 }}>
          Manage your personal information and account settings
        </Typography>

        <Box sx={{
          position: 'absolute',
          bottom: -30,
          left: '50%',
          transform: 'translateX(-50%)',
          bgcolor: 'background.paper',
          borderRadius: 2,
          boxShadow: 2,
          px: 2,
          py: 1
        }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant={isMobile ? "scrollable" : "standard"}
            scrollButtons={isMobile ? "auto" : undefined}
            centered={!isMobile}
            sx={{
              '& .MuiTab-root': {
                minWidth: isMobile ? 'auto' : 120,
                fontWeight: 'medium'
              }
            }}
          >
            <Tab icon={<AccountIcon />} label="Profile" />
            <Tab icon={<SettingsIcon />} label="Account" />
            <Tab icon={<DashboardIcon />} label="Activity" />
          </Tabs>
        </Box>
      </Box>

      <Grid container spacing={4}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
            <Box sx={{ textAlign: 'center' }}>
              <Box sx={{ position: 'relative', width: 150, height: 150, mx: 'auto', mb: 3 }}>
                <Badge
                  overlap="circular"
                  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                  badgeContent={
                    <Box
                      sx={{
                        backgroundColor: 'primary.main',
                        borderRadius: '50%',
                        width: 40,
                        height: 40,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer',
                        border: '2px solid white',
                        boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
                        transition: 'all 0.2s ease-in-out',
                        '&:hover': {
                          backgroundColor: 'primary.dark',
                          transform: 'scale(1.1)',
                        },
                      }}
                      component="label"
                    >
                      <input
                        type="file"
                        accept="image/*"
                        hidden
                        onChange={handleFileChange}
                        disabled={uploadingPicture}
                      />
                      <PhotoCameraIcon sx={{ color: 'white', fontSize: 22 }} />
                    </Box>
                  }
                >
                  <Avatar
                    src={previewUrl || user.profile_picture_url}
                    sx={{
                      width: 150,
                      height: 150,
                      bgcolor: getAvatarColor(user.role),
                      fontSize: '3.5rem',
                      boxShadow: 3,
                      border: '4px solid white',
                      transition: 'all 0.3s ease'
                    }}
                  >
                    {getInitials(user.full_name)}
                  </Avatar>
                </Badge>

                {uploadingPicture && (
                  <Box sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'rgba(255,255,255,0.7)',
                    borderRadius: '50%'
                  }}>
                    <CircularProgress size={60} />
                  </Box>
                )}
              </Box>

              {/* Preview and upload controls */}
              {selectedFile && (
                <Box sx={{ mt: 2, mb: 3 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Selected: {selectedFile.name}
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mt: 1 }}>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<CancelIcon />}
                      onClick={handleCancelUpload}
                      disabled={uploadingPicture}
                      sx={{ borderRadius: '20px' }}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="small"
                      variant="contained"
                      color="primary"
                      startIcon={uploadingPicture ? <CircularProgress size={16} /> : <CloudUploadIcon />}
                      onClick={handleUploadPicture}
                      disabled={uploadingPicture}
                      sx={{ borderRadius: '20px' }}
                    >
                      Upload
                    </Button>
                  </Box>
                </Box>
              )}

              {!selectedFile && !user.profile_picture_url && (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 1, mb: 2 }}>
                  Click the camera icon to upload a profile picture
                </Typography>
              )}

              <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                {user.full_name}
              </Typography>

              <Typography variant="body1" color="text.secondary" gutterBottom>
                {user.email}
              </Typography>

              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1, mb: 3 }}>
                <Chip
                  icon={<VerifiedIcon />}
                  label={user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                  color={getRoleColor(user.role)}
                  sx={{ fontWeight: 'medium' }}
                />
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Profile completion progress */}
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    Profile Completion
                  </Typography>
                  <Typography variant="body2" fontWeight="medium" color={
                    profileCompletionPercentage < 50 ? 'error.main' :
                    profileCompletionPercentage < 80 ? 'warning.main' :
                    'success.main'
                  }>
                    {profileCompletionPercentage}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={profileCompletionPercentage}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    bgcolor: 'grey.200',
                    '& .MuiLinearProgress-bar': {
                      bgcolor: profileCompletionPercentage < 50 ? 'error.main' :
                              profileCompletionPercentage < 80 ? 'warning.main' :
                              'success.main',
                      borderRadius: 4
                    }
                  }}
                />

                {profileCompletionPercentage < 100 && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontSize: '0.8rem' }}>
                    Complete your profile to get the most out of CampusPQ
                  </Typography>
                )}
              </Box>
            </Box>
          </Paper>

          <Paper sx={{ p: 3, borderRadius: 2, boxShadow: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" fontWeight="bold">
                Account Information
              </Typography>
              <IconButton
                color="primary"
                onClick={() => setEditMode(true)}
                disabled={editMode}
                size="small"
                sx={{ bgcolor: 'primary.light', color: 'white', '&:hover': { bgcolor: 'primary.main' } }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Box>

            <List sx={{ '& .MuiListItem-root': { px: 1, py: 1.5 } }}>
              <ListItem>
                <ListItemIcon>
                  <EmailIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary={<Typography variant="body2" color="text.secondary">Email</Typography>}
                  secondary={<Typography variant="body1">{user.email}</Typography>}
                />
              </ListItem>

              <Divider component="li" variant="inset" />

              {user.role === 'student' && user.institution && (
                <>
                  <ListItem>
                    <ListItemIcon>
                      <SchoolIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography variant="body2" color="text.secondary">Institution</Typography>}
                      secondary={
                        <Button
                          component={RouterLink}
                          to={`/schools/${user.institution.id}`}
                          size="small"
                          sx={{ p: 0, minWidth: 0, textTransform: 'none' }}
                        >
                          {user.institution.name}
                        </Button>
                      }
                    />
                  </ListItem>
                  <Divider component="li" variant="inset" />
                </>
              )}

              {user.department && (
                <>
                  <ListItem>
                    <ListItemIcon>
                      <ClassIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography variant="body2" color="text.secondary">Department</Typography>}
                      secondary={<Typography variant="body1">{user.department}</Typography>}
                    />
                  </ListItem>
                  <Divider component="li" variant="inset" />
                </>
              )}

              {user.level && (
                <>
                  <ListItem>
                    <ListItemIcon>
                      <SchoolIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography variant="body2" color="text.secondary">Level</Typography>}
                      secondary={<Typography variant="body1">{user.level} Level</Typography>}
                    />
                  </ListItem>
                  <Divider component="li" variant="inset" />
                </>
              )}

              {user.phone_number && (
                <>
                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography variant="body2" color="text.secondary">Phone</Typography>}
                      secondary={<Typography variant="body1">{user.phone_number}</Typography>}
                    />
                  </ListItem>
                  <Divider component="li" variant="inset" />
                </>
              )}

              {user.gender && (
                <>
                  <ListItem>
                    <ListItemIcon>
                      <GenderIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography variant="body2" color="text.secondary">Gender</Typography>}
                      secondary={<Typography variant="body1" sx={{ textTransform: 'capitalize' }}>{user.gender}</Typography>}
                    />
                  </ListItem>
                  <Divider component="li" variant="inset" />
                </>
              )}

              {user.date_of_birth && (
                <>
                  <ListItem>
                    <ListItemIcon>
                      <BirthdayIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography variant="body2" color="text.secondary">Date of Birth</Typography>}
                      secondary={<Typography variant="body1">{formatDate(user.date_of_birth)}</Typography>}
                    />
                  </ListItem>
                  <Divider component="li" variant="inset" />
                </>
              )}

              {user.state_of_origin && (
                <>
                  <ListItem>
                    <ListItemIcon>
                      <LocationIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={<Typography variant="body2" color="text.secondary">State of Origin</Typography>}
                      secondary={<Typography variant="body1">{user.state_of_origin}</Typography>}
                    />
                  </ListItem>
                  <Divider component="li" variant="inset" />
                </>
              )}

              <ListItem>
                <ListItemIcon>
                  <CalendarIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary={<Typography variant="body2" color="text.secondary">Joined</Typography>}
                  secondary={<Typography variant="body1">{formatDate(user.created_at)}</Typography>}
                />
              </ListItem>
            </List>

            <Box sx={{ mt: 3 }}>
              <Button
                fullWidth
                variant="outlined"
                color="primary"
                startIcon={<LockIcon />}
                onClick={() => setPasswordChangeMode(true)}
                disabled={passwordChangeMode}
                sx={{ mb: 2 }}
              >
                Change Password
              </Button>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={8}>
          {/* Tab Panels */}
          <TabPanel value={activeTab} index={0}>
            {editMode ? (
              <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Edit Profile
                </Typography>

                {updateProfileMutation.error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {(updateProfileMutation.error as Error).message || 'An error occurred. Please try again.'}
                  </Alert>
                )}

                <form onSubmit={profileFormik.handleSubmit}>
                  <Grid container spacing={3}>
                    {/* Personal Information */}
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" fontWeight="medium" color="primary" gutterBottom>
                        Personal Information
                      </Typography>
                    </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      id="full_name"
                      name="full_name"
                      label="Full Name"
                      variant="outlined"
                      value={profileFormik.values.full_name}
                      onChange={profileFormik.handleChange}
                      onBlur={profileFormik.handleBlur}
                      error={profileFormik.touched.full_name && Boolean(profileFormik.errors.full_name)}
                      helperText={profileFormik.touched.full_name && profileFormik.errors.full_name}
                      disabled={updateProfileMutation.isPending}
                      required
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      id="other_name"
                      name="other_name"
                      label="Other Name"
                      variant="outlined"
                      value={profileFormik.values.other_name || ''}
                      onChange={profileFormik.handleChange}
                      onBlur={profileFormik.handleBlur}
                      error={profileFormik.touched.other_name && Boolean(profileFormik.errors.other_name)}
                      helperText={(profileFormik.touched.other_name && profileFormik.errors.other_name) || "Optional"}
                      disabled={updateProfileMutation.isPending}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      id="email"
                      name="email"
                      label="Email"
                      variant="outlined"
                      value={profileFormik.values.email}
                      onChange={profileFormik.handleChange}
                      onBlur={profileFormik.handleBlur}
                      error={profileFormik.touched.email && Boolean(profileFormik.errors.email)}
                      helperText={profileFormik.touched.email && profileFormik.errors.email}
                      disabled={updateProfileMutation.isPending}
                      required
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl
                      fullWidth
                      error={profileFormik.touched.gender && Boolean(profileFormik.errors.gender)}
                    >
                      <InputLabel id="gender-label" shrink={true}>Gender</InputLabel>
                      <Select
                        labelId="gender-label"
                        id="gender"
                        name="gender"
                        value={profileFormik.values.gender}
                        label="Gender"
                        onChange={profileFormik.handleChange}
                        onBlur={profileFormik.handleBlur}
                        disabled={updateProfileMutation.isPending}
                        displayEmpty
                      >
                        <MenuItem value="">
                          <em>Select gender</em>
                        </MenuItem>
                        <MenuItem value="male">Male</MenuItem>
                        <MenuItem value="female">Female</MenuItem>
                        <MenuItem value="other">Other</MenuItem>
                      </Select>
                      {profileFormik.touched.gender && profileFormik.errors.gender && (
                        <FormHelperText>{profileFormik.errors.gender as string}</FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      id="phone_number"
                      name="phone_number"
                      label="Phone Number"
                      variant="outlined"
                      value={profileFormik.values.phone_number}
                      onChange={profileFormik.handleChange}
                      onBlur={profileFormik.handleBlur}
                      error={profileFormik.touched.phone_number && Boolean(profileFormik.errors.phone_number)}
                      helperText={profileFormik.touched.phone_number && profileFormik.errors.phone_number}
                      disabled={updateProfileMutation.isPending}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      id="date_of_birth"
                      name="date_of_birth"
                      label="Date of Birth"
                      type="date"
                      variant="outlined"
                      value={profileFormik.values.date_of_birth}
                      onChange={profileFormik.handleChange}
                      onBlur={profileFormik.handleBlur}
                      error={profileFormik.touched.date_of_birth && Boolean(profileFormik.errors.date_of_birth)}
                      helperText={profileFormik.touched.date_of_birth && profileFormik.errors.date_of_birth}
                      disabled={updateProfileMutation.isPending}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl
                      fullWidth
                      error={profileFormik.touched.state_of_origin && Boolean(profileFormik.errors.state_of_origin)}
                    >
                      <InputLabel id="state-label" shrink={true}>State of Origin</InputLabel>
                      <Select
                        labelId="state-label"
                        id="state_of_origin"
                        name="state_of_origin"
                        value={profileFormik.values.state_of_origin}
                        label="State of Origin"
                        onChange={profileFormik.handleChange}
                        onBlur={profileFormik.handleBlur}
                        disabled={updateProfileMutation.isPending}
                        displayEmpty
                      >
                        <MenuItem value="">
                          <em>Select state</em>
                        </MenuItem>
                        {nigerianStates.map((state) => (
                          <MenuItem key={state} value={state}>
                            {state}
                          </MenuItem>
                        ))}
                      </Select>
                      {profileFormik.touched.state_of_origin && profileFormik.errors.state_of_origin && (
                        <FormHelperText>{profileFormik.errors.state_of_origin as string}</FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  {/* Education Information */}
                  <Grid item xs={12} sx={{ mt: 2 }}>
                    <Typography variant="subtitle1" fontWeight="medium" color="primary" gutterBottom>
                      Education Information
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl
                      fullWidth
                      error={profileFormik.touched.institution_id && Boolean(profileFormik.errors.institution_id)}
                    >
                      <InputLabel id="institution-label" shrink={true}>Institution</InputLabel>
                      <Select
                        labelId="institution-label"
                        id="institution_id"
                        name="institution_id"
                        value={profileFormik.values.institution_id || ''}
                        label="Institution"
                        onChange={(e) => {
                          profileFormik.setFieldValue('institution_id', e.target.value);
                        }}
                        onBlur={profileFormik.handleBlur}
                        disabled={updateProfileMutation.isPending}
                        displayEmpty
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: 300,
                            },
                          },
                        }}
                      >
                        <MenuItem value="">
                          <em>Select institution</em>
                        </MenuItem>
                        {nigerianUniversities.map((uni) => (
                          <MenuItem key={uni.id} value={uni.id}>
                            {uni.name}
                          </MenuItem>
                        ))}
                      </Select>
                      {profileFormik.touched.institution_id && profileFormik.errors.institution_id && (
                        <FormHelperText>{profileFormik.errors.institution_id as string}</FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl
                      fullWidth
                      error={profileFormik.touched.department && Boolean(profileFormik.errors.department)}
                    >
                      <InputLabel id="department-label" shrink={true}>Department</InputLabel>
                      <Select
                        labelId="department-label"
                        id="department"
                        name="department"
                        value={profileFormik.values.department}
                        label="Department"
                        onChange={profileFormik.handleChange}
                        onBlur={profileFormik.handleBlur}
                        disabled={updateProfileMutation.isPending}
                        displayEmpty
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: 300,
                            },
                          },
                        }}
                      >
                        <MenuItem value="">
                          <em>Select department</em>
                        </MenuItem>
                        {commonDepartments.map((dept) => (
                          <MenuItem key={dept} value={dept}>
                            {dept}
                          </MenuItem>
                        ))}
                      </Select>
                      {profileFormik.touched.department && profileFormik.errors.department && (
                        <FormHelperText>{profileFormik.errors.department as string}</FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <FormControl
                      fullWidth
                      error={profileFormik.touched.level && Boolean(profileFormik.errors.level)}
                    >
                      <InputLabel id="level-label" shrink={true}>Level</InputLabel>
                      <Select
                        labelId="level-label"
                        id="level"
                        name="level"
                        value={profileFormik.values.level}
                        label="Level"
                        onChange={profileFormik.handleChange}
                        onBlur={profileFormik.handleBlur}
                        disabled={updateProfileMutation.isPending}
                        displayEmpty
                      >
                        <MenuItem value="">
                          <em>Select level</em>
                        </MenuItem>
                        {educationLevels.map((level) => (
                          <MenuItem key={level} value={level}>
                            {level}
                          </MenuItem>
                        ))}
                      </Select>
                      {profileFormik.touched.level && profileFormik.errors.level && (
                        <FormHelperText>{profileFormik.errors.level as string}</FormHelperText>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<CancelIcon />}
                      onClick={() => setEditMode(false)}
                      disabled={updateProfileMutation.isPending}
                    >
                      Cancel
                    </Button>

                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      startIcon={updateProfileMutation.isPending ? <CircularProgress size={24} /> : <SaveIcon />}
                      disabled={updateProfileMutation.isPending}
                    >
                      Save Changes
                    </Button>
                  </Grid>
                </Grid>
              </form>
            </Paper>
          ) : passwordChangeMode ? (
            <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
              <Typography variant="h6" gutterBottom>
                Change Password
              </Typography>

              {changePasswordMutation.error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {(changePasswordMutation.error as Error).message || 'An error occurred. Please try again.'}
                </Alert>
              )}

              <form onSubmit={passwordFormik.handleSubmit}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      id="current_password"
                      name="current_password"
                      label="Current Password"
                      type="password"
                      variant="outlined"
                      value={passwordFormik.values.current_password}
                      onChange={passwordFormik.handleChange}
                      onBlur={passwordFormik.handleBlur}
                      error={passwordFormik.touched.current_password && Boolean(passwordFormik.errors.current_password)}
                      helperText={passwordFormik.touched.current_password && passwordFormik.errors.current_password}
                      disabled={changePasswordMutation.isPending}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      id="new_password"
                      name="new_password"
                      label="New Password"
                      type="password"
                      variant="outlined"
                      value={passwordFormik.values.new_password}
                      onChange={passwordFormik.handleChange}
                      onBlur={passwordFormik.handleBlur}
                      error={passwordFormik.touched.new_password && Boolean(passwordFormik.errors.new_password)}
                      helperText={passwordFormik.touched.new_password && passwordFormik.errors.new_password}
                      disabled={changePasswordMutation.isPending}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      id="confirm_password"
                      name="confirm_password"
                      label="Confirm New Password"
                      type="password"
                      variant="outlined"
                      value={passwordFormik.values.confirm_password}
                      onChange={passwordFormik.handleChange}
                      onBlur={passwordFormik.handleBlur}
                      error={passwordFormik.touched.confirm_password && Boolean(passwordFormik.errors.confirm_password)}
                      helperText={passwordFormik.touched.confirm_password && passwordFormik.errors.confirm_password}
                      disabled={changePasswordMutation.isPending}
                    />
                  </Grid>

                  <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<CancelIcon />}
                      onClick={() => setPasswordChangeMode(false)}
                      disabled={changePasswordMutation.isPending}
                    >
                      Cancel
                    </Button>

                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      startIcon={changePasswordMutation.isPending ? <CircularProgress size={24} /> : <SaveIcon />}
                      disabled={changePasswordMutation.isPending}
                    >
                      Change Password
                    </Button>
                  </Grid>
                </Grid>
              </form>
            </Paper>
          ) : (
            <>
              <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" fontWeight="bold">
                    Profile Information
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<EditIcon />}
                    onClick={() => setEditMode(true)}
                    size="small"
                  >
                    Edit Profile
                  </Button>
                </Box>

                <Box sx={{ mb: 4 }}>
                  <Typography variant="subtitle1" fontWeight="medium" color="primary" gutterBottom>
                    Personal Information
                  </Typography>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Full Name
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {user.full_name}
                      </Typography>
                    </Grid>

                    {user.other_name && (
                      <Grid item xs={12} sm={6} md={4}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Other Name
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {user.other_name}
                        </Typography>
                      </Grid>
                    )}

                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Email
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {user.email}
                      </Typography>
                    </Grid>

                    {user.gender && (
                      <Grid item xs={12} sm={6} md={4}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Gender
                        </Typography>
                        <Typography variant="body1" fontWeight="medium" sx={{ textTransform: 'capitalize' }}>
                          {user.gender}
                        </Typography>
                      </Grid>
                    )}

                    {user.phone_number && (
                      <Grid item xs={12} sm={6} md={4}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Phone Number
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {user.phone_number}
                        </Typography>
                      </Grid>
                    )}

                    {user.date_of_birth && (
                      <Grid item xs={12} sm={6} md={4}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Date of Birth
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {formatDate(user.date_of_birth)}
                        </Typography>
                      </Grid>
                    )}

                    {user.state_of_origin && (
                      <Grid item xs={12} sm={6} md={4}>
                        <Typography variant="subtitle2" color="text.secondary">
                          State of Origin
                        </Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {user.state_of_origin}
                        </Typography>
                      </Grid>
                    )}

                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Role
                      </Typography>
                      <Chip
                        label={user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        color={getRoleColor(user.role)}
                        size="small"
                        sx={{ fontWeight: 'medium' }}
                      />
                    </Grid>

                    <Grid item xs={12} sm={6} md={4}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Joined
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {formatDate(user.created_at)}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>

                {user.role === 'student' && (
                  <Box>
                    <Typography variant="subtitle1" fontWeight="medium" color="primary" gutterBottom>
                      Education Information
                    </Typography>
                    <Grid container spacing={3}>
                      {user.institution && (
                        <Grid item xs={12} sm={6} md={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Institution
                          </Typography>
                          <Typography variant="body1" fontWeight="medium">
                            {user.institution.name}
                          </Typography>
                        </Grid>
                      )}

                      {user.department && (
                        <Grid item xs={12} sm={6} md={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Department
                          </Typography>
                          <Typography variant="body1" fontWeight="medium">
                            {user.department}
                          </Typography>
                        </Grid>
                      )}

                      {user.level && (
                        <Grid item xs={12} sm={6} md={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Level
                          </Typography>
                          <Typography variant="body1" fontWeight="medium">
                            {user.level}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </Box>
                )}
              </Paper>

              {user.role === 'student' && (
                <Paper sx={{ p: 3, mb: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    Enrolled Courses
                  </Typography>

                  {user.courses && user.courses.length > 0 ? (
                    <List>
                      {user.courses.map((course) => (
                        <ListItem
                          key={course.id}
                          component={RouterLink}
                          to={`/courses/${course.id}`}
                        >
                          <ListItemIcon>
                            <BookIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={course.name}
                            secondary={course.code}
                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Box sx={{ textAlign: 'center', py: 2 }}>
                      <Typography variant="body1" color="text.secondary" paragraph>
                        You are not enrolled in any courses yet.
                      </Typography>
                      <Button
                        component={RouterLink}
                        to="/courses"
                        variant="contained"
                      >
                        Browse Courses
                      </Button>
                    </Box>
                  )}
                </Paper>
              )}

              {user.role === 'tutor' && (
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Paper sx={{ p: 3, mb: { xs: 3, md: 0 } }}>
                      <Typography variant="h6" gutterBottom>
                        My Sessions
                      </Typography>

                      {user.sessions && user.sessions.length > 0 ? (
                        <List>
                          {user.sessions.slice(0, 5).map((session) => (
                            <ListItem
                              key={session.id}
                              component={RouterLink}
                              to={`/sessions/${session.id}`}
                            >
                              <ListItemIcon>
                                <EventIcon />
                              </ListItemIcon>
                              <ListItemText
                                primary={session.title}
                                secondary={new Date(session.start_time).toLocaleString()}
                              />
                            </ListItem>
                          ))}

                          {user.sessions.length > 5 && (
                            <Box sx={{ textAlign: 'center', mt: 1 }}>
                              <Button
                                component={RouterLink}
                                to="/sessions"
                              >
                                View All Sessions
                              </Button>
                            </Box>
                          )}
                        </List>
                      ) : (
                        <Box sx={{ textAlign: 'center', py: 2 }}>
                          <Typography variant="body1" color="text.secondary" paragraph>
                            You haven't created any sessions yet.
                          </Typography>
                          <Button
                            component={RouterLink}
                            to="/sessions/new"
                            variant="contained"
                          >
                            Create Session
                          </Button>
                        </Box>
                      )}
                    </Paper>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Paper sx={{ p: 3 }}>
                      <Typography variant="h6" gutterBottom>
                        My Questions
                      </Typography>

                      {user.questions && user.questions.length > 0 ? (
                        <List>
                          {user.questions.slice(0, 5).map((question) => (
                            <ListItem
                              key={question.id}
                              component={RouterLink}
                              to={`/questions/${question.id}`}
                            >
                              <ListItemIcon>
                                <QuestionIcon />
                              </ListItemIcon>
                              <ListItemText
                                primary={question.content.substring(0, 50) + (question.content.length > 50 ? '...' : '')}
                                secondary={`Created: ${new Date(question.created_at).toLocaleDateString()}`}
                              />
                            </ListItem>
                          ))}

                          {user.questions.length > 5 && (
                            <Box sx={{ textAlign: 'center', mt: 1 }}>
                              <Button
                                component={RouterLink}
                                to="/questions"
                              >
                                View All Questions
                              </Button>
                            </Box>
                          )}
                        </List>
                      ) : (
                        <Box sx={{ textAlign: 'center', py: 2 }}>
                          <Typography variant="body1" color="text.secondary" paragraph>
                            You haven't created any questions yet.
                          </Typography>
                          <Button
                            component={RouterLink}
                            to="/questions/new"
                            variant="contained"
                          >
                            Create Question
                          </Button>
                        </Box>
                      )}
                    </Paper>
                  </Grid>
                </Grid>
              )}
            </>
          )}
          </TabPanel>

          {/* Account Tab */}
          <TabPanel value={activeTab} index={1}>
            <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Account Settings
              </Typography>

              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" fontWeight="medium" color="primary" gutterBottom>
                  Security
                </Typography>

                <Button
                  fullWidth
                  variant="outlined"
                  color="primary"
                  startIcon={<LockIcon />}
                  onClick={() => setPasswordChangeMode(true)}
                  sx={{ mb: 2 }}
                >
                  Change Password
                </Button>

                {passwordChangeMode && (
                  <Box sx={{ mt: 3, mb: 3 }}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      Change Password
                    </Typography>

                    {changePasswordMutation.error && (
                      <Alert severity="error" sx={{ mb: 3 }}>
                        {(changePasswordMutation.error as Error).message || 'An error occurred. Please try again.'}
                      </Alert>
                    )}

                    <form onSubmit={passwordFormik.handleSubmit}>
                      <Grid container spacing={3}>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            id="current_password"
                            name="current_password"
                            label="Current Password"
                            type="password"
                            variant="outlined"
                            value={passwordFormik.values.current_password}
                            onChange={passwordFormik.handleChange}
                            onBlur={passwordFormik.handleBlur}
                            error={passwordFormik.touched.current_password && Boolean(passwordFormik.errors.current_password)}
                            helperText={passwordFormik.touched.current_password && passwordFormik.errors.current_password}
                            disabled={changePasswordMutation.isPending}
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            id="new_password"
                            name="new_password"
                            label="New Password"
                            type="password"
                            variant="outlined"
                            value={passwordFormik.values.new_password}
                            onChange={passwordFormik.handleChange}
                            onBlur={passwordFormik.handleBlur}
                            error={passwordFormik.touched.new_password && Boolean(passwordFormik.errors.new_password)}
                            helperText={passwordFormik.touched.new_password && passwordFormik.errors.new_password}
                            disabled={changePasswordMutation.isPending}
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            id="confirm_password"
                            name="confirm_password"
                            label="Confirm New Password"
                            type="password"
                            variant="outlined"
                            value={passwordFormik.values.confirm_password}
                            onChange={passwordFormik.handleChange}
                            onBlur={passwordFormik.handleBlur}
                            error={passwordFormik.touched.confirm_password && Boolean(passwordFormik.errors.confirm_password)}
                            helperText={passwordFormik.touched.confirm_password && passwordFormik.errors.confirm_password}
                            disabled={changePasswordMutation.isPending}
                          />
                        </Grid>

                        <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                          <Button
                            variant="outlined"
                            startIcon={<CancelIcon />}
                            onClick={() => setPasswordChangeMode(false)}
                            disabled={changePasswordMutation.isPending}
                          >
                            Cancel
                          </Button>

                          <Button
                            type="submit"
                            variant="contained"
                            color="primary"
                            startIcon={changePasswordMutation.isPending ? <CircularProgress size={24} /> : <SaveIcon />}
                            disabled={changePasswordMutation.isPending}
                          >
                            Change Password
                          </Button>
                        </Grid>
                      </Grid>
                    </form>
                  </Box>
                )}
              </Box>

              <Divider sx={{ my: 3 }} />

              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" fontWeight="medium" color="primary" gutterBottom>
                  Email Preferences
                </Typography>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="body1">
                    Receive email notifications
                  </Typography>
                  <Switch
                    checked={emailNotifications}
                    onChange={(e) => setEmailNotifications(e.target.checked)}
                    color="primary"
                  />
                </Box>
              </Box>

              <Divider sx={{ my: 3 }} />

              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" fontWeight="medium" color="error" gutterBottom>
                  Danger Zone
                </Typography>

                <Button
                  fullWidth
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={() => setAccountDeleteConfirm(true)}
                  sx={{ mt: 1 }}
                >
                  Delete Account
                </Button>
              </Box>
            </Paper>
          </TabPanel>

          {/* Activity Tab */}
          <TabPanel value={activeTab} index={2}>
            <Paper sx={{ p: 3, mb: 4, borderRadius: 2, boxShadow: 2 }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Activity History
              </Typography>

              {/* Exams Section */}
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" fontWeight="medium" color="primary" gutterBottom>
                  Recent Exams
                </Typography>

                {isLoadingExams ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress size={30} />
                  </Box>
                ) : exams.length > 0 ? (
                  <>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Course</TableCell>
                            <TableCell>Date</TableCell>
                            <TableCell>Score</TableCell>
                            <TableCell>Actions</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {exams.slice(0, 5).map((exam) => {
                            const course = courses.find(c => c.id === exam.course_id);
                            return (
                              <TableRow key={exam.id}>
                                <TableCell>{course ? course.name : `Course ${exam.course_id}`}</TableCell>
                                <TableCell>{new Date(exam.start_time).toLocaleDateString()}</TableCell>
                                <TableCell>
                                  {exam.score !== null ?
                                    `${exam.score}% (${exam.correct_answers}/${exam.total_questions})` :
                                    'In Progress'}
                                </TableCell>
                                <TableCell>
                                  <Button
                                    size="small"
                                    variant="outlined"
                                    onClick={() => navigate(`/mcq/exam-results/${exam.id}`)}
                                  >
                                    View Results
                                  </Button>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    {exams.length > 5 && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                        <Pagination
                          count={Math.ceil(exams.length / itemsPerPage)}
                          page={examPage}
                          onChange={(_e, page) => setExamPage(page)}
                          color="primary"
                          size="small"
                        />
                      </Box>
                    )}
                  </>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 2 }}>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      You haven't taken any exams yet.
                    </Typography>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => navigate('/mcq')}
                    >
                      Take an Exam
                    </Button>
                  </Box>
                )}
              </Box>

              <Divider sx={{ my: 3 }} />

              {/* Practice Attempts Section */}
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" fontWeight="medium" color="primary" gutterBottom>
                  Recent Practice Attempts
                </Typography>

                {isLoadingAttempts ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress size={30} />
                  </Box>
                ) : attempts.length > 0 ? (
                  <>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Question</TableCell>
                            <TableCell>Date</TableCell>
                            <TableCell>Result</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {attempts.slice(0, 5).map((attempt) => (
                            <TableRow key={attempt.id}>
                              <TableCell>Question #{attempt.question_id}</TableCell>
                              <TableCell>{new Date(attempt.attempt_time).toLocaleDateString()}</TableCell>
                              <TableCell>
                                <Chip
                                  label={attempt.is_correct ? "Correct" : "Incorrect"}
                                  color={attempt.is_correct ? "success" : "error"}
                                  size="small"
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    {attempts.length > 5 && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                        <Pagination
                          count={Math.ceil(attempts.length / itemsPerPage)}
                          page={practiceAttemptPage}
                          onChange={(_e, page) => setPracticeAttemptPage(page)}
                          color="primary"
                          size="small"
                        />
                      </Box>
                    )}
                  </>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 2 }}>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      You haven't practiced any questions yet.
                    </Typography>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => navigate('/mcq')}
                    >
                      Practice Questions
                    </Button>
                  </Box>
                )}
              </Box>

              <Divider sx={{ my: 3 }} />

              {/* Bookmarks Section */}
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" fontWeight="medium" color="primary" gutterBottom>
                  Bookmarked Questions
                </Typography>

                {isLoadingBookmarks ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress size={30} />
                  </Box>
                ) : bookmarks.length > 0 ? (
                  <>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Question</TableCell>
                            <TableCell>Date Bookmarked</TableCell>
                            <TableCell>Actions</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {bookmarks.slice(0, 5).map((bookmark) => (
                            <TableRow key={bookmark.id}>
                              <TableCell>Question #{bookmark.question_id}</TableCell>
                              <TableCell>{new Date(bookmark.created_at).toLocaleDateString()}</TableCell>
                              <TableCell>
                                <Button
                                  size="small"
                                  variant="outlined"
                                  onClick={() => navigate(`/questions/${bookmark.question_id}`)}
                                >
                                  View Question
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>

                    {bookmarks.length > 5 && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                        <Pagination
                          count={Math.ceil(bookmarks.length / itemsPerPage)}
                          page={bookmarkPage}
                          onChange={(_e, page) => setBookmarkPage(page)}
                          color="primary"
                          size="small"
                        />
                      </Box>
                    )}
                  </>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 2 }}>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      You haven't bookmarked any questions yet.
                    </Typography>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => navigate('/mcq')}
                    >
                      Browse Questions
                    </Button>
                  </Box>
                )}
              </Box>
            </Paper>
          </TabPanel>
        </Grid>
      </Grid>

      {/* Success message */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={3000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setSuccessMessage(null)}>
          {successMessage}
        </Alert>
      </Snackbar>

      {/* Error message for profile picture upload */}
      <Snackbar
        open={!!uploadPictureMutation.error}
        autoHideDuration={5000}
        onClose={() => uploadPictureMutation.reset()}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="error" onClose={() => uploadPictureMutation.reset()}>
          {(uploadPictureMutation.error as Error)?.message || 'Error uploading profile picture. Please try again.'}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Profile;
