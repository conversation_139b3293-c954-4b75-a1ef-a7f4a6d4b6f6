import React, { useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  Breadcrumbs,
  Link as MuiLink
} from '@mui/material';
import {
  School as SchoolIcon,
  NoteAlt as NoteIcon,
  ViewCarousel as CardIcon,
  NavigateNext as NavigateNextIcon
} from '@mui/icons-material';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

import FlashCardViewer from '../../components/FlashCards/FlashCardViewer';
import FlashCardGrid from '../../components/FlashCards/FlashCardGrid';
import FlashCardGenerationTool from '../../components/Tools/FlashCardGenerationTool';
import GeneratedFlashCardsTable from '../../components/Tools/GeneratedFlashCardsTable';
import PageHeader from '../../components/Layout/PageHeader';
import { FlashCard, getFlashCardGenerationStatus } from '../../api/studentTools';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`flash-card-tabpanel-${index}`}
      aria-labelledby={`flash-card-tab-${index}`}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const FlashCardPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { jobId: urlJobId, cardId } = useParams<{ jobId?: string; cardId?: string }>();
  const location = window.location;
  const searchParams = new URLSearchParams(location.search);
  const queryJobId = searchParams.get('jobId');
  const jobId = urlJobId === 'note-generated' ? queryJobId : urlJobId;
  const tabParam = searchParams.get('tab');

  // Set initial tab value based on URL parameters
  const [tabValue, setTabValue] = React.useState(() => {
    if (jobId || cardId) return 2;
    if (tabParam) return parseInt(tabParam);
    return 0;
  });

  const [jobTitle, setJobTitle] = React.useState<string | null>(null);

  console.log('FlashCardPage params:', { jobId, cardId, tabValue, tabParam });

  // Fetch job details if jobId is provided
  useEffect(() => {
    const fetchJobDetails = async () => {
      if (jobId) {
        try {
          const status = await getFlashCardGenerationStatus(parseInt(jobId));
          if (status) {
            setJobTitle(status.title || `Flash Cards #${jobId}`);
          }
        } catch (error) {
          console.error('Error fetching job details:', error);
        }
      }
    };

    fetchJobDetails();
  }, [jobId]);

  // Handle card click to navigate to study mode
  const handleCardClick = (card: FlashCard) => {
    console.log(`handleCardClick called with card ID: ${card.id}`);
    navigate(`/flash-cards/card/${card.id}`);
  };

  // Handle job click to navigate to job's flash cards
  const handleJobClick = (jobId: number) => {
    console.log(`handleJobClick called with jobId: ${jobId}`);
    navigate(`/flash-cards/study/note-generated?jobId=${jobId}`);
    setTabValue(2);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  const MotionPaper = motion(Paper);

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);

    // Update URL with tab parameter
    if (newValue === 0) {
      navigate('/flash-cards');
    } else if (newValue === 1) {
      navigate('/flash-cards?tab=1');
    } else if (newValue === 2 && !jobId && !cardId) {
      navigate('/flash-cards?tab=2');
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mb: 4 }}>
      <PageHeader
        title="Flash Cards"
        description="Create and study flash cards generated from your notes"
        icon={<CardIcon fontSize="large" />}
      />

      {/* Breadcrumbs navigation */}
      {(jobId || cardId) && (
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          aria-label="breadcrumb"
          sx={{ mb: 2, mt: -1 }}
        >
          <MuiLink
            component={Link}
            to="/flash-cards"
            color="inherit"
            underline="hover"
          >
            Flash Cards
          </MuiLink>
          {jobId && (
            <Typography color="text.primary">
              {jobTitle || `Job #${jobId}`}
            </Typography>
          )}
          {cardId && !jobId && (
            <Typography color="text.primary">
              Card #{cardId}
            </Typography>
          )}
        </Breadcrumbs>
      )}

      <MotionPaper
        variants={itemVariants}
        initial="hidden"
        animate="visible"
        elevation={0}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          mb: 3,
          border: `1px solid ${theme.palette.divider}`
        }}
      >
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant={isMobile ? "fullWidth" : "standard"}
          centered={!isMobile}
          sx={{
            borderBottom: `1px solid ${theme.palette.divider}`,
            backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)'
          }}
        >
          <Tab
            icon={<NoteIcon />}
            label="Generate"
            id="flash-card-tab-0"
            aria-controls="flash-card-tabpanel-0"
          />
          <Tab
            icon={<SchoolIcon />}
            label="My Flash Cards"
            id="flash-card-tab-1"
            aria-controls="flash-card-tabpanel-1"
          />
          <Tab
            icon={<CardIcon />}
            label="Study"
            id="flash-card-tab-2"
            aria-controls="flash-card-tabpanel-2"
          />
        </Tabs>

        <Box sx={{ p: { xs: 2, md: 3 } }}>
          <AnimatePresence mode="wait">
            <TabPanel value={tabValue} index={0}>
              <motion.div
                key="generate-tab"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
              >
                <Typography variant="h6" gutterBottom>
                  Generate Flash Cards from Your Notes
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Upload your notes and generate flash cards to help you study. The AI will create flash cards based on the content of your notes.
                </Typography>
                <FlashCardGenerationTool />
              </motion.div>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <motion.div
                key="my-cards-tab"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
              >
                <Typography variant="h6" gutterBottom>
                  Your Flash Card Sets
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  View and manage your generated flash card sets. Click on a set to study the cards.
                </Typography>
                <GeneratedFlashCardsTable onViewFlashCards={handleJobClick} />
              </motion.div>
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <motion.div
                key="study-tab"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
              >
                {jobId ? (
                  // If a job ID is provided, show the flash card viewer for that job
                  <FlashCardViewer jobId={parseInt(jobId)} />
                ) : cardId ? (
                  // If a card ID is provided, show the flash card viewer for that card
                  <FlashCardViewer cardId={parseInt(cardId)} />
                ) : (
                  // Otherwise, show all flash cards in a grid
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      All Flash Cards
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Browse all your flash cards or select a specific set from the "My Flash Cards" tab.
                    </Typography>
                    <FlashCardGrid onCardClick={handleCardClick} />
                  </Box>
                )}
              </motion.div>
            </TabPanel>
          </AnimatePresence>
        </Box>
      </MotionPaper>
    </Container>
  );
};

export default FlashCardPage;
