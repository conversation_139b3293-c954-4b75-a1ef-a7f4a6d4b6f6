import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Avatar,
  Rating,
  Chip,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
  Divider,
  TextField,
  InputAdornment,
} from '@mui/material';
import {
  Star,
  TrendingUp,
  Search,
  FilterList,
  ThumbUp,
  Comment,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

// Mock data for reviews
const mockReviewStats = {
  averageRating: 4.7,
  totalReviews: 28,
  ratingDistribution: {
    5: 18,
    4: 7,
    3: 2,
    2: 1,
    1: 0,
  },
  recentTrend: '+0.2',
};

const mockReviews = [
  {
    id: 1,
    student: {
      name: '<PERSON>',
      avatar: '',
    },
    rating: 5,
    date: '2024-01-15',
    course: 'Mathematics',
    comment: 'Excellent tutor! Very patient and explains concepts clearly. My grades have improved significantly since starting sessions.',
    helpful: 3,
    sessionCount: 12,
  },
  {
    id: 2,
    student: {
      name: '<PERSON>',
      avatar: '',
    },
    rating: 4,
    date: '2024-01-12',
    course: 'Physics',
    comment: 'Great teaching style and very knowledgeable. Sometimes sessions run a bit over time, but the content is always valuable.',
    helpful: 2,
    sessionCount: 8,
  },
  {
    id: 3,
    student: {
      name: '<PERSON> <PERSON>',
      avatar: '',
    },
    rating: 5,
    date: '2024-01-10',
    course: 'Chemistry',
    comment: 'Amazing tutor! Makes complex topics easy to understand. Highly recommend for anyone struggling with chemistry.',
    helpful: 5,
    sessionCount: 15,
  },
  {
    id: 4,
    student: {
      name: 'David Wilson',
      avatar: '',
    },
    rating: 4,
    date: '2024-01-08',
    course: 'Mathematics',
    comment: 'Very helpful and responsive. Good at identifying weak areas and providing targeted practice.',
    helpful: 1,
    sessionCount: 6,
  },
];

const TutorReviews: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState('all');
  const [courseFilter, setCourseFilter] = useState('all');

  // Filter reviews
  const filteredReviews = mockReviews.filter(review => {
    const matchesSearch = review.comment.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         review.course.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRating = ratingFilter === 'all' || review.rating.toString() === ratingFilter;
    const matchesCourse = courseFilter === 'all' || review.course === courseFilter;
    
    return matchesSearch && matchesRating && matchesCourse;
  });

  const getRatingPercentage = (rating: number) => {
    return (mockReviewStats.ratingDistribution[rating as keyof typeof mockReviewStats.ratingDistribution] / mockReviewStats.totalReviews) * 100;
  };

  const StatCard = ({ title, value, icon, color, subtitle }: any) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box
              sx={{
                p: 1,
                borderRadius: 2,
                bgcolor: `${color}.light`,
                color: `${color}.main`,
                mr: 2,
              }}
            >
              {icon}
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                {value}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="caption" color="success.main">
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Reviews & Ratings
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        See what your students are saying about your tutoring
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Average Rating"
            value={mockReviewStats.averageRating}
            icon={<Star />}
            color="warning"
            subtitle={`${mockReviewStats.recentTrend} this month`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Total Reviews"
            value={mockReviewStats.totalReviews}
            icon={<Comment />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Positive Reviews"
            value={`${Math.round(((mockReviewStats.ratingDistribution[5] + mockReviewStats.ratingDistribution[4]) / mockReviewStats.totalReviews) * 100)}%`}
            icon={<ThumbUp />}
            color="success"
          />
        </Grid>
      </Grid>

      {/* Rating Distribution */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Rating Distribution
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Box sx={{ textAlign: 'center', mb: 2 }}>
              <Typography variant="h2" sx={{ fontWeight: 'bold', color: 'warning.main' }}>
                {mockReviewStats.averageRating}
              </Typography>
              <Rating value={mockReviewStats.averageRating} readOnly size="large" />
              <Typography variant="body2" color="text.secondary">
                Based on {mockReviewStats.totalReviews} reviews
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            {[5, 4, 3, 2, 1].map((rating) => (
              <Box key={rating} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2" sx={{ minWidth: 20 }}>
                  {rating}
                </Typography>
                <Star sx={{ fontSize: 16, color: 'warning.main', mx: 1 }} />
                <LinearProgress
                  variant="determinate"
                  value={getRatingPercentage(rating)}
                  sx={{ flex: 1, mx: 1, height: 8, borderRadius: 4 }}
                />
                <Typography variant="body2" sx={{ minWidth: 30 }}>
                  {mockReviewStats.ratingDistribution[rating as keyof typeof mockReviewStats.ratingDistribution]}
                </Typography>
              </Box>
            ))}
          </Grid>
        </Grid>
      </Paper>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              size="small"
              placeholder="Search reviews..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} sm={3} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Rating</InputLabel>
              <Select
                value={ratingFilter}
                label="Rating"
                onChange={(e) => setRatingFilter(e.target.value)}
              >
                <MenuItem value="all">All Ratings</MenuItem>
                <MenuItem value="5">5 Stars</MenuItem>
                <MenuItem value="4">4 Stars</MenuItem>
                <MenuItem value="3">3 Stars</MenuItem>
                <MenuItem value="2">2 Stars</MenuItem>
                <MenuItem value="1">1 Star</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Course</InputLabel>
              <Select
                value={courseFilter}
                label="Course"
                onChange={(e) => setCourseFilter(e.target.value)}
              >
                <MenuItem value="all">All Courses</MenuItem>
                <MenuItem value="Mathematics">Mathematics</MenuItem>
                <MenuItem value="Physics">Physics</MenuItem>
                <MenuItem value="Chemistry">Chemistry</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Reviews List */}
      <Box sx={{ space: 2 }}>
        {filteredReviews.map((review) => (
          <motion.div
            key={review.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Paper sx={{ p: 3, mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                <Avatar sx={{ mr: 2 }}>
                  {review.student.name.charAt(0)}
                </Avatar>
                <Box sx={{ flex: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle1" sx={{ mr: 2 }}>
                      {review.student.name}
                    </Typography>
                    <Chip
                      label={review.course}
                      size="small"
                      variant="outlined"
                      sx={{ mr: 2 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {review.sessionCount} sessions
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Rating value={review.rating} readOnly size="small" />
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                      {new Date(review.date).toLocaleDateString()}
                    </Typography>
                  </Box>
                </Box>
              </Box>
              
              <Typography variant="body1" sx={{ mb: 2 }}>
                {review.comment}
              </Typography>
              
              <Divider sx={{ mb: 1 }} />
              
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <ThumbUp sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                  <Typography variant="caption" color="text.secondary">
                    {review.helpful} found this helpful
                  </Typography>
                </Box>
                <Button size="small" variant="outlined">
                  Respond
                </Button>
              </Box>
            </Paper>
          </motion.div>
        ))}
      </Box>

      {filteredReviews.length === 0 && (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            No reviews found matching your criteria.
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default TutorReviews;
