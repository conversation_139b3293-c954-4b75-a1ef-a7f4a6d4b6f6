import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Chip,
  Button,
  TextField,
  InputAdornment,
  Grid,
  Card,
  CardContent,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Search,
  MoreVert,
  Email,
  Phone,
  Event,
  School,
  TrendingUp,
  Person,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';

// Mock data for now - replace with actual API calls
const mockStudents = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '',
    totalSessions: 12,
    completedSessions: 10,
    upcomingSessions: 2,
    averageRating: 4.8,
    lastSession: '2024-01-15',
    courses: ['Mathematics', 'Physics'],
    status: 'active',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '',
    totalSessions: 8,
    completedSessions: 7,
    upcomingSessions: 1,
    averageRating: 4.5,
    lastSession: '2024-01-12',
    courses: ['Chemistry'],
    status: 'active',
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '',
    totalSessions: 15,
    completedSessions: 15,
    upcomingSessions: 0,
    averageRating: 4.9,
    lastSession: '2024-01-10',
    courses: ['Mathematics', 'Statistics'],
    status: 'inactive',
  },
];

const TutorStudents: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedStudent, setSelectedStudent] = useState<number | null>(null);

  // Filter students based on search term
  const filteredStudents = mockStudents.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.courses.some(course => course.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, studentId: number) => {
    setAnchorEl(event.currentTarget);
    setSelectedStudent(studentId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedStudent(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'default';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        My Students
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Manage and track your students' progress
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Person sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4">{mockStudents.length}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Students
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUp sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4">
                    {mockStudents.filter(s => s.status === 'active').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Students
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Event sx={{ fontSize: 40, color: 'warning.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4">
                    {mockStudents.reduce((sum, s) => sum + s.upcomingSessions, 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Upcoming Sessions
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <School sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
                <Box>
                  <Typography variant="h4">
                    {mockStudents.reduce((sum, s) => sum + s.completedSessions, 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Sessions
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search students by name, email, or course..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
        />
      </Paper>

      {/* Students Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Student</TableCell>
              <TableCell>Courses</TableCell>
              <TableCell align="center">Sessions</TableCell>
              <TableCell align="center">Rating</TableCell>
              <TableCell align="center">Status</TableCell>
              <TableCell align="center">Last Session</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredStudents.map((student) => (
              <TableRow key={student.id} hover>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ mr: 2 }}>
                      {student.name.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle2">
                        {student.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {student.email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                    {student.courses.map((course) => (
                      <Chip
                        key={course}
                        label={course}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </TableCell>
                <TableCell align="center">
                  <Typography variant="body2">
                    {student.completedSessions}/{student.totalSessions}
                  </Typography>
                  {student.upcomingSessions > 0 && (
                    <Typography variant="caption" color="primary">
                      +{student.upcomingSessions} upcoming
                    </Typography>
                  )}
                </TableCell>
                <TableCell align="center">
                  <Typography variant="body2">
                    ⭐ {student.averageRating}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Chip
                    label={student.status}
                    color={getStatusColor(student.status) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell align="center">
                  <Typography variant="body2">
                    {new Date(student.lastSession).toLocaleDateString()}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <IconButton
                    onClick={(e) => handleMenuClick(e, student.id)}
                  >
                    <MoreVert />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <Email sx={{ mr: 1 }} />
          Send Message
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Event sx={{ mr: 1 }} />
          Schedule Session
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <TrendingUp sx={{ mr: 1 }} />
          View Progress
        </MenuItem>
      </Menu>

      {filteredStudents.length === 0 && (
        <Alert severity="info" sx={{ mt: 2 }}>
          No students found matching your search criteria.
        </Alert>
      )}
    </Box>
  );
};

export default TutorStudents;
