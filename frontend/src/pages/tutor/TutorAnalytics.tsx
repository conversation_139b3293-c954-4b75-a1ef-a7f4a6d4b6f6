import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  TrendingUp,
  Event,
  People,
  Star,
  AttachMoney,
  School,
  AccessTime,
  BarChart,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

// Mock analytics data
const mockAnalytics = {
  overview: {
    totalSessions: 45,
    totalStudents: 12,
    averageRating: 4.7,
    totalEarnings: 2450.00,
    averageSessionDuration: 75, // minutes
    completionRate: 94.5, // percentage
  },
  monthlyTrends: {
    sessions: [8, 12, 15, 18, 22, 25],
    earnings: [400, 600, 750, 900, 1100, 1250],
    students: [3, 5, 7, 9, 11, 12],
    months: ['Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan'],
  },
  coursePerformance: [
    { course: 'Mathematics', sessions: 20, students: 8, avgRating: 4.8, earnings: 1200 },
    { course: 'Physics', sessions: 15, students: 6, avgRating: 4.6, earnings: 900 },
    { course: 'Chemistry', sessions: 10, students: 4, avgRating: 4.7, earnings: 550 },
  ],
  timeSlotAnalysis: [
    { timeSlot: '9:00 AM - 12:00 PM', sessions: 15, utilization: 75 },
    { timeSlot: '12:00 PM - 3:00 PM', sessions: 12, utilization: 60 },
    { timeSlot: '3:00 PM - 6:00 PM', sessions: 18, utilization: 90 },
    { timeSlot: '6:00 PM - 9:00 PM', sessions: 8, utilization: 40 },
  ],
  studentRetention: {
    newStudents: 4,
    returningStudents: 8,
    retentionRate: 85.7,
  },
};

const TutorAnalytics: React.FC = () => {
  const [timeFilter, setTimeFilter] = useState('last6Months');

  const StatCard = ({ title, value, icon, color, subtitle, trend }: any) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Box
              sx={{
                p: 1,
                borderRadius: 2,
                bgcolor: `${color}.light`,
                color: `${color}.main`,
                mr: 2,
              }}
            >
              {icon}
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                {value}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="caption" color="text.secondary">
                  {subtitle}
                </Typography>
              )}
              {trend && (
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                  <TrendingUp sx={{ fontSize: 14, color: 'success.main', mr: 0.5 }} />
                  <Typography variant="caption" color="success.main">
                    {trend}
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Analytics & Insights
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Track your tutoring performance and growth
          </Typography>
        </Box>
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>Time Period</InputLabel>
          <Select
            value={timeFilter}
            label="Time Period"
            onChange={(e) => setTimeFilter(e.target.value)}
          >
            <MenuItem value="lastMonth">Last Month</MenuItem>
            <MenuItem value="last3Months">Last 3 Months</MenuItem>
            <MenuItem value="last6Months">Last 6 Months</MenuItem>
            <MenuItem value="lastYear">Last Year</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Overview Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Total Sessions"
            value={mockAnalytics.overview.totalSessions}
            icon={<Event />}
            color="primary"
            trend="+12% this month"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Active Students"
            value={mockAnalytics.overview.totalStudents}
            icon={<People />}
            color="success"
            trend="+3 new students"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Average Rating"
            value={mockAnalytics.overview.averageRating}
            icon={<Star />}
            color="warning"
            trend="+0.2 improvement"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Total Earnings"
            value={`$${mockAnalytics.overview.totalEarnings.toFixed(2)}`}
            icon={<AttachMoney />}
            color="info"
            trend="+18% growth"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Avg Session Duration"
            value={`${mockAnalytics.overview.averageSessionDuration} min`}
            icon={<AccessTime />}
            color="secondary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Completion Rate"
            value={`${mockAnalytics.overview.completionRate}%`}
            icon={<BarChart />}
            color="success"
          />
        </Grid>
      </Grid>

      {/* Course Performance */}
      <Paper sx={{ mb: 3 }}>
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6">Course Performance</Typography>
        </Box>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Course</TableCell>
                <TableCell align="center">Sessions</TableCell>
                <TableCell align="center">Students</TableCell>
                <TableCell align="center">Avg Rating</TableCell>
                <TableCell align="right">Earnings</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {mockAnalytics.coursePerformance.map((course) => (
                <TableRow key={course.course} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <School sx={{ mr: 1, color: 'primary.main' }} />
                      {course.course}
                    </Box>
                  </TableCell>
                  <TableCell align="center">{course.sessions}</TableCell>
                  <TableCell align="center">{course.students}</TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <Star sx={{ fontSize: 16, color: 'warning.main', mr: 0.5 }} />
                      {course.avgRating}
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      ${course.earnings}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Grid container spacing={3}>
        {/* Time Slot Analysis */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Time Slot Performance
            </Typography>
            {mockAnalytics.timeSlotAnalysis.map((slot) => (
              <Box key={slot.timeSlot} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">{slot.timeSlot}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {slot.sessions} sessions ({slot.utilization}%)
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={slot.utilization}
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </Box>
            ))}
          </Paper>
        </Grid>

        {/* Student Retention */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Student Retention
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" color="primary.main">
                      {mockAnalytics.studentRetention.newStudents}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      New Students
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" color="success.main">
                      {mockAnalytics.studentRetention.returningStudents}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Returning Students
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Typography variant="h5" color="success.main">
                {mockAnalytics.studentRetention.retentionRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Retention Rate
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Performance Insights */}
      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Performance Insights
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <Box sx={{ p: 2, bgcolor: 'success.light', borderRadius: 2 }}>
              <Typography variant="subtitle2" color="success.dark">
                🎯 Peak Performance
              </Typography>
              <Typography variant="body2" color="success.dark">
                Your 3-6 PM time slot has the highest utilization rate at 90%
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ p: 2, bgcolor: 'info.light', borderRadius: 2 }}>
              <Typography variant="subtitle2" color="info.dark">
                📈 Growth Opportunity
              </Typography>
              <Typography variant="body2" color="info.dark">
                Consider expanding Chemistry tutoring - high demand and ratings
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ p: 2, bgcolor: 'warning.light', borderRadius: 2 }}>
              <Typography variant="subtitle2" color="warning.dark">
                ⚡ Quick Win
              </Typography>
              <Typography variant="body2" color="warning.dark">
                Evening slots (6-9 PM) have low utilization - potential for growth
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default TutorAnalytics;
