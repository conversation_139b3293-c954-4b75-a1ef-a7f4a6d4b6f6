import React, { useState } from 'react';
import {
  Box,
  Typography,
  Container,
  Paper,
  Breadcrumbs,
  Link,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  ViewCarousel as CardIcon,
  Home as HomeIcon,
  AutoAwesome as AutoAwesomeIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

import { useAuth } from '../../contexts/AuthContext';
import FlashCardGenerationTool from '../../components/Tools/FlashCardGenerationTool';
import GeneratedFlashCardsTable from '../../components/Tools/GeneratedFlashCardsTable';

// Tab Panel Component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`flashcard-tabpanel-${index}`}
      aria-labelledby={`flashcard-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{
          py: { xs: 2, sm: 3 },
          px: { xs: 1, sm: 2, md: 3 }
        }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `flashcard-tab-${index}`,
    'aria-controls': `flashcard-tabpanel-${index}`,
  };
}

const FlashCardGenerationPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (!user) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5">Please log in to generate flash cards</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Breadcrumbs */}
      <Container maxWidth="lg">
        <Box sx={{ pt: 2, pb: 1 }}>
          <Breadcrumbs aria-label="breadcrumb">
            <Link
              color="inherit"
              href="/dashboard"
              sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
            >
              <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Dashboard
            </Link>
            <Link
              color="inherit"
              href="/tools"
              sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
            >
              <AutoAwesomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Study Tools
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
              <CardIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Flash Card Generator
            </Typography>
          </Breadcrumbs>
        </Box>
      </Container>

      {/* Main Content */}
      <Container maxWidth="lg" sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
        <Box sx={{ py: { xs: 1, md: 3 } }}>
          {/* Page Header */}
          <Box sx={{ mb: { xs: 2, md: 4 }, px: { xs: 1, sm: 0 } }}>
            <Typography
              variant={isMobile ? "h5" : "h3"}
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' }
              }}
            >
              Flash Card Generator
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                mb: 3,
                fontSize: { xs: '0.9rem', sm: '1rem' }
              }}
            >
              Create interactive flash cards from your study notes
            </Typography>
          </Box>

          {/* Tabs Container */}
          <Paper
            elevation={0}
            sx={{
              borderRadius: { xs: 1, sm: 2 },
              border: 1,
              borderColor: 'divider',
              overflow: 'hidden',
              mx: { xs: 0, sm: 0 }
            }}
          >
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="Flash card generator tabs"
                variant={isMobile ? "fullWidth" : "standard"}
                sx={{
                  '& .MuiTab-root': {
                    minHeight: { xs: 56, sm: 64 },
                    textTransform: 'none',
                    fontSize: { xs: '0.875rem', sm: '1rem' },
                    fontWeight: 500,
                    px: { xs: 1, sm: 2 }
                  },
                  '& .MuiTabs-flexContainer': {
                    flexDirection: { xs: 'row', sm: 'row' }
                  }
                }}
              >
                <Tab
                  icon={<AutoAwesomeIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />}
                  iconPosition={isMobile ? "top" : "start"}
                  label={isMobile ? "Generate" : "Generate Cards"}
                  {...a11yProps(0)}
                  sx={{
                    '& .MuiTab-iconWrapper': {
                      mb: { xs: 0.5, sm: 0 },
                      mr: { xs: 0, sm: 1 }
                    }
                  }}
                />
                <Tab
                  icon={<PsychologyIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />}
                  iconPosition={isMobile ? "top" : "start"}
                  label={isMobile ? "My Cards" : "My Flash Cards"}
                  {...a11yProps(1)}
                  sx={{
                    '& .MuiTab-iconWrapper': {
                      mb: { xs: 0.5, sm: 0 },
                      mr: { xs: 0, sm: 1 }
                    }
                  }}
                />
              </Tabs>
            </Box>

            {/* Tab Panels */}
            <TabPanel value={tabValue} index={0}>
              <FlashCardGenerationTool />
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <GeneratedFlashCardsTable />
            </TabPanel>

          </Paper>
        </Box>
      </Container>
    </Box>
  );
};

export default FlashCardGenerationPage;
