:root {
  font-family: '<PERSON><PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-y: auto;
  position: relative;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 118, 255, 0.5);
  border-radius: 10px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 118, 255, 0.7);
}

/* Selection color */
::selection {
  background-color: rgba(0, 118, 255, 0.2);
  color: inherit;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 118, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 118, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 118, 255, 0);
  }
}

/* Utility classes */
.fadeIn {
  animation: fadeIn 0.5s ease forwards;
}

.slideUp {
  animation: slideUp 0.5s ease forwards;
}

.pulse {
  animation: pulse 2s infinite;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(90deg, #1976d2, #9c27b0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
}

/* Math rendering styles */
.katex {
  font-size: 1.1em !important;
  line-height: 1.5 !important;
  font-family: 'KaTeX_Main', serif !important;
}

.katex-display {
  margin: 1.5em 0 !important;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0.5em 0;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

.katex-display > .katex {
  max-width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  display: inline-block !important;
  text-align: center;
  padding: 0.5em;
}

/* Fix for inline math */
.katex-inline {
  display: inline-block;
  vertical-align: middle;
}

/* Ensure proper spacing in paragraphs */
p {
  margin-bottom: 1em;
}

/* Fix for lists with math */
li .katex {
  vertical-align: middle;
}

/* Dark mode support for math rendering */
.MuiPaper-root[data-mui-color-scheme="dark"] .katex {
  color: #f5f5f5;
}

.MuiPaper-root[data-mui-color-scheme="dark"] .katex-display {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Specific styles for AI assistant responses */
.ai-response-content {
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

.practice-help-content {
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

/* Fix for long equations */
.katex-html {
  max-width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 5px; /* Add space for scrollbar */
}

/* Fix for fractions and other tall elements */
.katex .vlist-t2 {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

/* Improve math rendering on mobile */
@media (max-width: 600px) {
  .katex-display {
    font-size: 0.9em !important;
    padding: 0.3em 0;
  }

  .katex {
    font-size: 1em !important;
  }

  /* Smaller padding on mobile */
  .ai-response-content p,
  .practice-help-content p {
    margin-bottom: 0.75em;
  }
}
