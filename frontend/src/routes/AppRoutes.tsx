import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import MainLayout from '../components/Layout/MainLayout';
import DashboardLayout from '../components/Layout/DashboardLayout';
import ExamLayout from '../components/Layout/ExamLayout';
import Home from '../pages/Home';
import LoginNew from '../pages/auth/LoginNew';
import RegisterEnhanced from '../pages/auth/RegisterEnhanced';
import EmailVerification from '../pages/auth/EmailVerification';
import EmailVerificationPending from '../pages/auth/EmailVerificationPending';
import ForgotPassword from '../pages/ForgotPassword';
import ResetPasswordNew from '../pages/ResetPasswordNew';
import AdminRoutes from './AdminRoutes';
import TutorRoutes from './TutorRoutes';


import Dashboard from '../pages/Dashboard';
import ProtectedRoute from './ProtectedRoute';

// MCQ pages
import MCQDashboard from '../pages/mcq/MCQDashboard';
import PracticeMode from '../pages/mcq/PracticeMode';
import ExamMode from '../pages/mcq/ExamMode';
import ExamResults from '../pages/mcq/ExamResults';
import MCQHistory from '../pages/mcq/MCQHistory';
import MCQBookmarks from '../pages/mcq/MCQBookmarks';
import ExamHistory from '../pages/mcq/ExamHistory';
import ExamDetails from '../pages/mcq/ExamDetails';

// School pages
import SchoolsList from '../pages/schools/SchoolsList';
import SchoolDetail from '../pages/schools/SchoolDetail';
import SchoolForm from '../pages/schools/SchoolForm';

// Department pages
import DepartmentsList from '../pages/departments/DepartmentsList';
import DepartmentDetail from '../pages/departments/DepartmentDetail';
import DepartmentForm from '../pages/departments/DepartmentForm';

// Course pages
import CoursesList from '../pages/courses/CoursesList';
import CourseDetail from '../pages/courses/CourseDetail';
import CourseForm from '../pages/courses/CourseForm';

// Question pages
import QuestionsList from '../pages/questions/QuestionsList';
import QuestionDetail from '../pages/questions/QuestionDetail';
import QuestionForm from '../pages/questions/QuestionForm';

// Session pages
import SessionsList from '../pages/sessions/SessionsList';
import SessionDetail from '../pages/sessions/SessionDetail';
import SessionForm from '../pages/sessions/SessionForm';

// User pages
import UsersList from '../pages/users/UsersList';
import UserDetail from '../pages/users/UserDetail';
import UserForm from '../pages/users/UserForm';
import Profile from '../pages/profile/Profile';
import SimpleProfileCompletion from '../pages/profile/SimpleProfileCompletion';
import Gamification from '../pages/Gamification';
import ToolsDashboard from '../pages/tools/ToolsDashboard';
import PDFUploadPage from '../pages/tools/PDFUploadPage';
import MCQGenerationPage from '../pages/tools/MCQGenerationPage';
import FlashCardGenerationPage from '../pages/tools/FlashCardGenerationPage';
import SummariesPage from '../pages/tools/SummariesPage';
import FlashCardPage from '../pages/flashcards/FlashCardPage';
import TutorDiscovery from '../pages/tutors/TutorDiscovery';
import TutorProfile from '../pages/tutors/TutorProfile';
import TutorProfileCompletion from '../pages/tutor/TutorProfileCompletion';
import TutorRedirect from '../components/TutorRedirect';

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Admin Routes - Separate from main app */}
      <Route path="/admin/*" element={<AdminRoutes />} />

      {/* Tutor Routes - Separate layout for tutors */}
      <Route path="/tutor/*" element={<TutorRoutes />} />

      {/* Landing page outside of MainLayout */}
      <Route path="/" element={<Home />} />

      {/* Login page outside of MainLayout */}
      <Route path="/login" element={<LoginNew />} />

      {/* Register page outside of MainLayout */}
      <Route path="/register" element={<RegisterEnhanced />} />

      {/* Email verification pages outside of MainLayout */}
      <Route path="/verify-email" element={<EmailVerification />} />
      <Route path="/verify-email-pending" element={<EmailVerificationPending />} />

      {/* Password reset pages outside of MainLayout */}
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/reset-password" element={<ResetPasswordNew />} />

      {/* Profile completion page outside of MainLayout */}
      <Route path="/complete-profile" element={
        <ProtectedRoute skipProfileCheck={true}>
          <SimpleProfileCompletion />
        </ProtectedRoute>
      } />

      {/* Tutor profile completion page outside of MainLayout */}
      <Route path="/complete-tutor-profile" element={
        <ProtectedRoute requiredRole="tutor" skipProfileCheck={true}>
          <TutorProfileCompletion />
        </ProtectedRoute>
      } />

      {/* Dashboard with its own layout - Student only */}
      <Route path="/dashboard" element={
        <ProtectedRoute requiredRole="student">
          <DashboardLayout>
            <Dashboard />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Exam mode with special layout (no header/footer) */}
      <Route path="/mcq/exam/:courseId" element={
        <ProtectedRoute requiredRole="student">
          <ExamLayout>
            <ExamMode />
          </ExamLayout>
        </ProtectedRoute>
      } />

      {/* Exam results page */}
      <Route path="/mcq/results/:examId" element={
        <ProtectedRoute requiredRole="student">
          <MainLayout>
            <ExamResults />
          </MainLayout>
        </ProtectedRoute>
      } />

      {/* All other routes inside MainLayout */}
      <Route
        path="/*"
        element={
          <MainLayout>
            <Routes>
              {/* Routes for all authenticated users */}
              <Route element={<ProtectedRoute />}>
                {/* Common routes for all users */}
                <Route path="/profile" element={<Profile />} />

                {/* Read-only routes for all users */}
                <Route path="/schools" element={<SchoolsList />} />
                <Route path="/schools/:id" element={<SchoolDetail />} />

                <Route path="/departments" element={<DepartmentsList />} />
                <Route path="/departments/:id" element={<DepartmentDetail />} />

                <Route path="/courses" element={<CoursesList />} />
                <Route path="/courses/:id" element={<CourseDetail />} />

                {/* Questions routes restricted to tutors and admins */}
                <Route element={<ProtectedRoute requiredRole="tutor" />}>
                  <Route path="/questions" element={<QuestionsList />} />
                  <Route path="/questions/:id" element={<QuestionDetail />} />
                </Route>
              </Route>

              {/* Student-specific routes */}
              <Route element={<ProtectedRoute requiredRole="student" />}>
                <Route path="/mcq" element={<MCQDashboard />} />
                <Route path="/mcq/practice/:courseId" element={<PracticeMode />} />
                <Route path="/mcq/history" element={<MCQHistory />} />
                <Route path="/mcq/bookmarks" element={<MCQBookmarks />} />
                <Route path="/mcq/exam-history" element={<ExamHistory />} />
                <Route path="/mcq/exam-details/:examId" element={<ExamDetails />} />
                <Route path="/gamification" element={<Gamification />} />

                {/* Tools routes */}
                <Route path="/tools" element={<ToolsDashboard />} />
                <Route path="/tools/upload" element={<PDFUploadPage />} />
                <Route path="/tools/mcq" element={<MCQGenerationPage />} />
                <Route path="/summaries" element={<SummariesPage />} />

                {/* Flash cards routes */}
                <Route path="/flash-cards" element={<FlashCardGenerationPage />} />
                <Route path="/flash-cards/view/:jobId" element={<FlashCardPage />} />
                <Route path="/flash-cards/study/:jobId" element={<FlashCardPage />} />
                <Route path="/flash-cards/card/:cardId" element={<FlashCardPage />} />

                <Route path="/sessions" element={
                  <TutorRedirect tutorPath="/tutor/sessions">
                    <SessionsList />
                  </TutorRedirect>
                } />
                <Route path="/sessions/:id" element={
                  <TutorRedirect tutorPath="/tutor/sessions" preserveParams={true}>
                    <SessionDetail />
                  </TutorRedirect>
                } />
                <Route path="/tutors" element={
                  <TutorRedirect tutorPath="/tutor/tutors">
                    <TutorDiscovery />
                  </TutorRedirect>
                } />
                <Route path="/tutors/:id" element={
                  <TutorRedirect tutorPath="/tutor/tutors" preserveParams={true}>
                    <TutorProfile />
                  </TutorRedirect>
                } />
              </Route>

              {/* Tutor-specific routes */}
              <Route element={<ProtectedRoute requiredRole="tutor" />}>
                <Route path="/sessions/new" element={<SessionForm />} />
                <Route path="/sessions/:id/edit" element={<SessionForm />} />
              </Route>

              {/* Admin-only routes are now handled by AdminRoutes */}

              {/* Fallback route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </MainLayout>
        }
      />
    </Routes>
  );
};

export default AppRoutes;
