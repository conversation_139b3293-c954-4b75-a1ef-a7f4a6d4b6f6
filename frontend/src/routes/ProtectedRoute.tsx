import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { CircularProgress, Box, Alert } from '@mui/material';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  requiredRole?: 'student' | 'tutor' | 'admin';
  children?: React.ReactNode;
  skipProfileCheck?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ requiredRole, children, skipProfileCheck = false }) => {
  const { isAuthenticated, user, isLoading, isProfileComplete, isEmailVerified } = useAuth();
  const location = useLocation();

  // Show loading state while authentication is being checked
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    // Save the location they were trying to access for redirecting after login
    return <Navigate to="/login" state={{ from: location }} />;
  }

  // Check if user needs to verify their email (except for profile completion page)
  if (isAuthenticated && !isEmailVerified && location.pathname !== '/complete-profile') {
    console.log('User needs to verify email, redirecting to email verification pending page');
    return <Navigate to="/verify-email-pending" state={{
      email: user?.email,
      message: 'Please verify your email address before accessing your account.'
    }} />;
  }

  // Check if user needs to complete their profile (only after email is verified)
  if (!skipProfileCheck && isAuthenticated && isEmailVerified && !isProfileComplete) {
    // Route tutors to tutor profile completion, others to regular profile completion
    if (user?.role === 'tutor' && location.pathname !== '/complete-tutor-profile') {
      console.log('Tutor needs to complete profile, redirecting to tutor profile completion page');
      return <Navigate to="/complete-tutor-profile" />;
    } else if (user?.role !== 'tutor' && location.pathname !== '/complete-profile') {
      console.log('User needs to complete profile, redirecting to profile completion page');
      return <Navigate to="/complete-profile" />;
    }
  }

  // Check if user has the required role
  if (requiredRole && user?.role !== requiredRole) {
    console.log(`Access denied: User role ${user?.role} does not match required role ${requiredRole}`);

    // Different handling based on user role
    if (user?.role === 'student' && (requiredRole === 'tutor' || requiredRole === 'admin')) {
      return (
        <Box sx={{ p: 3 }}>
          <Alert severity="error">
            Students do not have access to {requiredRole === 'tutor' ? 'tutor' : 'admin'} features.
            Please contact your administrator if you believe this is an error.
          </Alert>
        </Box>
      );
    } else if (user?.role === 'tutor' && requiredRole === 'admin') {
      return (
        <Box sx={{ p: 3 }}>
          <Alert severity="error">
            Tutors do not have access to administrative features.
            Please contact your administrator if you believe this is an error.
          </Alert>
        </Box>
      );
    } else if (user?.role === 'tutor' && requiredRole === 'student') {
      return (
        <Box sx={{ p: 3 }}>
          <Alert severity="error">
            This feature is only available to students.
            Tutors have access to different features.
          </Alert>
        </Box>
      );
    } else if (user?.role === 'admin' && (requiredRole === 'student' || requiredRole === 'tutor')) {
      // Admins can access student and tutor features for testing purposes
      console.log(`Admin accessing ${requiredRole} feature`);
      return children ? <>{children}</> : <Outlet />;
    }

    // Default fallback - redirect to role-specific dashboard
    if (user?.role === 'admin') {
      return <Navigate to="/admin/dashboard" />;
    } else if (user?.role === 'tutor') {
      return <Navigate to="/tutor/dashboard" />;
    } else {
      return <Navigate to="/dashboard" />; // Student dashboard
    }
  }

  // If children are provided, render them, otherwise use Outlet
  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoute;
