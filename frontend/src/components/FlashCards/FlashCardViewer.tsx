import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  IconButton,
  CircularProgress,
  Alert,
  Chip,
  useTheme,
  useMediaQuery,
  LinearProgress,
  Tooltip
} from '@mui/material';
import {
  NavigateNext as NextIcon,
  NavigateBefore as PrevIcon,
  Refresh as RefreshIcon,
  School as SchoolIcon,
  Topic as TopicIcon
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { getFlashCards, getFlashCardGenerationStatus } from '../../api/studentTools';
import { getCourse } from '../../api/courses';
import { FlashCard as FlashCardType } from '../../api/studentTools';
import '../../styles/FlashCard.css';

interface FlashCardViewerProps {
  jobId?: number;
  courseId?: number;
  cardId?: number;
}

const FlashCardViewer: React.FC<FlashCardViewerProps> = ({ jobId, courseId, cardId }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // State
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [courseName, setCourseName] = useState<string | null>(null);

  // Fetch flash cards
  const {
    data: flashCards = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['flashCards', jobId, courseId, cardId],
    queryFn: async () => {
      console.log('Fetching flash cards with params:', { jobId, courseId, cardId });

      let cards = [];

      if (jobId) {
        // Get flash cards by job ID
        console.log(`Fetching flash cards for job ID: ${jobId}`);
        try {
          // First, check if the job exists and is completed
          const jobStatus = await getFlashCardGenerationStatus(jobId);
          console.log(`Job status for ID ${jobId}:`, jobStatus);
          console.log(`Job generated_card_ids:`, jobStatus.generated_card_ids);

          // Check if job is completed
          if (jobStatus.status === 'COMPLETED' || jobStatus.status === 'completed') {
            console.log(`Job ${jobId} is completed, fetching cards`);

            // Fetch cards for this job
            cards = await getFlashCards({ jobId });
            console.log(`Received ${cards.length} flash cards for job ID ${jobId}:`, cards);

            if (cards.length === 0) {
              console.warn(`API returned 0 cards for job ${jobId}, trying to fetch all cards and filter`);

              // Try to fetch all cards and filter by job_id as a fallback
              const allCards = await getFlashCards();
              console.log(`Fetched ${allCards.length} total flash cards`);

              // First try to filter by job_id directly
              let filteredCards = allCards.filter(card => card.job_id === parseInt(jobId.toString()));
              console.log(`Filtered ${filteredCards.length} cards with job_id=${jobId} from all cards`);

              // If that doesn't work, try using the generated_card_ids from the job status
              if (filteredCards.length === 0 && jobStatus.generated_card_ids && jobStatus.generated_card_ids.length > 0) {
                console.log(`Job ${jobId} has ${jobStatus.generated_card_ids.length} generated_card_ids, filtering by card IDs`);
                filteredCards = allCards.filter(card => jobStatus.generated_card_ids?.includes(card.id));
                console.log(`Filtered ${filteredCards.length} cards by generated_card_ids from all cards`);
              }

              // If that doesn't work, try using the course_name from the job status
              if (filteredCards.length === 0 && jobStatus.course_name) {
                console.log(`Job ${jobId} has course_name ${jobStatus.course_name}, filtering by course`);
                filteredCards = allCards.filter(card => card.course_name === jobStatus.course_name);
                console.log(`Filtered ${filteredCards.length} cards for course name "${jobStatus.course_name}" from all cards`);
              }

              // If we found cards using any method, use them
              if (filteredCards.length > 0) {
                cards = filteredCards;
                console.log(`Using ${cards.length} filtered cards for job ${jobId}`);
              } else {
                console.warn(`Job ${jobId} has no associated cards, cannot display flash cards`);

                // As a last resort, if the job has generated_card_ids, try to fetch each card individually
                if (jobStatus.generated_card_ids && jobStatus.generated_card_ids.length > 0) {
                  console.log(`Trying to fetch ${jobStatus.generated_card_ids.length} cards individually by ID`);
                  const individualCards = [];
                  for (const cardId of jobStatus.generated_card_ids) {
                    try {
                      // Find the card in allCards
                      const card = allCards.find(c => c.id === cardId);
                      if (card) {
                        individualCards.push(card);
                        console.log(`Found card ${cardId} in allCards`);
                      } else {
                        console.warn(`Card ${cardId} not found in allCards`);
                      }
                    } catch (cardError) {
                      console.error(`Error fetching card ${cardId}:`, cardError);
                    }
                  }

                  if (individualCards.length > 0) {
                    console.log(`Found ${individualCards.length} individual cards for job ${jobId}`);
                    cards = individualCards;
                  }
                }
              }
            }
          } else {
            console.warn(`Job ${jobId} is not completed: ${jobStatus.status}`);
          }
        } catch (error) {
          console.error(`Error fetching flash cards for job ID ${jobId}:`, error);
        }
      } else if (courseId) {
        // Get all flash cards and filter by course ID
        console.log(`Fetching flash cards for course ID: ${courseId}`);
        try {
          // Make sure courseId is a valid number before fetching the course
          if (isNaN(Number(courseId))) {
            console.warn(`Invalid course ID: ${courseId}, skipping course fetch`);
          } else {
            // First get the course name
            const course = await getCourse(courseId);
            console.log(`Course name for ID ${courseId}: ${course.name}`);

            // Then get all flash cards and filter by course name
            cards = await getFlashCards();
            cards = cards.filter(card => card.course_name === course.name);
            console.log(`Filtered ${cards.length} flash cards for course name "${course.name}"`);

            // Set the course name for display
            setCourseName(course.name);
          }
        } catch (error) {
          console.error(`Error fetching flash cards for course ID ${courseId}:`, error);
        }
      } else if (cardId) {
        // Get all flash cards and find the specific card
        console.log(`Fetching flash card with ID: ${cardId}`);
        try {
          const allCards = await getFlashCards();
          const card = allCards.find(c => c.id === cardId);
          cards = card ? [card] : [];
          console.log(`Found card with ID ${cardId}:`, card);
        } catch (error) {
          console.error(`Error fetching flash card with ID ${cardId}:`, error);
        }
      } else {
        // Get all flash cards
        console.log('Fetching all flash cards');
        try {
          cards = await getFlashCards();
          console.log(`Received ${cards.length} flash cards`);
        } catch (error) {
          console.error('Error fetching all flash cards:', error);
        }
      }

      return cards;
    },
    staleTime: 60000 // 1 minute
  });

  // Set course name from flash cards if available
  useEffect(() => {
    if (jobId) {
      // For note-generated flash cards, use a generic name
      setCourseName('Generated Flash Cards');
    } else if (flashCards.length > 0 && flashCards[0].course_name) {
      setCourseName(flashCards[0].course_name);
    }
  }, [flashCards, jobId]);

  // Handle next card
  const handleNextCard = () => {
    if (currentCardIndex < flashCards.length - 1) {
      setIsFlipped(false);
      setTimeout(() => {
        setCurrentCardIndex(currentCardIndex + 1);
      }, 300);
    }
  };

  // Handle previous card
  const handlePrevCard = () => {
    if (currentCardIndex > 0) {
      setIsFlipped(false);
      setTimeout(() => {
        setCurrentCardIndex(currentCardIndex - 1);
      }, 300);
    }
  };

  // Handle flip card
  const handleFlipCard = () => {
    console.log('Flipping card, current state:', isFlipped);
    setIsFlipped(!isFlipped);
    console.log('Card flipped, new state:', !isFlipped);
  };

  // Get current card
  const currentCard = flashCards[currentCardIndex];

  // Calculate progress
  const progress = flashCards.length > 0 ? ((currentCardIndex + 1) / flashCards.length) * 100 : 0;

  // Generate a color based on the course name or topic
  const generateColor = () => {
    if (!currentCard) return theme.palette.mode === 'dark' ? '#333' : '#f5f5f5';

    const seed = currentCard.topic || courseName || 'general';
    const hue = seed.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % 360;

    return theme.palette.mode === 'dark'
      ? `hsl(${hue}, 70%, 25%)`
      : `hsl(${hue}, 70%, 85%)`;
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box>
          <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1 }}>
            Flash Cards
            {courseName && (
              <Chip
                icon={<SchoolIcon />}
                label={courseName}
                size="small"
              />
            )}
            {currentCard?.topic && (
              <Chip
                icon={<TopicIcon />}
                label={currentCard.topic}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Typography>
        </Box>
        <Tooltip title="Refresh">
          <IconButton onClick={() => refetch()}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {error ? (
        <Alert severity="error" sx={{ mb: 2 }}>
          Error loading flash cards: {error instanceof Error ? error.message : String(error)}
        </Alert>
      ) : isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : flashCards.length === 0 ? (
        <Alert severity="info">
          {jobId
            ? `No flash cards found for job ID ${jobId}. This could be because the job is still processing. Please wait a few minutes and try again, or check the job status in the "Tools" tab.`
            : "No flash cards found. Generate flash cards from your notes to see them here."}
        </Alert>
      ) : (
        <>
          {/* Progress bar */}
          <Box sx={{ width: '100%', mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="body2" color="text.secondary">
                Card {currentCardIndex + 1} of {flashCards.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {Math.round(progress)}% Complete
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={progress}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
              }}
            />
          </Box>

          {/* Flash Card */}
          <div className="card-container" onClick={handleFlipCard}>
            <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 1 }}>
              {isFlipped ? 'Showing Answer (Click to see question)' : 'Showing Question (Click to see answer)'}
            </Typography>
            <div className={`card-inner ${isFlipped ? 'flipped' : ''}`}>
              {/* Front of card (Question) */}
              <div
                className="card-front"
                style={{
                  backgroundColor: generateColor(),
                  color: theme.palette.mode === 'dark' ? 'white' : 'black'
                }}
              >
                {currentCard?.topic && (
                  <Chip
                    icon={<TopicIcon fontSize="small" />}
                    label={currentCard.topic}
                    size="small"
                    sx={{ mb: 2 }}
                  />
                )}
                <Typography
                  variant={isMobile ? "h6" : "h5"}
                  component="div"
                  align="center"
                  sx={{
                    fontWeight: 'bold',
                    mb: 2
                  }}
                >
                  {currentCard?.front_content}
                </Typography>
                <Typography
                  variant="body2"
                  color={theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.6)'}
                  align="center"
                  sx={{ mt: 2 }}
                >
                  (Click to see answer)
                </Typography>
              </div>

              {/* Back of card (Answer) */}
              <div
                className="card-back"
                style={{
                  backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.common.white,
                  color: theme.palette.mode === 'dark' ? theme.palette.common.white : theme.palette.common.black
                }}
              >
                <Typography
                  variant="body1"
                  component="div"
                  align="center"
                  sx={{ mb: 2 }}
                >
                  {currentCard?.back_content}
                </Typography>
                <Typography
                  variant="body2"
                  color={theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.6)'}
                  align="center"
                  sx={{ mt: 2 }}
                >
                  (Click to see question)
                </Typography>
              </div>
            </div>
          </div>

          {/* Navigation buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<PrevIcon />}
              onClick={handlePrevCard}
              disabled={currentCardIndex === 0}
            >
              Previous
            </Button>
            <Button
              variant="contained"
              endIcon={<NextIcon />}
              onClick={handleNextCard}
              disabled={currentCardIndex === flashCards.length - 1}
            >
              Next
            </Button>
          </Box>
        </>
      )}
    </Box>
  );
};

export default FlashCardViewer;
