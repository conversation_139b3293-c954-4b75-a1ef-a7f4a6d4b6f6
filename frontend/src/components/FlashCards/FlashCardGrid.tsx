import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Chip,
  IconButton,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  Pagination,
  Dialog,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Skeleton,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  School as SchoolIcon,
  Topic as TopicIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { getFlashCards, getFlashCardGenerationStatus } from '../../api/studentTools';
import { FlashCard } from '../../api/studentTools';
import { getCourse, getCourses } from '../../api/courses';
import { motion, AnimatePresence } from 'framer-motion';

interface FlashCardGridProps {
  jobId?: number;
  courseId?: number;
  onCardClick?: (card: FlashCard) => void;
}

const FlashCardGrid: React.FC<FlashCardGridProps> = ({ jobId, courseId, onCardClick }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  const [page, setPage] = useState(1);
  const [selectedCard, setSelectedCard] = useState<FlashCard | null>(null);
  const [showAnswer, setShowAnswer] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCourseId, setFilterCourseId] = useState<number | ''>('');
  const [filterTopic, setFilterTopic] = useState<string>('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(isMobile ? 'list' : 'grid');
  const [isFlipping, setIsFlipping] = useState(false);
  const [jobTitle, setJobTitle] = useState<string | null>(null);
  const cardsPerPage = viewMode === 'grid' ? 12 : 8;

  // Effect to update view mode based on screen size
  useEffect(() => {
    if (isMobile && viewMode === 'grid') {
      setViewMode('list');
    }
  }, [isMobile, viewMode]);

  // Fetch flash cards
  const {
    data: flashCards = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['flashCards', jobId, courseId],
    queryFn: async () => {
      if (jobId) {
        // Get flash cards by job ID
        console.log(`Fetching flash cards for job ID: ${jobId}`);
        try {
          // First, check if the job exists and is completed
          const jobStatus = await getFlashCardGenerationStatus(jobId);
          console.log(`Job status for ID ${jobId}:`, jobStatus);
          console.log(`Job generated_card_ids:`, jobStatus.generated_card_ids);

          // Set the job title
          setJobTitle(jobStatus.title || `Flash Cards #${jobId}`);

          // Check if job is completed
          if (jobStatus.status === 'COMPLETED' || jobStatus.status === 'completed') {
            console.log(`Job ${jobId} is completed, fetching cards`);

            // Fetch cards for this job
            const cards = await getFlashCards({ jobId });
            console.log(`Received ${cards.length} flash cards for job ID ${jobId}:`, cards);

            if (cards.length === 0) {
              console.warn(`API returned 0 cards for job ${jobId}, trying to fetch all cards and filter`);

              // Try to fetch all cards and filter by job_id as a fallback
              const allCards = await getFlashCards();
              console.log(`Fetched ${allCards.length} total flash cards`);

              // First try to filter by job_id directly
              let filteredCards = allCards.filter(card => card.job_id === parseInt(jobId.toString()));
              console.log(`Filtered ${filteredCards.length} cards with job_id=${jobId} from all cards`);

              // If that doesn't work, try using the generated_card_ids from the job status
              if (filteredCards.length === 0 && jobStatus.generated_card_ids && jobStatus.generated_card_ids.length > 0) {
                console.log(`Job ${jobId} has ${jobStatus.generated_card_ids.length} generated_card_ids, filtering by card IDs`);
                filteredCards = allCards.filter(card => jobStatus.generated_card_ids?.includes(card.id));
                console.log(`Filtered ${filteredCards.length} cards by generated_card_ids from all cards`);
              }

              // If that doesn't work, try using the course_name from the job status
              if (filteredCards.length === 0 && jobStatus.course_name) {
                console.log(`Job ${jobId} has course_name ${jobStatus.course_name}, filtering by course`);
                filteredCards = allCards.filter(card => card.course_name === jobStatus.course_name);
                console.log(`Filtered ${filteredCards.length} cards for course name "${jobStatus.course_name}" from all cards`);
              }

              // If we found cards using any method, use them
              if (filteredCards.length > 0) {
                console.log(`Using ${filteredCards.length} filtered cards for job ${jobId}`);
                return filteredCards;
              } else {
                console.warn(`Job ${jobId} has no associated cards, cannot display flash cards`);

                // As a last resort, if the job has generated_card_ids, try to fetch each card individually
                if (jobStatus.generated_card_ids && jobStatus.generated_card_ids.length > 0) {
                  console.log(`Trying to fetch ${jobStatus.generated_card_ids.length} cards individually by ID`);
                  const individualCards = [];
                  for (const cardId of jobStatus.generated_card_ids) {
                    try {
                      // Find the card in allCards
                      const card = allCards.find(c => c.id === cardId);
                      if (card) {
                        individualCards.push(card);
                        console.log(`Found card ${cardId} in allCards`);
                      } else {
                        console.warn(`Card ${cardId} not found in allCards`);
                      }
                    } catch (cardError) {
                      console.error(`Error fetching card ${cardId}:`, cardError);
                    }
                  }

                  if (individualCards.length > 0) {
                    console.log(`Found ${individualCards.length} individual cards for job ${jobId}`);
                    return individualCards;
                  }
                }
              }
            }

            return cards;
          } else {
            console.warn(`Job ${jobId} is not completed: ${jobStatus.status}`);
            return [];
          }
        } catch (error) {
          console.error(`Error fetching flash cards for job ID ${jobId}:`, error);
          return [];
        }
      } else if (courseId) {
        // Get all flash cards and filter by course name
        try {
          // Make sure courseId is a valid number before fetching the course
          if (isNaN(Number(courseId))) {
            console.warn(`Invalid course ID: ${courseId}, skipping course fetch`);
            return await getFlashCards();
          } else {
            const course = await getCourse(courseId);
            const cards = await getFlashCards();
            return cards.filter(card => card.course_name === course.name);
          }
        } catch (error) {
          console.error(`Error fetching course ${courseId}:`, error);
          return await getFlashCards();
        }
      } else {
        // Get all flash cards
        return await getFlashCards();
      }
    },
    staleTime: 60000 // 1 minute
  });

  // Fetch all courses for filtering
  const { data: courses = [] } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  // We don't need to fetch course names anymore since they're stored directly in the cards
  // But we'll keep a simplified version of this for backward compatibility
  const { data: courseMap = {} } = useQuery({
    queryKey: ['flashCardCourses', flashCards],
    queryFn: async () => {
      // Create a map of course IDs to course names from the cards themselves
      const courseMap: Record<number, string> = {};

      // For cards that have both course_id and course_name
      flashCards.forEach(card => {
        if (card.course_id && card.course_name) {
          courseMap[card.course_id] = card.course_name;
        }
      });

      // For any courseId prop that was passed in
      if (courseId && !isNaN(Number(courseId))) {
        try {
          const course = await getCourse(courseId);
          courseMap[courseId] = course.name;
        } catch (error) {
          console.error(`Error fetching course ${courseId}:`, error);
        }
      }

      return courseMap;
    },
    enabled: flashCards.length > 0 || !!courseId,
    staleTime: 5 * 60 * 1000 // 5 minutes
  });

  // Get unique topics from flash cards
  const topics = React.useMemo(() => {
    const topicSet = new Set<string>();
    flashCards.forEach(card => {
      if (card.topic) {
        topicSet.add(card.topic);
      }
    });
    return Array.from(topicSet).sort();
  }, [flashCards]);

  // Generate a color based on the topic or course
  const generateColor = (card: FlashCard) => {
    const seed = card.topic || card.course_name || (card.course_id && courseMap[card.course_id]) || 'general';
    const hue = seed.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % 360;

    return theme.palette.mode === 'dark'
      ? `hsl(${hue}, 70%, 25%)`
      : `hsl(${hue}, 70%, 85%)`;
  };

  // Handle search term change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(1); // Reset to first page when search changes
  };

  // Handle course filter change
  const handleCourseFilterChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setFilterCourseId(event.target.value as number | '');
    setPage(1); // Reset to first page when filter changes
  };

  // Handle topic filter change
  const handleTopicFilterChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setFilterTopic(event.target.value as string);
    setPage(1); // Reset to first page when filter changes
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setSearchTerm('');
    setFilterCourseId('');
    setFilterTopic('');
    setPage(1);
  };

  // Toggle view mode
  const handleToggleViewMode = () => {
    setViewMode(viewMode === 'grid' ? 'list' : 'grid');
  };

  // Handle page change
  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
    window.scrollTo(0, 0);
  };

  // Handle card click
  const handleCardClick = (card: FlashCard) => {
    if (onCardClick) {
      onCardClick(card);
    } else {
      setSelectedCard(card);
      setShowAnswer(false);
    }
  };

  // Handle dialog close
  const handleCloseDialog = () => {
    setSelectedCard(null);
    setShowAnswer(false);
  };

  // Handle flip card
  const handleFlipCard = () => {
    setIsFlipping(true);
    setTimeout(() => {
      setShowAnswer(!showAnswer);
      setIsFlipping(false);
    }, 150);
  };

  // Filter cards based on search term and filters
  const filteredCards = React.useMemo(() => {
    return flashCards.filter(card => {
      // Apply search term filter
      const matchesSearch = searchTerm === '' ||
        card.front_content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        card.back_content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (card.topic && card.topic.toLowerCase().includes(searchTerm.toLowerCase()));

      // Apply course filter
      const matchesCourse = filterCourseId === '' ||
        card.course_id === filterCourseId ||
        (filterCourseId && courseMap[filterCourseId] === card.course_name);

      // Apply topic filter
      const matchesTopic = filterTopic === '' || card.topic === filterTopic;

      return matchesSearch && matchesCourse && matchesTopic;
    });
  }, [flashCards, searchTerm, filterCourseId, filterTopic]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredCards.length / cardsPerPage);
  const paginatedCards = filteredCards.slice((page - 1) * cardsPerPage, page * cardsPerPage);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  const MotionCard = motion.create(Card);

  return (
    <Box sx={{ mb: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box>
          <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1 }}>
            Flash Cards
            {jobId && (
              <Chip
                label={jobTitle || `Flash Cards #${jobId}`}
                size="small"
              />
            )}
            {courseId && courseId !== 'note-generated' && courseMap[courseId] && (
              <Chip
                icon={<SchoolIcon />}
                label={courseMap[courseId]}
                size="small"
              />
            )}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            {filteredCards.length} {filteredCards.length === 1 ? 'card' : 'cards'} available
            {(searchTerm || filterCourseId || filterTopic) && ' (filtered)'}
          </Typography>
        </Box>
        <Box>
          {!isMobile && (
            <Tooltip title={viewMode === 'grid' ? 'Switch to List View' : 'Switch to Grid View'}>
              <IconButton onClick={handleToggleViewMode} sx={{ mr: 1 }}>
                {viewMode === 'grid' ? <ListViewIcon /> : <GridViewIcon />}
              </IconButton>
            </Tooltip>
          )}
          <Tooltip title="Refresh">
            <IconButton onClick={() => refetch()}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Search and Filters */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12, md: 4 }}>
            <TextField
              fullWidth
              placeholder="Search flash cards..."
              value={searchTerm}
              onChange={handleSearchChange}
              variant="outlined"
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton size="small" onClick={() => setSearchTerm('')}>
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
          </Grid>

          <Grid item xs={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel id="course-filter-label">Filter by Course</InputLabel>
              <Select
                labelId="course-filter-label"
                value={filterCourseId}
                onChange={handleCourseFilterChange}
                label="Filter by Course"
              >
                <MenuItem value="">All Courses</MenuItem>
                {courses.map(course => (
                  <MenuItem key={course.id} value={course.id}>
                    {course.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 6, md: 3 }}>
            <FormControl fullWidth size="small" disabled={topics.length === 0}>
              <InputLabel id="topic-filter-label">Filter by Topic</InputLabel>
              <Select
                labelId="topic-filter-label"
                value={filterTopic}
                onChange={handleTopicFilterChange}
                label="Filter by Topic"
              >
                <MenuItem value="">All Topics</MenuItem>
                {topics.map(topic => (
                  <MenuItem key={topic} value={topic}>
                    {topic}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, md: 2 }}>
            <Button
              variant="outlined"
              onClick={handleClearFilters}
              fullWidth
              disabled={!searchTerm && filterCourseId === '' && filterTopic === ''}
              startIcon={<FilterIcon />}
            >
              Clear Filters
            </Button>
          </Grid>
        </Grid>
      </Box>

      {error ? (
        <Alert severity="error" sx={{ mb: 2 }}>
          Error loading flash cards: {error instanceof Error ? error.message : String(error)}
        </Alert>
      ) : isLoading ? (
        <Box sx={{ mt: 4 }}>
          <Grid container spacing={2}>
            {Array.from(new Array(4)).map((_, index) => (
              <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={index}>
                <Skeleton variant="rectangular" height={200} sx={{ borderRadius: 2 }} />
              </Grid>
            ))}
          </Grid>
        </Box>
      ) : flashCards.length === 0 ? (
        <Alert severity="info">
          {jobId
            ? `No flash cards found for this job. The job may still be processing. Please wait a few minutes and try again, or check the job status in the "Tools" tab.`
            : "No flash cards found. Generate flash cards from your notes to see them here."}
        </Alert>
      ) : filteredCards.length === 0 ? (
        <Alert severity="info">
          No flash cards match your search or filters. Try adjusting your criteria.
        </Alert>
      ) : (
        <>
          <AnimatePresence mode="wait">
            {viewMode === 'grid' ? (
              <motion.div
                key="grid-view"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
              >
                <Grid container spacing={2}>
                  {paginatedCards.map((card) => (
                    <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={card.id}>
                      <MotionCard
                        variants={itemVariants}
                        sx={{
                          height: '200px',
                          backgroundColor: generateColor(card),
                          transition: 'transform 0.2s, box-shadow 0.2s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: '0 8px 16px rgba(0,0,0,0.15)'
                          },
                          borderRadius: 2,
                          overflow: 'hidden'
                        }}
                      >
                        <CardActionArea
                          sx={{ height: '100%' }}
                          onClick={() => handleCardClick(card)}
                        >
                          <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column', p: 2 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                              {card.topic && (
                                <Chip
                                  icon={<TopicIcon fontSize="small" />}
                                  label={card.topic}
                                  size="small"
                                  sx={{ alignSelf: 'flex-start' }}
                                />
                              )}
                              {card.course_name && (
                                <Tooltip title={card.course_name}>
                                  <SchoolIcon fontSize="small" color="action" />
                                </Tooltip>
                              )}
                            </Box>
                            <Typography
                              variant="body1"
                              sx={{
                                fontWeight: 'bold',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 4,
                                WebkitBoxOrient: 'vertical',
                                flex: 1
                              }}
                            >
                              {card.front_content}
                            </Typography>
                            <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                              Tap to study
                            </Typography>
                          </CardContent>
                        </CardActionArea>
                      </MotionCard>
                    </Grid>
                  ))}
                </Grid>
              </motion.div>
            ) : (
              <motion.div
                key="list-view"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
              >
                {paginatedCards.map((card) => (
                  <MotionCard
                    key={card.id}
                    variants={itemVariants}
                    sx={{
                      mb: 2,
                      borderLeft: `4px solid ${generateColor(card)}`,
                      transition: 'transform 0.2s, box-shadow 0.2s',
                      '&:hover': {
                        transform: 'translateX(4px)',
                        boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                      },
                      borderRadius: 2
                    }}
                  >
                    <CardActionArea onClick={() => handleCardClick(card)}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Typography variant="h6" component="div">
                            {card.front_content.length > 100
                              ? `${card.front_content.substring(0, 100)}...`
                              : card.front_content}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            {card.topic && (
                              <Chip
                                icon={<TopicIcon fontSize="small" />}
                                label={card.topic}
                                size="small"
                              />
                            )}
                            {card.course_id && courseMap[card.course_id] && (
                              <Chip
                                icon={<SchoolIcon fontSize="small" />}
                                label={courseMap[card.course_id]}
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </Box>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {card.back_content.length > 150
                            ? `${card.back_content.substring(0, 150)}...`
                            : card.back_content}
                        </Typography>
                      </CardContent>
                    </CardActionArea>
                  </MotionCard>
                ))}
              </motion.div>
            )}
          </AnimatePresence>

          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
                size={isMobile ? "small" : "medium"}
              />
            </Box>
          )}

          {/* Flash Card Dialog */}
          <Dialog
            open={!!selectedCard}
            onClose={handleCloseDialog}
            maxWidth="sm"
            fullWidth
            PaperProps={{
              sx: {
                borderRadius: 3,
                overflow: 'hidden'
              }
            }}
          >
            {selectedCard && (
              <Box sx={{ position: 'relative' }}>
                <IconButton
                  onClick={handleCloseDialog}
                  sx={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    zIndex: 1,
                    color: theme.palette.mode === 'dark' ? 'white' : 'black',
                    bgcolor: 'rgba(255,255,255,0.2)',
                    '&:hover': {
                      bgcolor: 'rgba(255,255,255,0.3)'
                    }
                  }}
                >
                  <CloseIcon />
                </IconButton>

                <Box sx={{
                  perspective: '1000px',
                  minHeight: isMobile ? '300px' : '400px'
                }}>
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={`dialog-card-${showAnswer ? 'back' : 'front'}`}
                      initial={isFlipping ? "hidden" : "visible"}
                      animate="visible"
                      exit="hidden"
                      variants={itemVariants}
                      style={{
                        width: '100%',
                        height: '100%',
                        position: 'relative',
                        transformStyle: 'preserve-3d'
                      }}
                    >
                      <DialogContent
                        sx={{
                          p: 0,
                          backgroundColor: showAnswer ?
                            (theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.common.white) :
                            generateColor(selectedCard),
                          minHeight: isMobile ? '300px' : '400px',
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          cursor: 'pointer',
                          transform: showAnswer ? 'rotateY(180deg)' : 'rotateY(0deg)',
                          transition: 'transform 0.6s',
                          backfaceVisibility: 'hidden'
                        }}
                        onClick={handleFlipCard}
                      >
                        {showAnswer ? (
                          // Back of card (Answer)
                          <Box sx={{
                            p: 4,
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            transform: 'rotateY(180deg)' // Flip text back so it's readable
                          }}>
                            <Typography
                              variant="body1"
                              component="div"
                              align="center"
                              sx={{
                                color: theme.palette.mode === 'dark' ? theme.palette.common.white : theme.palette.common.black,
                                mb: 2
                              }}
                            >
                              {selectedCard.back_content}
                            </Typography>
                            <Typography
                              variant="body2"
                              color={theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.6)'}
                              align="center"
                              sx={{ mt: 2 }}
                            >
                              (Tap to see question)
                            </Typography>
                          </Box>
                        ) : (
                          // Front of card (Question)
                          <Box sx={{ p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                            {selectedCard.topic && (
                              <Chip
                                icon={<TopicIcon fontSize="small" />}
                                label={selectedCard.topic}
                                size="small"
                                sx={{ mb: 2 }}
                              />
                            )}
                            <Typography
                              variant={isMobile ? "h6" : "h5"}
                              component="div"
                              align="center"
                              sx={{
                                fontWeight: 'bold',
                                mb: 2,
                                color: theme.palette.mode === 'dark' ? 'white' : 'black'
                              }}
                            >
                              {selectedCard.front_content}
                            </Typography>
                            <Typography
                              variant="body2"
                              color={theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.6)'}
                              align="center"
                              sx={{ mt: 2 }}
                            >
                              (Tap to see answer)
                            </Typography>
                          </Box>
                        )}
                      </DialogContent>
                    </motion.div>
                  </AnimatePresence>
                </Box>

                <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>
                  <Button
                    variant="outlined"
                    onClick={handleCloseDialog}
                    startIcon={<CloseIcon />}
                  >
                    Close
                  </Button>
                  {onCardClick && (
                    <Button
                      variant="contained"
                      onClick={() => {
                        handleCloseDialog();
                        if (onCardClick && selectedCard) {
                          onCardClick(selectedCard);
                        }
                      }}
                    >
                      Study This Card
                    </Button>
                  )}
                </DialogActions>
              </Box>
            )}
          </Dialog>
        </>
      )}
    </Box>
  );
};

export default FlashCardGrid;
