import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  LinearProgress,
  Chip,
  Avatar,
  useTheme,
  useMediaQuery,
  Button
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  QuestionAnswer as QuestionIcon,
  Bookmark as BookmarkIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Bookmark } from '../../api/studentProgress';
import { useNavigate } from 'react-router-dom';

interface StatsCardProps {
  bookmarks: Bookmark[];
  totalQuestions?: number;
  totalExams?: number;
  correctAnswers?: number;
  averageScore?: number;
}

const StatsCard: React.FC<StatsCardProps> = ({
  bookmarks = [],
  totalQuestions = 0,
  totalExams = 0,
  correctAnswers = 0,
  averageScore = 0
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Animation variants
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  const MotionPaper = motion(Paper);

  // Calculate accuracy percentage
  const accuracy = totalQuestions > 0
    ? Math.round((correctAnswers / totalQuestions) * 100)
    : 0;

  return (
    <MotionPaper
      variants={itemVariants}
      sx={{
        p: isMobile ? 2 : 3,
        mb: isMobile ? 2 : 3,
        borderRadius: 2,
        maxWidth: '100%',
        border: `1px solid ${theme.palette.divider}`
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Avatar
          sx={{
            bgcolor: theme.palette.secondary.main,
            mr: 1.5,
            width: 36,
            height: 36
          }}
        >
          <TrendingUpIcon fontSize="small" />
        </Avatar>
        <Typography variant={isMobile ? "subtitle1" : "h6"} fontWeight="bold">
          Your Stats
        </Typography>
      </Box>

      <Box sx={{ mb: 3 }}>
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: 'repeat(2, 1fr)',
            gap: isMobile ? 1.5 : 2
          }}
        >
          <Card
            sx={{
              bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.02)' : 'rgba(255,255,255,0.03)',
              boxShadow: 'none',
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 1.5
            }}
          >
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <QuestionIcon fontSize="small" color="primary" sx={{ mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">
                  Questions
                </Typography>
              </Box>
              <Typography variant="h6" fontWeight="bold">
                {totalQuestions}
              </Typography>
            </CardContent>
          </Card>
          <Card
            sx={{
              bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.02)' : 'rgba(255,255,255,0.03)',
              boxShadow: 'none',
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 1.5
            }}
          >
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <AssessmentIcon fontSize="small" color="secondary" sx={{ mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">
                  Exams
                </Typography>
              </Box>
              <Typography variant="h6" fontWeight="bold">
                {totalExams}
              </Typography>
            </CardContent>
          </Card>
        </Box>
      </Box>

      {totalQuestions > 0 && (
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
            <Typography variant="body2" color="text.secondary">
              Accuracy
            </Typography>
            <Typography variant="body2" fontWeight="medium">
              {accuracy}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={accuracy}
            sx={{
              height: 8,
              borderRadius: 4,
              bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.05)' : 'rgba(255,255,255,0.05)',
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
              }
            }}
          />
        </Box>
      )}

      <Divider sx={{ my: 2 }} />

      <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
        Bookmarked Questions
      </Typography>

      {bookmarks.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 2 }}>
          <BookmarkIcon sx={{ fontSize: 40, color: 'text.secondary', opacity: 0.5, mb: 1 }} />
          <Typography variant="body2" color="text.secondary">
            No bookmarked questions yet.
          </Typography>
        </Box>
      ) : (
        <Box sx={{ mt: 1 }}>
          {bookmarks.slice(0, 3).map((bookmark) => (
            <Box
              key={bookmark.id}
              sx={{
                p: 1.5,
                mb: 1,
                borderRadius: 1.5,
                border: `1px solid ${theme.palette.divider}`,
                '&:hover': {
                  bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.02)' : 'rgba(255,255,255,0.02)',
                },
                cursor: 'pointer'
              }}
              onClick={() => navigate(`/questions/${bookmark.question_id}`)}
            >
              <Typography
                variant="body2"
                sx={{
                  mb: 0.5,
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}
              >
                {bookmark.question_text}
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Chip
                  size="small"
                  label={bookmark.difficulty}
                  color={
                    bookmark.difficulty === 'easy' ? 'success' :
                    bookmark.difficulty === 'medium' ? 'warning' : 'error'
                  }
                  sx={{ height: 20, fontSize: '0.65rem' }}
                />
                <Typography variant="caption" color="text.secondary">
                  {new Date(bookmark.created_at).toLocaleDateString()}
                </Typography>
              </Box>
            </Box>
          ))}

          {bookmarks.length > 3 && (
            <Button
              variant="text"
              fullWidth
              size="small"
              onClick={() => navigate('/mcq/bookmarks')}
              sx={{ mt: 1 }}
            >
              View All ({bookmarks.length})
            </Button>
          )}
        </Box>
      )}
    </MotionPaper>
  );
};

export default StatsCard;
