import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  LinearProgress,
  Chip,
  Avatar,
  Card,
  CardContent,
  Divider,
  useTheme,
  Tooltip,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  EmojiEvents as TrophyIcon,
  LocalFireDepartment as StreakIcon,
  Stars as StarsIcon,
  Whatshot as HotstreakIcon,
  School as SchoolIcon,
  Timeline as LevelIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { getGamificationProfile, GamificationProfile as GamificationProfileType } from '../../api/gamification';
import { motion } from 'framer-motion';

interface GamificationProfileProps {
  userId?: number;
}

const GamificationProfile: React.FC<GamificationProfileProps> = ({ userId }) => {
  const theme = useTheme();

  const { data: profile, isLoading, error } = useQuery({
    queryKey: ['gamificationProfile', userId],
    queryFn: () => getGamificationProfile(userId),
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Error loading gamification profile
      </Alert>
    );
  }

  if (!profile) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        No gamification data available
      </Alert>
    );
  }

  // Calculate progress to next level
  const calculateProgress = () => {
    if (!profile.next_level || !profile.points_to_next_level) {
      return 100; // Max level reached
    }

    const currentLevelPoints = profile.current_level.max_points - profile.current_level.min_points;
    const pointsEarnedInCurrentLevel = profile.total_points - profile.current_level.min_points;
    return Math.round((pointsEarnedInCurrentLevel / currentLevelPoints) * 100);
  };

  const progress = calculateProgress();

  return (
    <Box>
      {/* Level and Points Summary */}
      <Paper
        elevation={2}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
          color: 'white'
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={2}>
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  border: '3px solid white',
                }}
              >
                <LevelIcon sx={{ fontSize: 40 }} />
              </Avatar>
            </Box>
          </Grid>
          <Grid item xs={12} md={10}>
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              Level {profile.current_level.level_number}: {profile.current_level.name}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <StarsIcon sx={{ mr: 1 }} />
              <Typography variant="h6">
                {profile.total_points} Points
              </Typography>
            </Box>
            <Box sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                <Typography variant="body2">
                  Progress to Level {profile.next_level ? profile.next_level.level_number : 'MAX'}
                </Typography>
                <Typography variant="body2" fontWeight="bold">
                  {progress}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={progress}
                sx={{
                  height: 10,
                  borderRadius: 5,
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  '& .MuiLinearProgress-bar': {
                    bgcolor: 'white',
                  },
                }}
              />
              {profile.next_level && profile.points_to_next_level && (
                <Typography variant="body2" sx={{ mt: 0.5 }}>
                  {profile.points_to_next_level} points needed for next level
                </Typography>
              )}
            </Box>
          </Grid>
        </Grid>
      </Paper>

      <Grid container spacing={3}>
        {/* Streak Card */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', borderRadius: 2 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <StreakIcon color="error" sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  Learning Streak
                </Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />
              {profile.streak ? (
                <Box sx={{ textAlign: 'center' }}>
                  <Box
                    sx={{
                      display: 'inline-flex',
                      position: 'relative',
                      justifyContent: 'center',
                      alignItems: 'center',
                      mb: 2,
                    }}
                  >
                    <CircularProgress
                      variant="determinate"
                      value={100}
                      size={100}
                      thickness={5}
                      sx={{ color: theme.palette.error.main }}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant="h4" fontWeight="bold">
                        {profile.streak.current_streak}
                      </Typography>
                    </Box>
                  </Box>
                  <Typography variant="body1" gutterBottom>
                    Current Streak
                  </Typography>
                  <Chip
                    icon={<HotstreakIcon />}
                    label={`Longest: ${profile.streak.longest_streak} days`}
                    color="error"
                    variant="outlined"
                    sx={{ mt: 1 }}
                  />
                </Box>
              ) : (
                <Typography variant="body1" color="text.secondary" align="center">
                  Start learning to build your streak!
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Badges Card */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: '100%', borderRadius: 2 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrophyIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight="bold">
                  Achievements
                </Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />
              {profile.badges.length > 0 ? (
                <Grid container spacing={2}>
                  {profile.badges.map((badge) => (
                    <Grid item key={badge.id} xs={6} sm={4} md={3}>
                      <Tooltip title={badge.description}>
                        <Box
                          component="div"
                          className="badge-hover"
                          sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            textAlign: 'center',
                          }}
                        >
                          <Avatar
                            src={badge.icon_url || undefined}
                            sx={{
                              width: 60,
                              height: 60,
                              mb: 1,
                              bgcolor: theme.palette.primary.main,
                            }}
                          >
                            <SchoolIcon />
                          </Avatar>
                          <Typography variant="body2" fontWeight="medium" noWrap>
                            {badge.name}
                          </Typography>
                        </Box>
                      </Tooltip>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Typography variant="body1" color="text.secondary" align="center">
                  Complete activities to earn badges!
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default GamificationProfile;
