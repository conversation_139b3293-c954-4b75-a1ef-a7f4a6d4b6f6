import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';

interface MathMarkdownProps {
  children: string;
  className?: string;
}

/**
 * A component that renders markdown content with math expressions
 * using KaTeX for math rendering.
 *
 * Supports both inline math with $ and block math with $$.
 * Also automatically detects and converts common math patterns.
 *
 * Example:
 * Inline math: $E = mc^2$
 * Block math: $$\int_{a}^{b} f(x) dx = F(b) - F(a)$$
 */
const MathMarkdown: React.FC<MathMarkdownProps> = ({ children, className }) => {
  // Handle errors in KaTeX rendering
  const katexOptions = {
    throwOnError: false,
    strict: false,
    trust: true,
    output: 'html',
    macros: {
      // Common math macros
      "\\R": "\\mathbb{R}",
      "\\N": "\\mathbb{N}",
      "\\Z": "\\mathbb{Z}",
      "\\Q": "\\mathbb{Q}",
      "\\C": "\\mathbb{C}",
    }
  };

  // Function to convert LaTeX delimiters to markdown-compatible format
  const preprocessMath = (text: string): string => {
    // Ensure we have a string
    if (!text || typeof text !== 'string') {
      return String(text || '');
    }

    try {
      // Convert \(...\) to $...$ for inline math
      let processed = text.replace(/\\?\\\(/g, '$').replace(/\\?\\\)/g, '$');

      // Convert \[...\] to $$...$$ for display math
      processed = processed.replace(/\\?\\\[/g, '$$').replace(/\\?\\\]/g, '$$');

      return processed;
    } catch (error) {
      console.error('Error in preprocessMath:', error);
      return text;
    }
  };

  // Process content to ensure proper line breaks and math formatting
  const preprocessedContent = preprocessMath(children);

  // Ensure we have a string before further processing
  if (typeof preprocessedContent !== 'string') {
    console.error('preprocessMath did not return a string:', preprocessedContent);
    return <div className={className}>{children}</div>;
  }

  const processedContent = preprocessedContent
    // Replace single newlines with two newlines for proper markdown paragraphs
    .replace(/(?<!\n)\n(?!\n)/g, '\n\n')
    // Ensure math blocks have proper spacing
    .replace(/\$\$(.*?)\$\$/gs, (match) => {
      // Add newlines around display math if not already present
      if (!match.startsWith('\n\n')) {
        match = '\n\n' + match;
      }
      if (!match.endsWith('\n\n')) {
        match = match + '\n\n';
      }
      return match;
    });



  return (
    <div className={className}>
      <ReactMarkdown
        remarkPlugins={[remarkMath]}
        rehypePlugins={[[rehypeKatex, katexOptions]]}
        components={{
          // Add custom styling for code blocks
          code: ({ className, children, ...props }) => {
            return (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },
          // Ensure paragraphs have proper spacing
          p: ({ children }) => (
            <p style={{ marginBottom: '1em' }}>{children}</p>
          ),
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

export default MathMarkdown;
