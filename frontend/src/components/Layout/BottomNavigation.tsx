import React from 'react';
import {
  Paper,
  BottomNavigation as Mu<PERSON><PERSON>ottomNavigation,
  BottomNavigationAction,
  useTheme,
  Badge
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Book as BookIcon,
  QuestionAnswer as QuestionIcon,
  Quiz as QuizIcon,
  EmojiEvents as TrophyIcon,
  AutoAwesome as AutoAwesomeIcon,
  ViewCarousel as CardIcon,
  Event as EventIcon,
  People as PeopleIcon,
  AttachMoney as AttachMoneyIcon
} from '@mui/icons-material';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { motion } from 'framer-motion';

const BottomNavigation: React.FC = () => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Get the current path
  const currentPath = location.pathname;

  // Determine which navigation item is active
  const getActiveValue = () => {
    if (user?.role === 'tutor') {
      if (currentPath.startsWith('/tutor/dashboard')) return 0;
      if (currentPath.startsWith('/tutor/sessions')) return 1;
      if (currentPath.startsWith('/tutor/students')) return 2;
      if (currentPath.startsWith('/tutor/earnings')) return 3;
      return 0; // Default to dashboard
    } else if (user?.role === 'admin') {
      if (currentPath.startsWith('/admin/dashboard')) return 0;
      if (currentPath.startsWith('/questions')) return 1;
      return 0; // Default to dashboard
    } else {
      // Student navigation
      if (currentPath.startsWith('/dashboard')) return 0;
      if (currentPath.startsWith('/courses')) return 1;
      if (currentPath.startsWith('/mcq')) return 2;
      if (currentPath.startsWith('/tools')) return 3;
      if (currentPath.startsWith('/flash-cards')) return 4;
      if (currentPath.startsWith('/gamification')) return 5;
      return 0; // Default to dashboard
    }
  };

  return (
    <Paper
      sx={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        borderRadius: '16px 16px 0 0',
        overflow: 'hidden',
        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)',
        height: '64px',
      }}
      elevation={3}
    >
      <MuiBottomNavigation
        showLabels
        value={getActiveValue()}
        onChange={(_, newValue) => {
          if (user?.role === 'tutor') {
            switch(newValue) {
              case 0:
                navigate('/tutor/dashboard');
                break;
              case 1:
                navigate('/tutor/sessions');
                break;
              case 2:
                navigate('/tutor/students');
                break;
              case 3:
                navigate('/tutor/earnings');
                break;
              default:
                navigate('/tutor/dashboard');
            }
          } else if (user?.role === 'admin') {
            switch(newValue) {
              case 0:
                navigate('/admin/dashboard');
                break;
              case 1:
                navigate('/questions');
                break;
              default:
                navigate('/admin/dashboard');
            }
          } else {
            // Student navigation
            switch(newValue) {
              case 0:
                navigate('/dashboard');
                break;
              case 1:
                navigate('/courses');
                break;
              case 2:
                navigate('/mcq');
                break;
              case 3:
                navigate('/tools');
                break;
              case 4:
                navigate('/flash-cards');
                break;
              case 5:
                navigate('/gamification');
                break;
              default:
                navigate('/dashboard');
            }
          }
        }}
        sx={{
          height: '100%',
          '& .MuiBottomNavigationAction-root': {
            color: theme.palette.text.secondary,
            '&.Mui-selected': {
              color: theme.palette.primary.main,
            },
          },
        }}
      >
        <BottomNavigationAction
          label="Dashboard"
          icon={
            <motion.div
              animate={{
                scale: getActiveValue() === 0 ? 1.1 : 1,
                y: getActiveValue() === 0 ? -4 : 0
              }}
              transition={{ type: 'spring', stiffness: 400, damping: 10 }}
            >
              <DashboardIcon />
            </motion.div>
          }
        />
        {user?.role === 'student' && (
          <BottomNavigationAction
            label="Courses"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 1 ? 1.1 : 1,
                  y: getActiveValue() === 1 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <BookIcon />
              </motion.div>
            }
          />
        )}
        {user?.role === 'admin' && (
          <BottomNavigationAction
            label="Questions"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 2 ? 1.1 : 1,
                  y: getActiveValue() === 2 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <QuestionIcon />
              </motion.div>
            }
          />
        )}
        {user?.role === 'student' && (
          <BottomNavigationAction
            label="MCQ"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 3 ? 1.1 : 1,
                  y: getActiveValue() === 3 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <QuizIcon />
              </motion.div>
            }
          />
        )}
        {user?.role === 'tutor' && (
          <BottomNavigationAction
            label="Sessions"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 1 ? 1.1 : 1,
                  y: getActiveValue() === 1 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <EventIcon />
              </motion.div>
            }
          />
        )}
        {user?.role === 'tutor' && (
          <BottomNavigationAction
            label="Students"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 2 ? 1.1 : 1,
                  y: getActiveValue() === 2 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <PeopleIcon />
              </motion.div>
            }
          />
        )}
        {user?.role === 'tutor' && (
          <BottomNavigationAction
            label="Earnings"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 3 ? 1.1 : 1,
                  y: getActiveValue() === 3 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <AttachMoneyIcon />
              </motion.div>
            }
          />
        )}
        {user?.role === 'student' && (
          <BottomNavigationAction
            label="Tools"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 4 ? 1.1 : 1,
                  y: getActiveValue() === 4 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <AutoAwesomeIcon />
              </motion.div>
            }
          />
        )}
        {user?.role === 'student' && (
          <BottomNavigationAction
            label="Cards"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 5 ? 1.1 : 1,
                  y: getActiveValue() === 5 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <CardIcon />
              </motion.div>
            }
          />
        )}
        {user?.role === 'student' && (
          <BottomNavigationAction
            label="Rewards"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 6 ? 1.1 : 1,
                  y: getActiveValue() === 6 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <Badge color="secondary" variant="dot" invisible={false}>
                  <TrophyIcon />
                </Badge>
              </motion.div>
            }
          />
        )}
      </MuiBottomNavigation>
    </Paper>
  );
};

export default BottomNavigation;
