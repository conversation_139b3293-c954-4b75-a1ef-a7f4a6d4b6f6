import React from 'react';
import { Box, Typography, useTheme, useMediaQuery } from '@mui/material';
import { motion } from 'framer-motion';

interface PageHeaderProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({ title, description, icon }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          mb: 3,
          flexDirection: isMobile ? 'column' : 'row',
          textAlign: isMobile ? 'center' : 'left',
        }}
      >
        {icon && (
          <Box
            sx={{
              mr: isMobile ? 0 : 2,
              mb: isMobile ? 1 : 0,
              color: theme.palette.primary.main,
            }}
          >
            {icon}
          </Box>
        )}
        <Box>
          <Typography
            variant={isMobile ? 'h5' : 'h4'}
            component="h1"
            fontWeight="bold"
            gutterBottom={!!description}
          >
            {title}
          </Typography>
          {description && (
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{ maxWidth: '800px' }}
            >
              {description}
            </Typography>
          )}
        </Box>
      </Box>
    </motion.div>
  );
};

export default PageHeader;
