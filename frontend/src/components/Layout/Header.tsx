import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Box,
  useMediaQuery,
  useTheme,
  Avatar,
  Tooltip,
  Badge,
  alpha,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  Menu as MenuIcon,
  AccountCircle,
  School as SchoolIcon,
  Book,
  QuestionAnswer,
  Event,
  Dashboard,
  Person,
  Notifications as NotificationsIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
  People,
  PersonSearch,
  AttachMoney,
  Star,
  Analytics
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import ThemeToggle from '../ThemeToggle';
import { motion } from 'framer-motion';
import MobileMenu from './MobileMenu';
import { useThemeMode } from '../../contexts/ThemeContext';

const Header: React.FC = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { mode, toggleColorMode } = useThemeMode();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleClose();
    setMobileMenuOpen(false);
    navigate('/login');
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Role-specific menu items
  const getMenuItems = () => {
    if (user?.role === 'tutor') {
      return [
        { text: 'Dashboard', icon: <Dashboard />, path: '/tutor/dashboard' },
        { text: 'Profile', icon: <Person />, path: '/tutor/profile' },
        { text: 'Sessions', icon: <Event />, path: '/tutor/sessions' },
        { text: 'Students', icon: <People />, path: '/tutor/students' },
        { text: 'Earnings', icon: <AttachMoney />, path: '/tutor/earnings' },
        { text: 'Reviews', icon: <Star />, path: '/tutor/reviews' },
      ];
    } else if (user?.role === 'admin') {
      return [
        { text: 'Dashboard', icon: <Dashboard />, path: '/admin/dashboard' },
        { text: 'Users', icon: <Person />, path: '/admin/users' },
        { text: 'Schools', icon: <SchoolIcon />, path: '/admin/schools' },
        { text: 'Courses', icon: <Book />, path: '/admin/courses' },
        { text: 'Questions', icon: <QuestionAnswer />, path: '/admin/questions' },
        { text: 'Analytics', icon: <Analytics />, path: '/admin/analytics' },
      ];
    } else {
      // Student menu items
      return [
        { text: 'Dashboard', icon: <Dashboard />, path: '/dashboard' },
        { text: 'Schools', icon: <SchoolIcon />, path: '/schools' },
        { text: 'Departments', icon: <Book />, path: '/departments' },
        { text: 'Courses', icon: <Book />, path: '/courses' },
        { text: 'Sessions', icon: <Event />, path: '/sessions' },
        { text: 'Find Tutors', icon: <PersonSearch />, path: '/tutors' },
      ];
    }
  };

  const menuItems = getMenuItems();



  // Animation variants for the AppBar
  const appBarVariants = {
    hidden: { y: -64 },
    visible: {
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 20
      }
    }
  };

  // Create a motion component for AppBar
  const MotionAppBar = motion.create(AppBar);

  return (
    <>
      <MotionAppBar
        position="sticky"
        elevation={0}
        initial="hidden"
        animate="visible"
        variants={appBarVariants}
        sx={{
          backdropFilter: 'blur(10px)',
          bgcolor: theme.palette.mode === 'light'
            ? 'rgba(255, 255, 255, 0.8)'
            : 'rgba(18, 18, 18, 0.8)',
          color: theme.palette.mode === 'light' ? 'primary.main' : 'white',
          borderBottom: `1px solid ${theme.palette.divider}`
        }}
      >
        <Toolbar>
          {isAuthenticated && isMobile && (
            <IconButton
              size="large"
              edge="start"
              color="inherit"
              aria-label="menu"
              sx={{
                mr: 2,
                borderRadius: 1.5,
                transition: 'all 0.2s ease',
                '&:hover': {
                  background: alpha(theme.palette.primary.main, 0.1),
                  transform: 'scale(1.05)'
                },
                '&:active': {
                  transform: 'scale(0.95)'
                }
              }}
              onClick={toggleMobileMenu}
            >
              <motion.div
                whileTap={{ scale: 0.9 }}
                initial={{ rotate: 0 }}
                animate={{ rotate: mobileMenuOpen ? 180 : 0 }}
                transition={{ duration: 0.3 }}
              >
                <MenuIcon />
              </motion.div>
            </IconButton>
          )}

          <Box
            component={RouterLink}
            to="/"
            sx={{
              display: 'flex',
              alignItems: 'center',
              textDecoration: 'none',
              color: 'inherit',
              flexGrow: 1
            }}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <SchoolIcon sx={{ fontSize: 28, mr: 1, color: 'primary.main' }} />
            </motion.div>
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontWeight: 700,
                background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              CampusPQ
            </Typography>
          </Box>

          {isAuthenticated && !isMobile && (
            <Box sx={{ display: 'flex', gap: 2 }}>
              {menuItems.map((item) => (
                <Button
                  key={item.text}
                  color="inherit"
                  component={RouterLink}
                  to={item.path}
                  startIcon={item.icon}
                >
                  {item.text}
                </Button>
              ))}
            </Box>
          )}

          {/* Theme Toggle removed for debugging */}

          {/* Notifications (for authenticated users) */}
          {isAuthenticated && (
            <Tooltip title="Notifications">
              <IconButton color="inherit" sx={{ ml: 1 }}>
                <Badge badgeContent={3} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>
          )}

          {isAuthenticated ? (
            <div>
              <Tooltip title={user?.full_name || 'Account'}>
                <IconButton
                  size="large"
                  aria-label="account of current user"
                  aria-controls="menu-appbar"
                  aria-haspopup="true"
                  onClick={handleMenu}
                  color="inherit"
                  sx={{ ml: 1 }}
                >
                  {user?.full_name ? (
                    <Avatar
                      sx={{
                        width: 32,
                        height: 32,
                        bgcolor: user.role === 'admin'
                          ? 'error.main'
                          : user.role === 'tutor'
                            ? 'primary.main'
                            : 'success.main'
                      }}
                    >
                      {user.full_name.charAt(0).toUpperCase()}
                    </Avatar>
                  ) : (
                    <AccountCircle />
                  )}
                </IconButton>
              </Tooltip>
              <Menu
                id="menu-appbar"
                anchorEl={anchorEl}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                keepMounted
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                open={Boolean(anchorEl)}
                onClose={handleClose}
                PaperProps={{
                  elevation: 3,
                  sx: {
                    borderRadius: 2,
                    minWidth: 180,
                    overflow: 'visible',
                    mt: 1.5,
                    '&:before': {
                      content: '""',
                      display: 'block',
                      position: 'absolute',
                      top: 0,
                      right: 14,
                      width: 10,
                      height: 10,
                      bgcolor: 'background.paper',
                      transform: 'translateY(-50%) rotate(45deg)',
                      zIndex: 0,
                    },
                  },
                }}
              >
                <Box sx={{ px: 2, py: 1.5 }}>
                  <Typography variant="subtitle1" noWrap>
                    {user?.full_name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" noWrap>
                    {user?.email}
                  </Typography>
                </Box>
                <Divider />
                <MenuItem component={RouterLink} to="/profile" onClick={handleClose}>
                  <ListItemIcon>
                    <Person fontSize="small" />
                  </ListItemIcon>
                  Profile
                </MenuItem>
                <MenuItem onClick={handleLogout}>
                  <ListItemIcon>
                    <AccountCircle fontSize="small" />
                  </ListItemIcon>
                  Logout
                </MenuItem>
              </Menu>
            </div>
          ) : (
            <Box sx={{ display: 'flex', gap: 1, ml: 1 }}>
              <Button
                color="inherit"
                component={RouterLink}
                to="/login"
                sx={{
                  borderRadius: 2,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                  }
                }}
              >
                Login
              </Button>
              <Button
                variant="contained"
                color="secondary"
                component={RouterLink}
                to="/register"
                sx={{
                  borderRadius: 2,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                  }
                }}
              >
                Register
              </Button>
            </Box>
          )}
        </Toolbar>
      </MotionAppBar>

      <MobileMenu
        open={mobileMenuOpen}
        onClose={() => setMobileMenuOpen(false)}
        menuItems={menuItems}
        onLogout={handleLogout}
      />
    </>
  );
};

export default Header;
