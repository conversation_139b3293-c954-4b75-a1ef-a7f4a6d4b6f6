import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  IconButton,
  Avatar,
  useTheme,
  alpha,
  Button,
  Collapse
} from '@mui/material';
import {
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Logout as LogoutIcon,
  Person as PersonIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon
} from '@mui/icons-material';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { useThemeMode } from '../../contexts/ThemeContext';

interface MobileMenuProps {
  open: boolean;
  onClose: () => void;
  menuItems: Array<{
    text: string;
    icon: React.ReactNode;
    path: string;
  }>;
  onLogout: () => void;
}

const MobileMenu: React.FC<MobileMenuProps> = ({
  open,
  onClose,
  menuItems,
  onLogout
}) => {
  const theme = useTheme();
  const location = useLocation();
  const { user } = useAuth();
  const { mode, toggleColorMode } = useThemeMode();
  const isDarkMode = mode === 'dark';
  const [settingsOpen, setSettingsOpen] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.07,
        delayChildren: 0.2
      }
    },
    exit: {
      opacity: 0,
      transition: {
        staggerChildren: 0.05,
        staggerDirection: -1
      }
    }
  };

  const itemVariants = {
    hidden: { x: -20, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 24
      }
    },
    exit: {
      x: -20,
      opacity: 0
    }
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { duration: 0.3 }
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.3 }
    }
  };

  const drawerVariants = {
    hidden: { x: '-100%' },
    visible: {
      x: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      x: '-100%',
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    }
  };

  return (
    <AnimatePresence>
      {open && (
        <>
          <motion.div
            key="backdrop"
            variants={backdropVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: alpha(theme.palette.common.black, 0.5),
              backdropFilter: 'blur(4px)',
              zIndex: 1200
            }}
            onClick={onClose}
          />

          <motion.div
            key="drawer"
            variants={drawerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              bottom: 0,
              width: '85%',
              maxWidth: '320px',
              background: theme.palette.background.paper,
              zIndex: 1300,
              borderRadius: '0 16px 16px 0',
              boxShadow: theme.shadows[10],
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            {/* Header */}
            <Box
              sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                borderBottom: `1px solid ${theme.palette.divider}`,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                color: 'white'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar
                  sx={{
                    width: 40,
                    height: 40,
                    bgcolor: 'white',
                    color: theme.palette.primary.main,
                    fontWeight: 'bold',
                    mr: 1.5
                  }}
                >
                  {user?.full_name?.charAt(0).toUpperCase() || 'C'}
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {user?.full_name || 'CampusPQ'}
                  </Typography>
                  <Typography variant="caption">
                    {user?.email || 'Welcome!'}
                  </Typography>
                </Box>
              </Box>
              <IconButton
                onClick={onClose}
                sx={{
                  color: 'white',
                  '&:hover': {
                    background: 'rgba(255, 255, 255, 0.2)'
                  }
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>

            {/* Menu Items */}
            <Box sx={{ overflow: 'auto', flex: 1, py: 1 }}>
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                <List sx={{ px: 1 }}>
                  {menuItems.map((item, index) => (
                    <motion.div key={item.text} variants={itemVariants} custom={index}>
                      <ListItem
                        button
                        component={RouterLink}
                        to={item.path}
                        onClick={onClose}
                        sx={{
                          borderRadius: 2,
                          mb: 0.5,
                          py: 1.5,
                          position: 'relative',
                          overflow: 'hidden',
                          '&::before': {
                            content: '""',
                            position: 'absolute',
                            left: 0,
                            top: 0,
                            bottom: 0,
                            width: 4,
                            bgcolor: location.pathname === item.path
                              ? theme.palette.primary.main
                              : 'transparent',
                            borderRadius: '0 4px 4px 0',
                            transition: 'all 0.2s ease'
                          },
                          bgcolor: location.pathname === item.path
                            ? alpha(theme.palette.primary.main, 0.1)
                            : 'transparent',
                          '&:hover': {
                            bgcolor: alpha(theme.palette.primary.main, 0.05),
                            '&::before': {
                              width: 4,
                              bgcolor: theme.palette.primary.main,
                              opacity: 0.5
                            }
                          }
                        }}
                      >
                        <ListItemIcon
                          sx={{
                            color: location.pathname === item.path
                              ? theme.palette.primary.main
                              : theme.palette.text.secondary,
                            minWidth: 40
                          }}
                        >
                          {item.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={item.text}
                          primaryTypographyProps={{
                            fontWeight: location.pathname === item.path ? 'bold' : 'medium'
                          }}
                        />
                      </ListItem>
                    </motion.div>
                  ))}
                </List>
              </motion.div>
            </Box>

            {/* Footer */}
            <Box sx={{ p: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
              <List sx={{ p: 0 }}>
                <ListItem
                  button
                  onClick={() => setSettingsOpen(!settingsOpen)}
                  sx={{
                    borderRadius: 2,
                    transition: 'all 0.2s ease',
                    bgcolor: settingsOpen ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                    '&:hover': {
                      bgcolor: settingsOpen
                        ? alpha(theme.palette.primary.main, 0.15)
                        : alpha(theme.palette.primary.main, 0.05),
                    }
                  }}
                >
                  <ListItemIcon sx={{
                    color: settingsOpen ? theme.palette.primary.main : theme.palette.text.secondary,
                    transition: 'all 0.2s ease'
                  }}>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Settings"
                    primaryTypographyProps={{
                      fontWeight: settingsOpen ? 'bold' : 'medium',
                      transition: 'all 0.2s ease'
                    }}
                  />
                  <motion.div
                    animate={{ rotate: settingsOpen ? 180 : 0 }}
                    transition={{ duration: 0.3, type: 'spring', stiffness: 300 }}
                  >
                    {settingsOpen ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </motion.div>
                </ListItem>

                <Collapse in={settingsOpen} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    <ListItem
                      button
                      component={RouterLink}
                      to="/profile"
                      onClick={onClose}
                      sx={{ pl: 4, borderRadius: 2 }}
                    >
                      <ListItemIcon>
                        <PersonIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary="Profile" />
                    </ListItem>

                    <ListItem
                      button
                      onClick={toggleColorMode}
                      sx={{
                        pl: 4,
                        borderRadius: 2,
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                    >
                      <motion.div
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ duration: 0.3 }}
                        style={{ position: 'absolute', left: 16 }}
                      >
                        <ListItemIcon>
                          {isDarkMode ?
                            <LightModeIcon fontSize="small" style={{ color: theme.palette.warning.light }} /> :
                            <DarkModeIcon fontSize="small" style={{ color: theme.palette.primary.main }} />
                          }
                        </ListItemIcon>
                      </motion.div>
                      <ListItemText
                        primary={
                          <motion.span
                            key={isDarkMode ? "dark" : "light"}
                            initial={{ y: 10, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            exit={{ y: -10, opacity: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            {isDarkMode ? "Light Mode" : "Dark Mode"}
                          </motion.span>
                        }
                      />
                    </ListItem>
                  </List>
                </Collapse>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <ListItem
                    button
                    onClick={onLogout}
                    sx={{
                      borderRadius: 2,
                      color: theme.palette.error.main,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: alpha(theme.palette.error.main, 0.1)
                      }
                    }}
                  >
                    <ListItemIcon sx={{ color: 'inherit' }}>
                      <motion.div
                        whileHover={{ rotate: [0, -20, 20, -20, 0] }}
                        transition={{ duration: 0.5 }}
                      >
                        <LogoutIcon />
                      </motion.div>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="body1" fontWeight="medium">
                          Logout
                        </Typography>
                      }
                    />
                  </ListItem>
                </motion.div>
              </List>
            </Box>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default MobileMenu;
