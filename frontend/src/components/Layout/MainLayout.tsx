import React from 'react';
import { Box, Container, CssBaseline, useTheme } from '@mui/material';
import Header from './Header';
import PageTransition from '../PageTransition';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        bgcolor: theme.palette.background.default,
      }}
    >
      <CssBaseline />
      <Header />
      <Container
        component="main"
        sx={{
          mt: 4,
          mb: 4,
          flex: 1,
          position: 'relative',
          zIndex: 1,
        }}
      >
        {children}
      </Container>
    </Box>
  );
};

export default MainLayout;
