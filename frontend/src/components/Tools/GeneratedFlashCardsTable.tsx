import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Chip,
  IconButton,
  CircularProgress,
  Alert,
  Button,
  useTheme,
  Grid,
  Card,
  CardContent,
  CardActions,
  useMediaQuery,
  Tooltip,
  Badge,
  TablePagination,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  PlayArrow as PlayArrowIcon,
  School as SchoolIcon,
  CalendarToday as CalendarIcon,
  Description as DescriptionIcon,
  ViewCarousel as CardIcon,
  ViewList as TableIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { getFlashCardGenerationJobs, getFlashCardGenerationStatus, deleteFlashCardGenerationJob } from '../../api/studentTools';
import { ProcessingStatus } from '../../types/studentTools';
import { formatDate, formatRelativeTime } from '../../utils/formatters';
import { motion } from 'framer-motion';

interface GeneratedFlashCardsTableProps {
  onViewFlashCards?: (jobId: number) => void;
}

const GeneratedFlashCardsTable: React.FC<GeneratedFlashCardsTableProps> = ({
  onViewFlashCards
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>(isMobile ? 'grid' : 'table');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [jobToDelete, setJobToDelete] = useState<any>(null);

  // Fetch flash card generation jobs
  const {
    data: jobs = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['flashCardGenerationJobs'],
    queryFn: () => getFlashCardGenerationJobs(),
    staleTime: 30000, // 30 seconds
    refetchInterval: 10000 // Refetch every 10 seconds to update status
  });

  // Delete flash card generation job mutation
  const deleteMutation = useMutation({
    mutationFn: deleteFlashCardGenerationJob,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['flashCardGenerationJobs'] });
      setDeleteDialogOpen(false);
      setJobToDelete(null);
    },
    onError: (error) => {
      console.error('Failed to delete flash card generation job:', error);
    }
  });

  // Listen for refresh events
  useEffect(() => {
    const handleRefresh = () => {
      refetch();
    };

    window.addEventListener('refreshNotes', handleRefresh);
    return () => {
      window.removeEventListener('refreshNotes', handleRefresh);
    };
  }, [refetch]);

  // Handle page change
  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Effect to update view mode based on screen size
  useEffect(() => {
    setViewMode(isMobile ? 'grid' : 'table');
  }, [isMobile]);

  // Handle view flash cards
  const handleViewFlashCards = async (jobId: number) => {
    try {
      console.log(`Checking status for flash card job ID: ${jobId}`);

      // Get the job status to ensure it's completed
      const status = await getFlashCardGenerationStatus(jobId);
      console.log(`Job status for ID ${jobId}:`, status);

      if (status.status === ProcessingStatus.COMPLETED && status.generated_card_ids && status.generated_card_ids.length > 0) {
        console.log(`Job ${jobId} is completed with ${status.generated_card_ids.length} cards. Navigating to study view.`);

        // Use the provided callback or navigate directly
        if (onViewFlashCards) {
          console.log(`Using onViewFlashCards callback for job ID: ${jobId}`);
          onViewFlashCards(jobId);
        } else {
          console.log(`Navigating to /flash-cards/study/note-generated?jobId=${jobId}`);
          navigate(`/flash-cards/study/note-generated?jobId=${jobId}`);
        }
      } else if (status.status === ProcessingStatus.PENDING || status.status === ProcessingStatus.PROCESSING) {
        console.log(`Job ${jobId} is still processing (${status.status})`);
        alert('This job is still processing. Please wait for it to complete before viewing the flash cards.');
      } else {
        console.log(`Job ${jobId} has no cards or failed: ${status.status}`);
        alert('No flash cards were generated for this job. Please try generating flash cards again.');
      }
    } catch (error) {
      console.error('Error checking job status:', error);
      alert('Error checking job status. Please try again later.');
    }
  };

  // Toggle view mode
  const toggleViewMode = () => {
    setViewMode(viewMode === 'table' ? 'grid' : 'table');
  };

  // Handle delete button click
  const handleDeleteClick = (job: any) => {
    setJobToDelete(job);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (jobToDelete) {
      deleteMutation.mutate(jobToDelete.id);
    }
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setJobToDelete(null);
  };

  // Get status chip
  const getStatusChip = (status: ProcessingStatus, progress: number) => {
    switch (status) {
      case ProcessingStatus.PENDING:
        return <Chip
          label="Pending"
          size="small"
          color="default"
        />;
      case ProcessingStatus.PROCESSING:
      case ProcessingStatus.GENERATING:
      case ProcessingStatus.EMBEDDING:
        return <Chip
          label={`${status} ${progress}%`}
          size="small"
          color="primary"
          icon={<CircularProgress size={16} color="inherit" />}
        />;
      case ProcessingStatus.COMPLETED:
        return <Chip
          label="Completed"
          size="small"
          color="success"
        />;
      case ProcessingStatus.FAILED:
        return <Chip
          label="Failed"
          size="small"
          color="error"
        />;
      default:
        return <Chip
          label={status}
          size="small"
          color="default"
        />;
    }
  };

  // Empty rows
  const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - jobs.length) : 0;

  // Calculate total pages
  const totalPages = Math.ceil(jobs.length / rowsPerPage);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  // Generate a color based on the job ID
  const generateColor = (jobId: number) => {
    const hue = (jobId * 137) % 360; // Use a prime number to get good distribution
    return theme.palette.mode === 'dark'
      ? `hsl(${hue}, 70%, 25%)`
      : `hsl(${hue}, 70%, 90%)`;
  };

  const MotionCard = motion.create(Card);

  return (
    <Box>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: { xs: 2, sm: 3 },
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 2, sm: 0 }
      }}>
        <Typography
          variant="h6"
          sx={{
            fontSize: { xs: '1.1rem', sm: '1.25rem' },
            fontWeight: 600,
            color: 'text.primary'
          }}
        >
          Flash Card Sets
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {!isMobile && (
            <Tooltip title={viewMode === 'table' ? 'Switch to Grid View' : 'Switch to Table View'}>
              <IconButton onClick={toggleViewMode} size="small">
                {viewMode === 'table' ? <CardIcon /> : <TableIcon />}
              </IconButton>
            </Tooltip>
          )}
          <Tooltip title="Refresh">
            <IconButton onClick={() => refetch()} size="small">
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {error ? (
        <Alert severity="error" sx={{ mb: 2 }}>
          Error loading flash card sets
        </Alert>
      ) : isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : jobs.length === 0 ? (
        <Alert severity="info">
          No flash card sets found. Generate flash cards from your notes to see them here.
        </Alert>
      ) : viewMode === 'grid' ? (
        // Grid View
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={2}>
            {jobs
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((job) => (
                <Grid size={{ xs: 12, sm: 6, md: 4 }} key={job.id}>
                  <MotionCard
                    variants={itemVariants}
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      borderRadius: 2,
                      overflow: 'hidden',
                      backgroundColor: job.status === ProcessingStatus.COMPLETED ?
                        generateColor(job.id) :
                        theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)',
                      transition: 'transform 0.2s, box-shadow 0.2s',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: '0 8px 16px rgba(0,0,0,0.15)'
                      }
                    }}
                  >
                    <CardContent sx={{ flexGrow: 1, p: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Badge
                          badgeContent={job.status === ProcessingStatus.COMPLETED && job.generated_card_ids ?
                            job.generated_card_ids.length : job.card_count}
                          color="primary"
                          max={999}
                        >
                          <Typography variant="h6" component="div">
                            {job.title || `Set #${job.id}`}
                          </Typography>
                        </Badge>
                        {getStatusChip(job.status, job.progress_percentage)}
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <CalendarIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          {formatRelativeTime(job.created_at)}
                        </Typography>
                      </Box>

                      {job.course_id && !isNaN(Number(job.course_id)) && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <SchoolIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            Course {job.course_id}
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                    <CardActions sx={{ p: 2, pt: 0, display: 'flex', gap: 1 }}>
                      {job.status === ProcessingStatus.COMPLETED && job.generated_card_ids && job.generated_card_ids.length > 0 ? (
                        <Button
                          variant="contained"
                          size="small"
                          startIcon={<PlayArrowIcon />}
                          onClick={() => handleViewFlashCards(job.id)}
                          sx={{ flexGrow: 1 }}
                        >
                          Study Cards
                        </Button>
                      ) : (
                        <Button
                          variant="outlined"
                          size="small"
                          disabled
                          sx={{ flexGrow: 1 }}
                        >
                          {job.status === ProcessingStatus.FAILED ? 'Failed' : 'Processing...'}
                        </Button>
                      )}
                      <Tooltip title="Delete Flash Card Set">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteClick(job)}
                          disabled={deleteMutation.isPending}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </CardActions>
                  </MotionCard>
                </Grid>
              ))}
          </Grid>

          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <TablePagination
                rowsPerPageOptions={[6, 12, 24]}
                component="div"
                count={jobs.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </Box>
          )}
        </motion.div>
      ) : (
        // Table View
        <Paper
          sx={{
            width: '100%',
            mb: 2,
            borderRadius: 2,
            overflow: 'hidden',
            border: 1,
            borderColor: 'divider'
          }}
        >
          <TableContainer>
            <Table
              sx={{
                minWidth: { xs: 'auto', sm: 650 }
              }}
              aria-label="flash card generation jobs table"
            >
              <TableHead>
                <TableRow sx={{ backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)' }}>
                  <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}>ID</TableCell>
                  <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}>Created</TableCell>
                  <TableCell sx={{ display: { xs: 'none', md: 'table-cell' } }}>Course</TableCell>
                  <TableCell>Cards</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {jobs
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((job) => (
                    <TableRow
                      key={job.id}
                      sx={{
                        '&:last-child td, &:last-child th': { border: 0 },
                        '&:hover': { backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)' }
                      }}
                    >
                      <TableCell
                        component="th"
                        scope="row"
                        sx={{ display: { xs: 'none', sm: 'table-cell' } }}
                      >
                        {job.id}
                      </TableCell>
                      <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}>
                        <Typography variant="body2" sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                          {formatDate(job.created_at)}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ display: { xs: 'none', md: 'table-cell' } }}>
                        {job.course_id && !isNaN(Number(job.course_id)) ? (
                          <Chip
                            icon={<SchoolIcon fontSize="small" />}
                            label={`Course ${job.course_id}`}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: { xs: '0.75rem', sm: '0.8125rem' } }}
                          />
                        ) : (
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                          >
                            Auto-detected
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                          {job.status === ProcessingStatus.COMPLETED && job.generated_card_ids ? (
                            `${job.generated_card_ids.length} cards`
                          ) : (
                            `${job.card_count} (requested)`
                          )}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={job.status === ProcessingStatus.PROCESSING ?
                            `${job.status} ${job.progress_percentage}%` :
                            job.status
                          }
                          size="small"
                          color={
                            job.status === ProcessingStatus.COMPLETED ? 'success' :
                            job.status === ProcessingStatus.FAILED ? 'error' :
                            job.status === ProcessingStatus.PROCESSING ? 'primary' : 'default'
                          }
                          sx={{ fontSize: { xs: '0.75rem', sm: '0.8125rem' } }}
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end', alignItems: 'center' }}>
                          {job.status === ProcessingStatus.COMPLETED && job.generated_card_ids && job.generated_card_ids.length > 0 ? (
                            <Button
                              variant="outlined"
                              size="small"
                              startIcon={<PlayArrowIcon sx={{ fontSize: { xs: 16, sm: 18 } }} />}
                              onClick={() => handleViewFlashCards(job.id)}
                              sx={{
                                fontSize: { xs: '0.75rem', sm: '0.8125rem' },
                                px: { xs: 1, sm: 1.5 },
                                py: { xs: 0.5, sm: 0.75 },
                                minWidth: { xs: 'auto', sm: 'auto' }
                              }}
                            >
                              Study
                            </Button>
                          ) : (
                            <Button
                              variant="outlined"
                              size="small"
                              disabled
                              sx={{
                                fontSize: { xs: '0.75rem', sm: '0.8125rem' },
                                px: { xs: 1, sm: 1.5 },
                                py: { xs: 0.5, sm: 0.75 },
                                minWidth: { xs: 'auto', sm: 'auto' }
                              }}
                            >
                              {job.status === ProcessingStatus.FAILED ? 'Failed' : 'Processing'}
                            </Button>
                          )}
                          <Tooltip title="Delete Flash Card Set">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeleteClick(job)}
                              disabled={deleteMutation.isPending}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                {emptyRows > 0 && (
                  <TableRow style={{ height: 53 * emptyRows }}>
                    <TableCell colSpan={6} />
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={jobs.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Delete Flash Card Set
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete this flash card set? This will permanently delete all {jobToDelete?.generated_card_ids?.length || jobToDelete?.card_count || 0} flash cards in this set. This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GeneratedFlashCardsTable;
