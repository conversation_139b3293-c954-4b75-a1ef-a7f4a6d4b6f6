import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  useTheme,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { getStudentNotes, deleteNote } from '../../api/studentTools';
import { ProcessingStatus, StudentNote } from '../../types/studentTools';
import { formatFileSize, formatDate } from '../../utils/formatters';

const StudentNotesTable: React.FC = () => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [noteToDelete, setNoteToDelete] = useState<StudentNote | null>(null);

  // Fetch student notes
  const {
    data: notes = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['studentNotes'],
    queryFn: () => getStudentNotes(),
    staleTime: 30000, // 30 seconds
    refetchInterval: 10000 // Refetch every 10 seconds to update status
  });

  // Listen for refresh events
  useEffect(() => {
    const handleRefresh = () => {
      console.log('Refreshing notes list...');
      refetch();
    };

    // Add event listener
    window.addEventListener('refreshNotes', handleRefresh);

    // Clean up
    return () => {
      window.removeEventListener('refreshNotes', handleRefresh);
    };
  }, [refetch]);

  // Delete note mutation
  const deleteMutation = useMutation({
    mutationFn: deleteNote,
    onSuccess: () => {
      // Refresh the notes list
      queryClient.invalidateQueries({ queryKey: ['studentNotes'] });
      setDeleteDialogOpen(false);
      setNoteToDelete(null);
    },
    onError: (error) => {
      console.error('Failed to delete note:', error);
      // You could add a toast notification here
    }
  });

  // Handle delete button click
  const handleDeleteClick = (note: StudentNote) => {
    setNoteToDelete(note);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (noteToDelete) {
      deleteMutation.mutate(noteToDelete.id);
    }
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setNoteToDelete(null);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Get status color
  const getStatusColor = (status: ProcessingStatus) => {
    switch (status) {
      case ProcessingStatus.PENDING:
      case ProcessingStatus.UPLOADING:
        return theme.palette.info.main;
      case ProcessingStatus.PROCESSING:
      case ProcessingStatus.EMBEDDING:
      case ProcessingStatus.GENERATING:
        return theme.palette.warning.main;
      case ProcessingStatus.COMPLETED:
        return theme.palette.success.main;
      case ProcessingStatus.FAILED:
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  // Get status icon
  const getStatusIcon = (status: ProcessingStatus) => {
    switch (status) {
      case ProcessingStatus.PENDING:
      case ProcessingStatus.UPLOADING:
      case ProcessingStatus.PROCESSING:
      case ProcessingStatus.EMBEDDING:
      case ProcessingStatus.GENERATING:
        return <CircularProgress size={16} />;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load your notes. Please try again later.
      </Alert>
    );
  }

  if (notes.length === 0) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        You don't have any uploaded notes yet. Please upload some notes first.
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <Tooltip title="Refresh notes">
          <IconButton onClick={() => refetch()}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      <TableContainer component={Paper} variant="outlined">
        <Table sx={{ minWidth: 650 }} aria-label="notes table">
          <TableHead>
            <TableRow>
              <TableCell>File Name</TableCell>
              <TableCell>Size</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Course</TableCell>
              <TableCell>Uploaded</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {notes
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((note) => (
                <TableRow key={note.id}>
                  <TableCell component="th" scope="row">
                    {note.file_name}
                  </TableCell>
                  <TableCell>{formatFileSize(note.file_size)}</TableCell>
                  <TableCell>
                    <Chip
                      icon={getStatusIcon(note.status)}
                      label={note.status}
                      size="small"
                      sx={{
                        backgroundColor: getStatusColor(note.status),
                        color: 'white'
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    {note.detected_course_id ? (
                      <Typography variant="body2">
                        Course ID: {note.detected_course_id}
                      </Typography>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        Not detected
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>{formatDate(note.created_at)}</TableCell>
                  <TableCell>
                    <Tooltip title="View details">
                      <IconButton size="small">
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete note">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDeleteClick(note)}
                        disabled={deleteMutation.isPending}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={notes.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Delete Note
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete "{noteToDelete?.file_name}"?
            This action cannot be undone. Your generated MCQs, flash cards, and summaries will be preserved.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentNotesTable;
