import React, { useState, useRef } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  LinearProgress,
  Alert,
  Chip,
  useTheme,
  TextField
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  PictureAsPdf as PdfIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Pending as PendingIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

import { uploadNotes } from '../../api/studentTools';
import { ProcessingStatus } from '../../types/studentTools';

const MAX_FILES = 3;
const MAX_FILE_SIZE = 30 * 1024 * 1024; // 30MB

const PDFUploadTool: React.FC = () => {
  const theme = useTheme();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState<boolean>(false);
  const [uploadResults, setUploadResults] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [courseName, setCourseName] = useState<string>('');

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    // Reset error
    setError(null);

    // Check if adding these files would exceed the limit
    if (selectedFiles.length + files.length > MAX_FILES) {
      setError(`You can only upload a maximum of ${MAX_FILES} files at once.`);
      return;
    }

    // Check file types and sizes
    const newFiles: File[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // Check file type
      if (!file.type.includes('pdf')) {
        setError('Only PDF files are allowed.');
        return;
      }

      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        setError(`File "${file.name}" exceeds the maximum size of 30MB.`);
        return;
      }

      newFiles.push(file);
    }

    setSelectedFiles([...selectedFiles, ...newFiles]);

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemoveFile = (index: number) => {
    const newFiles = [...selectedFiles];
    newFiles.splice(index, 1);
    setSelectedFiles(newFiles);
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      setError('Please select at least one file to upload.');
      return;
    }

    setUploadProgress(true);
    setError(null);

    try {
      const formData = new FormData();

      // Important: Make sure we're using the correct field name expected by the backend
      // The backend expects 'files' as the field name
      selectedFiles.forEach(file => {
        formData.append('files', file);
      });

      // Add the course name if available
      if (courseName.trim()) {
        formData.append('course_name', courseName.trim());
      }

      console.log('Uploading files:', selectedFiles.map(f => f.name));
      console.log('Course name:', courseName);

      // Debug FormData contents
      const formDataEntries = Array.from(formData.entries());
      console.log('FormData entries:', formDataEntries.map(entry => {
        if (entry[1] instanceof File) {
          return `${entry[0]}: File(${(entry[1] as File).name}, ${(entry[1] as File).type}, ${(entry[1] as File).size} bytes)`;
        }
        return `${entry[0]}: ${entry[1]}`;
      }));

      // Call the API to upload the files
      const results = await uploadNotes(formData);
      console.log('Upload results:', results);

      setUploadResults(results);

      // Clear selected files if all uploads were successful
      const allSuccessful = results.every(result => result.status !== ProcessingStatus.FAILED);
      if (allSuccessful) {
        setSelectedFiles([]);

        // Trigger a refresh of the notes list
        // This will update the My Notes tab with the newly uploaded files
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('refreshNotes'));
        }, 1000);
      }
    } catch (err: any) {
      console.error('Upload error:', err);
      if (err.response && err.response.data) {
        setError(`Upload failed: ${err.response.data.detail || JSON.stringify(err.response.data)}`);
      } else {
        setError(err.message || 'An error occurred during upload. Please try again.');
      }
    } finally {
      setUploadProgress(false);
    }
  };

  const getStatusChip = (status: ProcessingStatus) => {
    switch (status) {
      case ProcessingStatus.PENDING:
        return <Chip
          icon={<PendingIcon />}
          label="Pending"
          size="small"
          color="default"
        />;
      case ProcessingStatus.UPLOADING:
        return <Chip
          icon={<CloudUploadIcon />}
          label="Uploading"
          size="small"
          color="primary"
        />;
      case ProcessingStatus.PROCESSING:
        return <Chip
          icon={<PendingIcon />}
          label="Processing"
          size="small"
          color="secondary"
        />;
      case ProcessingStatus.EMBEDDING:
        return <Chip
          icon={<PendingIcon />}
          label="Creating Embeddings"
          size="small"
          color="info"
        />;
      case ProcessingStatus.COMPLETED:
        return <Chip
          icon={<CheckCircleIcon />}
          label="Completed"
          size="small"
          color="success"
        />;
      case ProcessingStatus.FAILED:
        return <Chip
          icon={<ErrorIcon />}
          label="Failed"
          size="small"
          color="error"
        />;
      default:
        return <Chip
          label={status}
          size="small"
          color="default"
        />;
    }
  };

  return (
    <Box>
      <input
        type="file"
        accept=".pdf"
        multiple
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        ref={fileInputRef}
      />

      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Upload PDF Notes
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Select up to 3 PDF files (max 30MB each) to upload. These will be processed and converted to vector embeddings for MCQ generation.
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper
        variant="outlined"
        sx={{
          p: 2,
          mb: 3,
          borderStyle: 'dashed',
          borderWidth: 2,
          borderColor: theme.palette.divider,
          backgroundColor: theme.palette.background.default,
          textAlign: 'center',
          cursor: 'pointer',
          '&:hover': {
            borderColor: theme.palette.primary.main,
            backgroundColor: theme.palette.action.hover
          }
        }}
        onClick={() => fileInputRef.current?.click()}
        onDragOver={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        onDrop={(e) => {
          e.preventDefault();
          e.stopPropagation();

          if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
            // Convert FileList to Array
            const droppedFiles = Array.from(e.dataTransfer.files);

            // Filter for PDF files
            const pdfFiles = droppedFiles.filter(file =>
              file.type === 'application/pdf' ||
              file.name.toLowerCase().endsWith('.pdf')
            );

            if (pdfFiles.length === 0) {
              setError('Only PDF files are allowed.');
              return;
            }

            // Check file sizes
            const validFiles = pdfFiles.filter(file => {
              if (file.size > MAX_FILE_SIZE) {
                setError(`File "${file.name}" exceeds the maximum size of 30MB.`);
                return false;
              }
              return true;
            });

            // Check total number of files
            if (selectedFiles.length + validFiles.length > MAX_FILES) {
              setError(`You can only upload a maximum of ${MAX_FILES} files at once.`);
              return;
            }

            // Add valid files to selected files
            setSelectedFiles([...selectedFiles, ...validFiles]);
          }
        }}
      >
        <Box sx={{ py: 3 }}>
          <CloudUploadIcon sx={{ fontSize: 48, color: theme.palette.text.secondary, mb: 1 }} />
          <Typography variant="body1" gutterBottom>
            Click to select PDF files
          </Typography>
          <Typography variant="body2" color="text.secondary">
            or drag and drop files here
          </Typography>
        </Box>
      </Paper>

      {selectedFiles.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Paper variant="outlined" sx={{ mb: 3 }}>
            <List dense>
              {selectedFiles.map((file, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <PdfIcon color="error" />
                  </ListItemIcon>
                  <ListItemText
                    primary={file.name}
                    secondary={`${(file.size / (1024 * 1024)).toFixed(2)} MB`}
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      aria-label="delete"
                      onClick={() => handleRemoveFile(index)}
                      disabled={uploadProgress}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Paper>

          {/* Course Name Input */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Enter Course Name
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Please enter the name of the course these notes are for.
            </Typography>
            <TextField
              fullWidth
              label="Course Name"
              variant="outlined"
              value={courseName}
              onChange={(e) => setCourseName(e.target.value)}
              placeholder="e.g. Introduction to Computer Science"
              margin="normal"
            />
          </Box>

          <Button
            variant="contained"
            color="primary"
            startIcon={<CloudUploadIcon />}
            onClick={handleUpload}
            disabled={uploadProgress}
            fullWidth
          >
            Upload {selectedFiles.length} {selectedFiles.length === 1 ? 'File' : 'Files'}
          </Button>
        </motion.div>
      )}

      {uploadProgress && (
        <Box sx={{ mt: 2 }}>
          <LinearProgress />
          <Typography variant="body2" align="center" sx={{ mt: 1 }}>
            Uploading files...
          </Typography>
        </Box>
      )}

      {uploadResults.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Upload Results
            </Typography>
            <Paper variant="outlined">
              <List dense>
                {uploadResults.map((result, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <PdfIcon color="error" />
                    </ListItemIcon>
                    <ListItemText
                      primary={result.file_name}
                      secondary={result.message}
                    />
                    <Box sx={{ ml: 2 }}>
                      {getStatusChip(result.status)}
                    </Box>
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Box>
        </motion.div>
      )}
    </Box>
  );
};

export default PDFUploadTool;
