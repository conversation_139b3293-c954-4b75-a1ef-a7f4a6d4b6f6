import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Grid,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Delete as DeleteIcon,
  PlayArrow as PlayArrowIcon,
  Flip as FlipIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { getFlashCards, deleteFlashCard } from '../../api/studentTools';
import { FlashCard } from '../../types/studentTools';
import { formatDate } from '../../utils/formatters';

interface GeneratedFlashCardsListProps {
  jobId?: number;
}

const GeneratedFlashCardsList: React.FC<GeneratedFlashCardsListProps> = ({ jobId }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [cardToDelete, setCardToDelete] = useState<FlashCard | null>(null);
  const [flippedCards, setFlippedCards] = useState<Set<number>>(new Set());

  // Fetch flash cards
  const {
    data: flashCards = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['generatedFlashCards', jobId],
    queryFn: () => getFlashCards(0, 100, jobId),
    staleTime: 30000,
  });

  // Delete flash card mutation
  const deleteMutation = useMutation({
    mutationFn: deleteFlashCard,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['generatedFlashCards'] });
      setDeleteDialogOpen(false);
      setCardToDelete(null);
    },
    onError: (error) => {
      console.error('Failed to delete flash card:', error);
    }
  });

  // Handle delete button click
  const handleDeleteClick = (card: FlashCard) => {
    setCardToDelete(card);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (cardToDelete) {
      deleteMutation.mutate(cardToDelete.id);
    }
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setCardToDelete(null);
  };

  // Handle card flip
  const handleFlip = (cardId: number) => {
    setFlippedCards(prev => {
      const newSet = new Set(prev);
      if (newSet.has(cardId)) {
        newSet.delete(cardId);
      } else {
        newSet.add(cardId);
      }
      return newSet;
    });
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load your generated flash cards. Please try again later.
      </Alert>
    );
  }

  if (flashCards.length === 0) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        No flash cards found. Please generate some flash cards first.
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" fontWeight="bold">
          Generated Flash Cards ({flashCards.length})
        </Typography>
        <Tooltip title="Refresh">
          <IconButton onClick={() => refetch()} size="small">
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Flash Cards Grid */}
      <Grid container spacing={2}>
        {flashCards.map((card) => {
          const isFlipped = flippedCards.has(card.id);
          return (
            <Grid item xs={12} sm={6} md={4} key={card.id}>
              <Card 
                sx={{ 
                  borderRadius: 2, 
                  border: '1px solid', 
                  borderColor: 'divider',
                  minHeight: 200,
                  cursor: 'pointer',
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: 2
                  }
                }}
                onClick={() => handleFlip(card.id)}
              >
                <CardContent sx={{ height: 140, display: 'flex', flexDirection: 'column' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Chip
                      label={`ID: ${card.id}`}
                      variant="outlined"
                      size="small"
                    />
                    <Typography variant="caption" color="text.secondary">
                      {card.created_at ? formatDate(card.created_at) : 'Unknown date'}
                    </Typography>
                  </Box>

                  <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Typography 
                      variant="body1" 
                      sx={{ 
                        textAlign: 'center',
                        fontWeight: isFlipped ? 'normal' : 'bold',
                        color: isFlipped ? 'text.secondary' : 'text.primary'
                      }}
                    >
                      {isFlipped 
                        ? (card.back_content.length > 100 
                            ? `${card.back_content.substring(0, 100)}...` 
                            : card.back_content)
                        : (card.front_content.length > 80 
                            ? `${card.front_content.substring(0, 80)}...` 
                            : card.front_content)
                      }
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
                    <Chip
                      label={isFlipped ? "Back" : "Front"}
                      size="small"
                      color={isFlipped ? "secondary" : "primary"}
                      variant="outlined"
                    />
                  </Box>
                </CardContent>

                <CardActions sx={{ px: 2, pb: 2, justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<FlipIcon />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleFlip(card.id);
                      }}
                    >
                      Flip
                    </Button>
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<PlayArrowIcon />}
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/flashcards/study/${card.id}`);
                      }}
                    >
                      Study
                    </Button>
                  </Box>

                  <Tooltip title="Delete Flash Card">
                    <IconButton
                      size="small"
                      color="error"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteClick(card);
                      }}
                      disabled={deleteMutation.isPending}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </CardActions>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-card-dialog-title"
        aria-describedby="delete-card-dialog-description"
      >
        <DialogTitle id="delete-card-dialog-title">
          Delete Flash Card
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-card-dialog-description">
            Are you sure you want to delete this flash card? This action cannot be undone.
          </DialogContentText>
          {cardToDelete && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500, mb: 1 }}>
                Front: {cardToDelete.front_content.length > 50 
                  ? `${cardToDelete.front_content.substring(0, 50)}...` 
                  : cardToDelete.front_content}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Back: {cardToDelete.back_content.length > 50 
                  ? `${cardToDelete.back_content.substring(0, 50)}...` 
                  : cardToDelete.back_content}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            variant="contained"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GeneratedFlashCardsList;
