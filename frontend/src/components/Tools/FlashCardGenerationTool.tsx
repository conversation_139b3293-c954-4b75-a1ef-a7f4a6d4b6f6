import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
  OutlinedInput,
  Slider,
  Button,
  Chip,
  CircularProgress,
  Alert,
  useTheme,
  SelectChangeEvent,
  Grid,
  useMediaQuery
} from '@mui/material';
import {
  AutoAwesome as AutoAwesomeIcon
} from '@mui/icons-material';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getStudentNotes, generateFlashCards } from '../../api/studentTools';
import { ProcessingStatus } from '../../types/studentTools';

const FlashCardGenerationTool: React.FC = () => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State
  const [selectedNoteIds, setSelectedNoteIds] = useState<number[]>([]);
  const [cardCount, setCardCount] = useState<number>(30);
  const [error, setError] = useState<string | null>(null);
  const [generationJobId, setGenerationJobId] = useState<number | null>(null);

  // Fetch notes
  const {
    data: notes = [],
    isLoading: isLoadingNotes,
    refetch: refetchNotes
  } = useQuery({
    queryKey: ['studentNotes'],
    queryFn: () => getStudentNotes(),
    staleTime: 30000 // 30 seconds
  });

  // Filter only completed notes
  const completedNotes = notes.filter(note => note.status === ProcessingStatus.COMPLETED);

  // No longer fetching courses

  // Generate flash cards mutation
  const generateFlashCardsMutation = useMutation({
    mutationFn: (data: { note_ids: number[], card_count: number }) =>
      generateFlashCards(data.note_ids, undefined, data.card_count),
    onSuccess: (data) => {
      setGenerationJobId(data.job_id);
    },
    onError: (error: any) => {
      setError(error.message || 'Failed to start flash card generation');
    }
  });

  // Listen for refresh events
  useEffect(() => {
    const handleRefresh = () => {
      refetchNotes();
    };

    window.addEventListener('refreshNotes', handleRefresh);
    return () => {
      window.removeEventListener('refreshNotes', handleRefresh);
    };
  }, [refetchNotes]);

  // Handle note selection change
  const handleNoteSelectionChange = (event: SelectChangeEvent<number[]>) => {
    const value = event.target.value as number[];
    setSelectedNoteIds(value);
  };

  // Course selection removed

  // Handle card count change
  const handleCardCountChange = (_event: Event, newValue: number | number[]) => {
    setCardCount(newValue as number);
  };

  // Handle generate button click
  const handleGenerate = () => {
    if (selectedNoteIds.length === 0) {
      setError('Please select at least one note');
      return;
    }

    setError(null);

    console.log('Generating flash cards with:', {
      note_ids: selectedNoteIds,
      card_count: cardCount
    });

    generateFlashCardsMutation.mutate({
      note_ids: selectedNoteIds,
      card_count: cardCount
    });

    // Trigger a refresh of the flash card jobs list after a delay
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('refreshNotes'));
    }, 2000);
  };

  // Animation variants (keeping for potential future use)
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  return (
    <Box>
      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 2,
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            '& .MuiAlert-message': {
              fontSize: '1rem',
              width: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }
          }}
        >
          {error}
        </Alert>
      )}

      {generationJobId && (
        <Alert
          severity="success"
          sx={{
            mb: 3,
            borderRadius: 2,
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            '& .MuiAlert-message': {
              fontSize: '1rem',
              width: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }
          }}
        >
          Flash card generation started! Job ID: {generationJobId}. You can view the progress in the "My Flash Cards" tab.
        </Alert>
      )}

      {completedNotes.length === 0 && !isLoadingNotes ? (
        <Alert
          severity="info"
          sx={{
            mb: 3,
            borderRadius: 2,
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            '& .MuiAlert-message': {
              fontSize: '1rem',
              width: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }
          }}
        >
          You don't have any processed notes yet. Please upload and process some notes first to generate flash cards.
        </Alert>
      ) : (
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
            <Grid item xs={12}>
              <Box sx={{ mb: { xs: 2, sm: 3 } }}>
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{
                    fontWeight: 600,
                    color: 'text.primary',
                    fontSize: { xs: '1.1rem', sm: '1.25rem' }
                  }}
                >
                  Select Study Materials
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    fontSize: { xs: '0.875rem', sm: '0.875rem' }
                  }}
                >
                  Choose the notes you want to generate flash cards from
                </Typography>
                <FormControl fullWidth disabled={isLoadingNotes || generateFlashCardsMutation.isPending}>
                  <InputLabel id="notes-select-label">Select Notes</InputLabel>
                  <Select
                    labelId="notes-select-label"
                    id="notes-select"
                    multiple
                    value={selectedNoteIds}
                    onChange={handleNoteSelectionChange}
                    input={<OutlinedInput label="Select Notes" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((noteId) => {
                          const note = completedNotes.find(n => n.id === noteId);
                          return (
                            <Chip
                              key={noteId}
                              label={note ? note.file_name : `Note ${noteId}`}
                              size="small"
                              sx={{
                                bgcolor: 'primary.main',
                                color: 'white',
                                '& .MuiChip-deleteIcon': {
                                  color: 'white'
                                }
                              }}
                            />
                          );
                        })}
                      </Box>
                    )}
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 300,
                          borderRadius: 8
                        }
                      }
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2
                      }
                    }}
                  >
                    {isLoadingNotes ? (
                      <MenuItem disabled>
                        <CircularProgress size={20} sx={{ mr: 1 }} />
                        Loading notes...
                      </MenuItem>
                    ) : completedNotes.length === 0 ? (
                      <MenuItem disabled>
                        No processed notes available
                      </MenuItem>
                    ) : (
                      completedNotes.map((note) => (
                        <MenuItem key={note.id} value={note.id}>
                          <Checkbox checked={selectedNoteIds.indexOf(note.id) > -1} />
                          <ListItemText
                            primary={note.file_name}
                            secondary={`Uploaded: ${new Date(note.created_at).toLocaleDateString()}`}
                          />
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ mb: { xs: 2, sm: 3 } }}>
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{
                    fontWeight: 600,
                    color: 'text.primary',
                    fontSize: { xs: '1.1rem', sm: '1.25rem' }
                  }}
                >
                  Card Settings
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    fontSize: { xs: '0.875rem', sm: '0.875rem' }
                  }}
                >
                  Choose how many flash cards to generate
                </Typography>
                <Box sx={{ px: { xs: 1, sm: 2 } }}>
                  <Typography
                    id="card-count-slider"
                    gutterBottom
                    sx={{
                      fontWeight: 500,
                      fontSize: { xs: '0.9rem', sm: '1rem' }
                    }}
                  >
                    Number of Flash Cards: {cardCount}
                  </Typography>
                  <Slider
                    value={cardCount}
                    onChange={handleCardCountChange}
                    aria-labelledby="card-count-slider"
                    valueLabelDisplay="auto"
                    step={5}
                    marks
                    min={5}
                    max={100}
                    disabled={generateFlashCardsMutation.isPending}
                    sx={{
                      '& .MuiSlider-thumb': {
                        width: { xs: 18, sm: 20 },
                        height: { xs: 18, sm: 20 }
                      },
                      '& .MuiSlider-track': {
                        height: { xs: 4, sm: 6 }
                      },
                      '& .MuiSlider-rail': {
                        height: { xs: 4, sm: 6 }
                      },
                      '& .MuiSlider-markLabel': {
                        fontSize: { xs: '0.75rem', sm: '0.875rem' }
                      }
                    }}
                  />
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AutoAwesomeIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />}
                onClick={handleGenerate}
                disabled={
                  selectedNoteIds.length === 0 ||
                  generateFlashCardsMutation.isPending
                }
                fullWidth
                size="large"
                sx={{
                  py: { xs: 1.2, sm: 1.5 },
                  fontSize: { xs: '1rem', sm: '1.1rem' },
                  fontWeight: 600,
                  borderRadius: 2,
                  textTransform: 'none',
                  minHeight: { xs: 48, sm: 56 }
                }}
              >
                {generateFlashCardsMutation.isPending ? (
                  <>
                    Generating...
                    <CircularProgress
                      size={20}
                      sx={{ ml: 1 }}
                    />
                  </>
                ) : (
                  `Generate ${cardCount} Flash Cards`
                )}
              </Button>
            </Grid>
          </Grid>
        </Box>
      )}
    </Box>
  );
};

export default FlashCardGenerationTool;
