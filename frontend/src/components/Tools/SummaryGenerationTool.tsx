import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  SelectChangeEvent,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  AutoAwesome as AutoAwesomeIcon,
  Summarize as SummarizeIcon,
  Description as DescriptionIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  getStudentNotes, 
  generateSummaryForNote, 
  generateSummaries, 
  getSummaryGenerationStatus 
} from '../../api/studentTools';
import { ProcessingStatus } from '../../types/studentTools';

const SummaryGenerationTool: React.FC = () => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [selectedNoteIds, setSelectedNoteIds] = useState<number[]>([]);
  const [generationJobId, setGenerationJobId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [generatedSummary, setGeneratedSummary] = useState<any>(null);

  // Fetch student notes
  const {
    data: notes = [],
    isLoading: isLoadingNotes,
    error: notesError
  } = useQuery({
    queryKey: ['studentNotes'],
    queryFn: () => getStudentNotes(),
    staleTime: 30000 // 30 seconds
  });

  // Filter only completed notes
  const completedNotes = notes.filter(note => note.status === ProcessingStatus.COMPLETED);

  // Generate summary mutation
  const generateSummaryMutation = useMutation({
    mutationFn: (data: { note_ids: number[] }) => {
      if (data.note_ids.length === 1) {
        return generateSummaryForNote(data.note_ids[0]);
      } else {
        return generateSummaries(data.note_ids);
      }
    },
    onSuccess: (data) => {
      setGenerationJobId(data.job_id);
    },
    onError: (error: any) => {
      setError(error.message || 'Failed to start summary generation');
    }
  });

  // Poll for job status if we have a job ID
  const {
    data: jobStatus,
    isLoading: isLoadingJobStatus
  } = useQuery({
    queryKey: ['summaryGenerationStatus', generationJobId],
    queryFn: () => getSummaryGenerationStatus(generationJobId!),
    enabled: !!generationJobId,
    refetchInterval: (data) => {
      // Poll every 2 seconds until job is completed or failed
      return data?.status === ProcessingStatus.COMPLETED ||
             data?.status === ProcessingStatus.FAILED ?
             false : 2000;
    }
  });

  // Handle note selection
  const handleNoteSelection = (event: SelectChangeEvent<typeof selectedNoteIds>) => {
    const value = event.target.value;
    const newSelectedIds = typeof value === 'string' ? JSON.parse(value) : value;
    console.log('Selected note IDs:', newSelectedIds);
    setSelectedNoteIds(newSelectedIds);
  };

  // Handle generate button click
  const handleGenerate = () => {
    if (selectedNoteIds.length === 0) {
      setError('Please select at least one note');
      return;
    }

    setError(null);
    setGeneratedSummary(null);

    console.log('Generating summary with:', {
      note_ids: selectedNoteIds
    });

    generateSummaryMutation.mutate({
      note_ids: selectedNoteIds
    });

    // Trigger a refresh of the summaries list after a delay
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('refreshNotes'));
    }, 2000);
  };

  // Get status color
  const getStatusColor = (status: ProcessingStatus) => {
    switch (status) {
      case ProcessingStatus.PENDING:
        return theme.palette.info.main;
      case ProcessingStatus.PROCESSING:
      case ProcessingStatus.GENERATING:
        return theme.palette.warning.main;
      case ProcessingStatus.COMPLETED:
        return theme.palette.success.main;
      case ProcessingStatus.FAILED:
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };



  return (
    <Box>
      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 2,
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            '& .MuiAlert-message': {
              fontSize: '1rem',
              width: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }
          }}
        >
          {error}
        </Alert>
      )}

      {notesError && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 2,
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            '& .MuiAlert-message': {
              fontSize: '1rem',
              width: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }
          }}
        >
          Failed to load your notes. Please try again later.
        </Alert>
      )}

      {completedNotes.length === 0 && !isLoadingNotes ? (
        <Alert
          severity="info"
          sx={{
            mb: 3,
            borderRadius: 2,
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            '& .MuiAlert-message': {
              fontSize: '1rem',
              width: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }
          }}
        >
          You don't have any processed notes yet. Please upload and process some notes first to generate summaries.
        </Alert>
      ) : (
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
            <Grid item xs={12}>
              <Box sx={{ mb: { xs: 2, sm: 3 } }}>
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{
                    fontWeight: 600,
                    color: 'text.primary',
                    fontSize: { xs: '1.1rem', sm: '1.25rem' }
                  }}
                >
                  Select Study Materials
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    fontSize: { xs: '0.875rem', sm: '0.875rem' }
                  }}
                >
                  Choose the notes you want to generate summaries from
                </Typography>
                <FormControl fullWidth disabled={isLoadingNotes || generateSummaryMutation.isPending}>
                  <InputLabel id="notes-select-label">Select Notes</InputLabel>
                  <Select
                    labelId="notes-select-label"
                    id="notes-select"
                    multiple
                    value={selectedNoteIds}
                    onChange={handleNoteSelection}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((noteId) => {
                          const note = completedNotes.find(n => n.id === noteId);
                            return (
                              <Chip
                                key={noteId}
                                label={note?.file_name || `Note ${noteId}`}
                                size="small"
                                sx={{
                                  bgcolor: 'primary.main',
                                  color: 'white',
                                  '& .MuiChip-deleteIcon': {
                                    color: 'white'
                                  }
                                }}
                              />
                            );
                          })}
                        </Box>
                      )}
                      label="Select Notes"
                      MenuProps={{
                        PaperProps: {
                          style: {
                            maxHeight: 300,
                            borderRadius: 8
                          }
                        }
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2
                        }
                      }}
                    >
                      {isLoadingNotes ? (
                        <MenuItem disabled>
                          <CircularProgress size={20} sx={{ mr: 1 }} />
                          Loading notes...
                        </MenuItem>
                      ) : completedNotes.length === 0 ? (
                        <MenuItem disabled>No processed notes available</MenuItem>
                      ) : (
                        completedNotes.map((note) => (
                          <MenuItem key={note.id} value={note.id}>
                            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                              <DescriptionIcon sx={{ mr: 1, color: 'text.secondary' }} />
                              <Box>
                                <Typography variant="body2">{note.file_name}</Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {note.course_name && `Course: ${note.course_name} • `}
                                  Uploaded: {new Date(note.created_at).toLocaleDateString()}
                                </Typography>
                              </Box>
                            </Box>
                          </MenuItem>
                        ))
                      )}
                    </Select>
                  </FormControl>
                </Box>
              </Grid>

                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<SummarizeIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />}
                    onClick={handleGenerate}
                    disabled={
                      isLoadingNotes ||
                      generateSummaryMutation.isPending ||
                      selectedNoteIds.length === 0 ||
                      (!!jobStatus && jobStatus.status === ProcessingStatus.PROCESSING)
                    }
                    fullWidth
                    size="large"
                    sx={{
                      py: { xs: 1.2, sm: 1.5 },
                      fontSize: { xs: '1rem', sm: '1.1rem' },
                      fontWeight: 600,
                      borderRadius: 2,
                      textTransform: 'none',
                      minHeight: { xs: 48, sm: 56 }
                    }}
                  >
                    {generateSummaryMutation.isPending ? (
                      <>
                        Generating Summary...
                        <CircularProgress
                          size={20}
                          sx={{ ml: 1 }}
                        />
                      </>
                    ) : (
                      `Generate AI Summary${selectedNoteIds.length > 1 ? ` (${selectedNoteIds.length} notes)` : ''}`
                    )}
                  </Button>
                </Grid>
              </Grid>
            </Box>
          )}

        {jobStatus && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Paper variant="outlined" sx={{ p: 3, mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <AutoAwesomeIcon sx={{ mr: 1, color: 'primary.main' }} />
                Summary Generation Status
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
                  <CircularProgress
                    variant="determinate"
                    value={jobStatus.progress_percentage}
                    size={60}
                    thickness={4}
                    color={
                      jobStatus.status === ProcessingStatus.COMPLETED ? 'success' :
                      jobStatus.status === ProcessingStatus.FAILED ? 'error' : 'primary'
                    }
                  />
                  <Box
                    sx={{
                      top: 0,
                      left: 0,
                      bottom: 0,
                      right: 0,
                      position: 'absolute',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="caption" component="div" color="text.secondary" fontWeight="bold">
                      {`${Math.round(jobStatus.progress_percentage)}%`}
                    </Typography>
                  </Box>
                </Box>

                <Box>
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    Status: <Chip
                      label={jobStatus.status}
                      size="small"
                      sx={{
                        backgroundColor: getStatusColor(jobStatus.status),
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  </Typography>

                  {jobStatus.error_message && (
                    <Typography variant="body2" color="error">
                      Error: {jobStatus.error_message}
                    </Typography>
                  )}

                  {jobStatus.processing_metadata && (
                    <Typography variant="caption" color="text.secondary">
                      Multiagent AI system processing...
                    </Typography>
                  )}
                </Box>
              </Box>

              {jobStatus.status === ProcessingStatus.COMPLETED && (
                <>
                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body1" gutterBottom color="success.main" fontWeight="bold">
                      🎉 Summary generated successfully!
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {jobStatus.generated_summary_ids?.length || 0} comprehensive summary created with AI-powered analysis
                    </Typography>

                    <Button
                      variant="contained"
                      color="success"
                      startIcon={<ViewIcon />}
                      onClick={() => {
                        // Navigate to summaries view or trigger refresh
                        window.dispatchEvent(new CustomEvent('refreshSummaries'));
                        queryClient.invalidateQueries({ queryKey: ['summaries'] });
                      }}
                      sx={{ mt: 1 }}
                    >
                      View Generated Summary
                    </Button>
                  </Box>
                </>
              )}
            </Paper>
          </motion.div>
        )}

      </Box>
    );
};

export default SummaryGenerationTool;
