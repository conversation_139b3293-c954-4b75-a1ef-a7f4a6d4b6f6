import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Slider,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  SelectChangeEvent,
  useTheme,
  Tooltip
} from '@mui/material';
import {
  AutoAwesome as AutoAwesomeIcon,
  School as SchoolIcon,
  PlayArrow as PlayArrowIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getStudentNotes, generateMCQs, getMCQGenerationStatus } from '../../api/studentTools';
import { ProcessingStatus } from '../../types/studentTools';

const MCQGenerationTool: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [selectedNoteIds, setSelectedNoteIds] = useState<number[]>([]);
  const [questionCount, setQuestionCount] = useState<number>(60);
  const [generationJobId, setGenerationJobId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch student notes
  const {
    data: notes = [],
    isLoading: isLoadingNotes,
    error: notesError
  } = useQuery({
    queryKey: ['studentNotes'],
    queryFn: () => getStudentNotes(),
    staleTime: 30000 // 30 seconds
  });

  // Filter only completed notes
  const completedNotes = notes.filter(note => note.status === ProcessingStatus.COMPLETED);

  // No longer fetching courses

  // Generate MCQs mutation
  const generateMCQsMutation = useMutation({
    mutationFn: (data: { note_ids: number[], question_count: number }) =>
      generateMCQs(data.note_ids, undefined, data.question_count),
    onSuccess: (data) => {
      setGenerationJobId(data.job_id);
    },
    onError: (error: any) => {
      setError(error.message || 'Failed to start MCQ generation');
    }
  });

  // Poll for job status if we have a job ID
  const {
    data: jobStatus,
    isLoading: isLoadingJobStatus
  } = useQuery({
    queryKey: ['mcqGenerationStatus', generationJobId],
    queryFn: () => getMCQGenerationStatus(generationJobId!),
    enabled: !!generationJobId,
    refetchInterval: (data) => {
      // Poll every 2 seconds until job is completed or failed
      return data?.status === ProcessingStatus.COMPLETED ||
             data?.status === ProcessingStatus.FAILED ?
             false : 2000;
    }
  });

  // Handle note selection
  const handleNoteSelection = (event: SelectChangeEvent<typeof selectedNoteIds>) => {
    const value = event.target.value;
    // Make sure we're handling the value correctly
    const newSelectedIds = typeof value === 'string' ? JSON.parse(value) : value;
    console.log('Selected note IDs:', newSelectedIds);
    setSelectedNoteIds(newSelectedIds);
  };

  // Handle question count change
  const handleQuestionCountChange = (event: Event, newValue: number | number[]) => {
    setQuestionCount(newValue as number);
  };

  // Handle generate button click
  const handleGenerate = () => {
    if (selectedNoteIds.length === 0) {
      setError('Please select at least one note');
      return;
    }

    setError(null);

    console.log('Generating MCQs with:', {
      note_ids: selectedNoteIds,
      question_count: questionCount
    });

    generateMCQsMutation.mutate({
      note_ids: selectedNoteIds,
      question_count: questionCount
    });

    // Trigger a refresh of the MCQ jobs list after a delay
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('refreshNotes'));
    }, 2000);
  };

  // Handle practice button click
  const handlePractice = () => {
    if (!jobStatus || !jobStatus.course_id) return;

    // Navigate to practice mode with the generated questions
    navigate(`/mcq/practice/${jobStatus.course_id}?noteGenerated=true&jobId=${jobStatus.job_id}`);
  };

  // Handle exam button click
  const handleExam = () => {
    if (!jobStatus || !jobStatus.course_id) return;

    // Navigate to exam mode with the generated questions
    navigate(`/mcq/exam/${jobStatus.course_id}?noteGenerated=true&jobId=${jobStatus.job_id}`);
  };

  // Get status color
  const getStatusColor = (status: ProcessingStatus) => {
    switch (status) {
      case ProcessingStatus.PENDING:
        return theme.palette.info.main;
      case ProcessingStatus.PROCESSING:
      case ProcessingStatus.GENERATING:
        return theme.palette.warning.main;
      case ProcessingStatus.COMPLETED:
        return theme.palette.success.main;
      case ProcessingStatus.FAILED:
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Generate MCQs from Your Notes
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Select your processed notes and generate multiple-choice questions for practice or exam.
        </Typography>
      </Box>

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 2,
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            '& .MuiAlert-message': {
              width: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }
          }}
        >
          {error}
        </Alert>
      )}

      {notesError && (
        <Alert
          severity="error"
          sx={{
            mb: 2,
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            '& .MuiAlert-message': {
              width: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }
          }}
        >
          Failed to load your notes. Please try again later.
        </Alert>
      )}

      {completedNotes.length === 0 && !isLoadingNotes ? (
        <Alert
          severity="info"
          sx={{
            mb: 3,
            borderRadius: 2,
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            '& .MuiAlert-message': {
              fontSize: '1rem',
              width: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }
          }}
        >
          You don't have any processed notes yet. Please upload and process some notes first to generate MCQs.
        </Alert>
      ) : (
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
            <Grid item xs={12}>
              <Box sx={{ mb: { xs: 2, sm: 3 } }}>
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{
                    fontWeight: 600,
                    color: 'text.primary',
                    fontSize: { xs: '1.1rem', sm: '1.25rem' }
                  }}
                >
                  Select Study Materials
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    fontSize: { xs: '0.875rem', sm: '0.875rem' }
                  }}
                >
                  Choose the notes you want to generate questions from
                </Typography>
                <FormControl fullWidth disabled={isLoadingNotes || generateMCQsMutation.isPending}>
                  <InputLabel id="notes-select-label">Select Notes</InputLabel>
                  <Select
                    labelId="notes-select-label"
                    id="notes-select"
                    multiple
                    value={selectedNoteIds}
                    onChange={handleNoteSelection}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((noteId) => {
                          const note = completedNotes.find(n => n.id === noteId);
                          return (
                            <Chip
                              key={noteId}
                              label={note?.file_name || `Note ${noteId}`}
                              size="small"
                              sx={{
                                bgcolor: 'primary.main',
                                color: 'white',
                                '& .MuiChip-deleteIcon': {
                                  color: 'white'
                                }
                              }}
                            />
                          );
                        })}
                      </Box>
                    )}
                    label="Select Notes"
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 300,
                          borderRadius: 8
                        }
                      }
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2
                      }
                    }}
                  >
                    {isLoadingNotes ? (
                      <MenuItem disabled>Loading notes...</MenuItem>
                    ) : completedNotes.length === 0 ? (
                      <MenuItem disabled>No processed notes available</MenuItem>
                    ) : (
                      completedNotes.map((note) => (
                        <MenuItem key={note.id} value={note.id}>
                          {note.file_name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              </Box>
            </Grid>

            {/* Course selection removed - using course name from notes */}

            <Grid item xs={12}>
              <Box sx={{ mb: { xs: 2, sm: 3 } }}>
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{
                    fontWeight: 600,
                    color: 'text.primary',
                    fontSize: { xs: '1.1rem', sm: '1.25rem' }
                  }}
                >
                  Question Settings
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    fontSize: { xs: '0.875rem', sm: '0.875rem' }
                  }}
                >
                  Choose how many questions to generate
                </Typography>
                <Box sx={{ px: { xs: 1, sm: 2 } }}>
                  <Typography
                    id="question-count-slider"
                    gutterBottom
                    sx={{
                      fontWeight: 500,
                      fontSize: { xs: '0.9rem', sm: '1rem' }
                    }}
                  >
                    Number of Questions: {questionCount}
                  </Typography>
                  <Slider
                    value={questionCount}
                    onChange={handleQuestionCountChange}
                    aria-labelledby="question-count-slider"
                    valueLabelDisplay="auto"
                    step={10}
                    marks
                    min={10}
                    max={100}
                    disabled={generateMCQsMutation.isPending}
                    sx={{
                      '& .MuiSlider-thumb': {
                        width: { xs: 18, sm: 20 },
                        height: { xs: 18, sm: 20 }
                      },
                      '& .MuiSlider-track': {
                        height: { xs: 4, sm: 6 }
                      },
                      '& .MuiSlider-rail': {
                        height: { xs: 4, sm: 6 }
                      },
                      '& .MuiSlider-markLabel': {
                        fontSize: { xs: '0.75rem', sm: '0.875rem' }
                      }
                    }}
                  />
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AutoAwesomeIcon sx={{ fontSize: { xs: 20, sm: 24 } }} />}
                onClick={handleGenerate}
                disabled={
                  isLoadingNotes ||
                  generateMCQsMutation.isPending ||
                  selectedNoteIds.length === 0 ||
                  (!!jobStatus && jobStatus.status === ProcessingStatus.PROCESSING)
                }
                fullWidth
                size="large"
                sx={{
                  py: { xs: 1.2, sm: 1.5 },
                  fontSize: { xs: '1rem', sm: '1.1rem' },
                  fontWeight: 600,
                  borderRadius: 2,
                  textTransform: 'none',
                  minHeight: { xs: 48, sm: 56 }
                }}
              >
                {generateMCQsMutation.isPending ? (
                  <>
                    Generating...
                    <CircularProgress
                      size={{ xs: 18, sm: 20 }}
                      sx={{ ml: 1 }}
                    />
                  </>
                ) : (
                  `Generate ${questionCount} MCQs`
                )}
              </Button>
            </Grid>
          </Grid>
        </Box>
      )}

      {jobStatus && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Paper
            variant="outlined"
            sx={{
              p: { xs: 2, sm: 3 },
              mb: 3,
              borderRadius: 2,
              border: 1,
              borderColor: 'divider'
            }}
          >
            <Typography
              variant="h6"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                fontSize: { xs: '1.1rem', sm: '1.25rem' }
              }}
            >
              Generation Progress
            </Typography>

            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 2,
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 2, sm: 0 }
            }}>
              <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
                <CircularProgress
                  variant="determinate"
                  value={jobStatus.progress_percentage}
                  color={
                    jobStatus.status === ProcessingStatus.COMPLETED ? 'success' :
                    jobStatus.status === ProcessingStatus.FAILED ? 'error' : 'primary'
                  }
                />
                <Box
                  sx={{
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0,
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="caption" component="div" color="text.secondary">
                    {`${Math.round(jobStatus.progress_percentage)}%`}
                  </Typography>
                </Box>
              </Box>

              <Box>
                <Typography variant="body1">
                  Status: <Chip
                    label={jobStatus.status}
                    size="small"
                    sx={{
                      backgroundColor: getStatusColor(jobStatus.status),
                      color: 'white'
                    }}
                  />
                </Typography>

                {jobStatus.error_message && (
                  <Typography variant="body2" color="error">
                    Error: {jobStatus.error_message}
                  </Typography>
                )}
              </Box>
            </Box>

            {jobStatus.status === ProcessingStatus.COMPLETED && (
              <>
                <Divider sx={{ my: 2 }} />

                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body1" gutterBottom>
                    {jobStatus.generated_question_ids?.length || 0} questions generated successfully!
                  </Typography>

                  <Box sx={{
                    mt: 3,
                    display: 'flex',
                    justifyContent: 'center',
                    gap: { xs: 1.5, sm: 2 },
                    flexWrap: 'wrap',
                    flexDirection: { xs: 'column', sm: 'row' }
                  }}>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<PlayArrowIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />}
                      onClick={handlePractice}
                      sx={{
                        px: { xs: 2, sm: 3 },
                        py: { xs: 1, sm: 1 },
                        borderRadius: 2,
                        textTransform: 'none',
                        fontWeight: 600,
                        fontSize: { xs: '0.9rem', sm: '1rem' },
                        minHeight: { xs: 44, sm: 48 },
                        flex: { xs: 1, sm: 'none' }
                      }}
                    >
                      Practice Mode
                    </Button>

                    <Button
                      variant="contained"
                      color="secondary"
                      startIcon={<AssessmentIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />}
                      onClick={handleExam}
                      sx={{
                        px: { xs: 2, sm: 3 },
                        py: { xs: 1, sm: 1 },
                        borderRadius: 2,
                        textTransform: 'none',
                        fontWeight: 600,
                        fontSize: { xs: '0.9rem', sm: '1rem' },
                        minHeight: { xs: 44, sm: 48 },
                        flex: { xs: 1, sm: 'none' }
                      }}
                    >
                      Exam Mode
                    </Button>
                  </Box>
                </Box>
              </>
            )}
          </Paper>
        </motion.div>
      )}
    </Box>
  );
};

export default MCQGenerationTool;
