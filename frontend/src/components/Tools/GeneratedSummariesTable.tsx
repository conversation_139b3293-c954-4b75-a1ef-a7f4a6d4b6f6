import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  useTheme,
  Tooltip,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon,
  AutoAwesome as AutoAwesomeIcon,
  School as SchoolIcon,
  Description as DescriptionIcon,
  CheckCircle as CheckCircleIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { getSummaries, downloadSummaryAsPdf, deleteSummary } from '../../api/studentTools';
import { NoteSummary } from '../../types/studentTools';
import MathMarkdown from '../common/MathMarkdown';

const GeneratedSummariesTable: React.FC = () => {
  const theme = useTheme();
  const queryClient = useQueryClient();

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedSummary, setSelectedSummary] = useState<NoteSummary | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [summaryToDelete, setSummaryToDelete] = useState<NoteSummary | null>(null);

  // Fetch summaries
  const {
    data: summaries = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['summaries', page, rowsPerPage],
    queryFn: () => getSummaries({
      skip: page * rowsPerPage,
      limit: rowsPerPage
    }),
    staleTime: 30000 // 30 seconds
  });

  // Listen for refresh events
  useEffect(() => {
    const handleRefresh = () => {
      refetch();
    };

    window.addEventListener('refreshSummaries', handleRefresh);
    return () => {
      window.removeEventListener('refreshSummaries', handleRefresh);
    };
  }, [refetch]);

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle view summary
  const handleViewSummary = (summary: NoteSummary) => {
    setSelectedSummary(summary);
    setDialogOpen(true);
  };

  // Handle close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedSummary(null);
  };

  // Handle download summary as PDF
  const handleDownloadSummary = async (summary: NoteSummary) => {
    try {
      await downloadSummaryAsPdf(summary.id);
    } catch (error) {
      console.error('Failed to download summary as PDF:', error);
      // You could add a toast notification here to inform the user of the error
    }
  };

  // Delete summary mutation
  const deleteMutation = useMutation({
    mutationFn: deleteSummary,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['summaries'] });
      setDeleteDialogOpen(false);
      setSummaryToDelete(null);
    },
    onError: (error) => {
      console.error('Failed to delete summary:', error);
    }
  });

  // Handle delete button click
  const handleDeleteClick = (summary: NoteSummary) => {
    setSummaryToDelete(summary);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (summaryToDelete) {
      deleteMutation.mutate(summaryToDelete.id);
    }
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setSummaryToDelete(null);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Render key concepts
  const renderKeyConcepts = (concepts: string[] | undefined) => {
    if (!concepts || concepts.length === 0) return 'None';
    
    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
        {concepts.slice(0, 3).map((concept, index) => (
          <Chip
            key={index}
            label={typeof concept === 'string' ? concept : concept}
            size="small"
            variant="outlined"
            color="primary"
          />
        ))}
        {concepts.length > 3 && (
          <Chip
            label={`+${concepts.length - 3} more`}
            size="small"
            variant="outlined"
            color="secondary"
          />
        )}
      </Box>
    );
  };

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load summaries. Please try again later.
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Generated Summaries ({summaries.length})
        </Typography>
        <Button
          startIcon={<RefreshIcon />}
          onClick={() => refetch()}
          disabled={isLoading}
          size="small"
        >
          Refresh
        </Button>
      </Box>

      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : summaries.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <AutoAwesomeIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No summaries generated yet
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Generate your first AI-powered summary from your notes using the tool above.
          </Typography>
        </Paper>
      ) : (
        <>
          <TableContainer
            component={Paper}
            variant="outlined"
            sx={{
              borderRadius: 2,
              border: 1,
              borderColor: 'divider'
            }}
          >
            <Table sx={{ minWidth: { xs: 'auto', sm: 650 } }}>
              <TableHead>
                <TableRow sx={{ backgroundColor: theme.palette.grey[50] }}>
                  <TableCell><strong>Title</strong></TableCell>
                  <TableCell sx={{ display: { xs: 'none', md: 'table-cell' } }}><strong>Course</strong></TableCell>
                  <TableCell sx={{ display: { xs: 'none', lg: 'table-cell' } }}><strong>Key Concepts</strong></TableCell>
                  <TableCell sx={{ display: { xs: 'none', lg: 'table-cell' } }}><strong>Topics</strong></TableCell>
                  <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}><strong>Created</strong></TableCell>
                  <TableCell align="center"><strong>Actions</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {summaries.map((summary) => (
                  <TableRow
                    key={summary.id}
                    hover
                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                  >
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <DescriptionIcon sx={{ mr: 1, color: 'primary.main', fontSize: { xs: 18, sm: 20 } }} />
                        <Box>
                          <Typography
                            variant="body2"
                            fontWeight="medium"
                            sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                          >
                            {summary.title}
                          </Typography>
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
                          >
                            ID: {summary.id}
                          </Typography>
                          {/* Show course on mobile */}
                          <Box sx={{ display: { xs: 'block', md: 'none' }, mt: 0.5 }}>
                            {summary.course_name ? (
                              <Chip
                                label={summary.course_name}
                                size="small"
                                icon={<SchoolIcon sx={{ fontSize: 14 }} />}
                                color="secondary"
                                variant="outlined"
                                sx={{ fontSize: '0.7rem', height: 20 }}
                              />
                            ) : (
                              <Typography variant="caption" color="text.secondary">
                                No course
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell sx={{ display: { xs: 'none', md: 'table-cell' } }}>
                      {summary.course_name ? (
                        <Chip
                          label={summary.course_name}
                          size="small"
                          icon={<SchoolIcon />}
                          color="secondary"
                          variant="outlined"
                          sx={{ fontSize: { xs: '0.75rem', sm: '0.8125rem' } }}
                        />
                      ) : (
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                        >
                          No course
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell sx={{ display: { xs: 'none', lg: 'table-cell' } }}>
                      {renderKeyConcepts(summary.key_concepts)}
                    </TableCell>
                    <TableCell sx={{ display: { xs: 'none', lg: 'table-cell' } }}>
                      {summary.topics && summary.topics.length > 0 ? (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {summary.topics.slice(0, 2).map((topic, index) => (
                            <Chip
                              key={index}
                              label={topic}
                              size="small"
                              variant="filled"
                              color="info"
                              sx={{ fontSize: '0.75rem' }}
                            />
                          ))}
                          {summary.topics.length > 2 && (
                            <Chip
                              label={`+${summary.topics.length - 2}`}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.75rem' }}
                            />
                          )}
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No topics
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}>
                      <Typography
                        variant="body2"
                        sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                      >
                        {formatDate(summary.created_at)}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{
                        display: 'flex',
                        gap: { xs: 0.5, sm: 1 },
                        justifyContent: 'center',
                        flexDirection: { xs: 'column', sm: 'row' }
                      }}>
                        <Tooltip title="View Summary">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleViewSummary(summary)}
                            sx={{ fontSize: { xs: 16, sm: 18 } }}
                          >
                            <ViewIcon sx={{ fontSize: { xs: 16, sm: 18 } }} />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Download as PDF">
                          <IconButton
                            size="small"
                            color="secondary"
                            onClick={() => handleDownloadSummary(summary)}
                            sx={{ fontSize: { xs: 16, sm: 18 } }}
                          >
                            <DownloadIcon sx={{ fontSize: { xs: 16, sm: 18 } }} />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Summary">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteClick(summary)}
                            disabled={deleteMutation.isPending}
                            sx={{ fontSize: { xs: 16, sm: 18 } }}
                          >
                            <DeleteIcon sx={{ fontSize: { xs: 16, sm: 18 } }} />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={summaries.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </>
      )}

      {/* Summary View Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { minHeight: '60vh' }
        }}
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <AutoAwesomeIcon sx={{ mr: 1, color: 'primary.main' }} />
            {selectedSummary?.title}
          </Box>
          <IconButton onClick={handleCloseDialog} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        
        <DialogContent dividers>
          {selectedSummary && (
            <Box>
              {/* Summary metadata */}
              <Card sx={{ mb: 3, backgroundColor: theme.palette.grey[50] }}>
                <CardContent sx={{ py: 2 }}>
                  <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 2 }}>
                    <Box>
                      <Typography variant="caption" color="text.secondary">Course</Typography>
                      <Typography variant="body2">
                        {selectedSummary.course_name || 'No course specified'}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="caption" color="text.secondary">Created</Typography>
                      <Typography variant="body2">
                        {formatDate(selectedSummary.created_at)}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>

              {/* Key concepts */}
              {selectedSummary.key_concepts && selectedSummary.key_concepts.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <CheckCircleIcon sx={{ mr: 1, color: 'success.main' }} />
                    Key Concepts
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {selectedSummary.key_concepts.map((concept, index) => (
                      <Chip
                        key={index}
                        label={typeof concept === 'string' ? concept : JSON.stringify(concept)}
                        color="primary"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {/* Topics */}
              {selectedSummary.topics && selectedSummary.topics.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Topics Covered
                  </Typography>
                  <List dense>
                    {selectedSummary.topics.map((topic, index) => (
                      <ListItem key={index} sx={{ py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <SchoolIcon color="secondary" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText primary={topic} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}

              <Divider sx={{ my: 2 }} />

              {/* Summary content */}
              <Typography variant="subtitle2" gutterBottom>
                Summary Content
              </Typography>
              <Paper variant="outlined" sx={{ p: 2, backgroundColor: 'background.default' }}>
                <Box
                  sx={{
                    lineHeight: 1.6,
                    '& h1': {
                      color: theme.palette.primary.main,
                      fontWeight: 'bold',
                      fontSize: '1.5rem',
                      mt: 3,
                      mb: 2,
                      borderBottom: `2px solid ${theme.palette.primary.main}`,
                      pb: 1
                    },
                    '& h2': {
                      color: theme.palette.primary.main,
                      fontWeight: 'bold',
                      fontSize: '1.25rem',
                      mt: 2.5,
                      mb: 1.5
                    },
                    '& h3': {
                      color: theme.palette.secondary.main,
                      fontWeight: 'bold',
                      fontSize: '1.1rem',
                      mt: 2,
                      mb: 1
                    },
                    '& p': {
                      mb: 1.5,
                      textAlign: 'justify'
                    },
                    '& ul, & ol': {
                      pl: 2,
                      mb: 1.5
                    },
                    '& li': {
                      mb: 0.5
                    },
                    '& strong': {
                      fontWeight: 'bold',
                      color: theme.palette.text.primary
                    },
                    '& em': {
                      fontStyle: 'italic',
                      color: theme.palette.text.secondary
                    },
                    '& blockquote': {
                      borderLeft: `4px solid ${theme.palette.primary.main}`,
                      pl: 2,
                      ml: 1,
                      fontStyle: 'italic',
                      backgroundColor: theme.palette.grey[50],
                      py: 1
                    }
                  }}
                >
                  <MathMarkdown>{selectedSummary.content}</MathMarkdown>
                </Box>
              </Paper>
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button
            startIcon={<DownloadIcon />}
            onClick={() => selectedSummary && handleDownloadSummary(selectedSummary)}
            color="secondary"
          >
            Download
          </Button>
          <Button onClick={handleCloseDialog} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-summary-dialog-title"
        aria-describedby="delete-summary-dialog-description"
      >
        <DialogTitle id="delete-summary-dialog-title">
          Delete Summary
        </DialogTitle>
        <DialogContent>
          <Typography id="delete-summary-dialog-description">
            Are you sure you want to delete this summary? This action cannot be undone.
          </Typography>
          {summaryToDelete && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                {summaryToDelete.title}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Course: {summaryToDelete.course_name || 'No course'}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GeneratedSummariesTable;
