import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Button,
  useTheme,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  PlayArrow as PlayArrowIcon,
  Assessment as AssessmentIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { getMCQGenerationJobs, deleteMCQGenerationJob } from '../../api/studentTools';
import { ProcessingStatus } from '../../types/studentTools';
import { formatDate } from '../../utils/formatters';

const GeneratedMCQsTable: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [jobToDelete, setJobToDelete] = useState<any>(null);

  // Fetch MCQ generation jobs
  const {
    data: jobs = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['mcqGenerationJobs'],
    queryFn: () => getMCQGenerationJobs(),
    staleTime: 30000, // 30 seconds
    refetchInterval: 10000 // Refetch every 10 seconds to update status
  });

  // Delete MCQ generation job mutation
  const deleteMutation = useMutation({
    mutationFn: deleteMCQGenerationJob,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mcqGenerationJobs'] });
      setDeleteDialogOpen(false);
      setJobToDelete(null);
    },
    onError: (error) => {
      console.error('Failed to delete MCQ generation job:', error);
    }
  });

  // Listen for refresh events
  useEffect(() => {
    const handleRefresh = () => {
      console.log('Refreshing MCQ jobs list...');
      refetch();
    };

    // Add event listener
    window.addEventListener('refreshNotes', handleRefresh);

    // Clean up
    return () => {
      window.removeEventListener('refreshNotes', handleRefresh);
    };
  }, [refetch]);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Get status color
  const getStatusColor = (status: ProcessingStatus) => {
    switch (status) {
      case ProcessingStatus.PENDING:
        return theme.palette.info.main;
      case ProcessingStatus.PROCESSING:
      case ProcessingStatus.GENERATING:
        return theme.palette.warning.main;
      case ProcessingStatus.COMPLETED:
        return theme.palette.success.main;
      case ProcessingStatus.FAILED:
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  // Handle practice button click
  const handlePractice = (job: any) => {
    if (!job) return;
    // Use a special URL format for note-generated MCQs that doesn't rely on course_id
    navigate(`/mcq/practice/note-generated?jobId=${job.id}`);
  };

  // Handle exam button click
  const handleExam = (job: any) => {
    if (!job) return;
    // Use a special URL format for note-generated MCQs that doesn't rely on course_id
    navigate(`/mcq/exam/note-generated?jobId=${job.id}`);
  };

  // Handle delete button click
  const handleDeleteClick = (job: any) => {
    setJobToDelete(job);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (jobToDelete) {
      deleteMutation.mutate(jobToDelete.id);
    }
  };

  // Handle delete cancellation
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setJobToDelete(null);
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load your generated MCQs. Please try again later.
      </Alert>
    );
  }

  if (jobs.length === 0) {
    return (
      <Alert severity="info" sx={{ mb: 2 }}>
        You don't have any generated MCQs yet. Please generate some MCQs first.
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <Tooltip title="Refresh MCQs">
          <IconButton onClick={() => refetch()}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      <TableContainer
        component={Paper}
        variant="outlined"
        sx={{
          borderRadius: 2,
          '& .MuiTable-root': {
            minWidth: { xs: 'auto', sm: 650 }
          }
        }}
      >
        <Table aria-label="generated MCQs table">
          <TableHead>
            <TableRow>
              <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}>Name</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Questions</TableCell>
              <TableCell sx={{ display: { xs: 'none', md: 'table-cell' } }}>Course</TableCell>
              <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}>Created</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {jobs
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((job) => (
                <TableRow key={job.id}>
                  <TableCell
                    component="th"
                    scope="row"
                    sx={{ display: { xs: 'none', sm: 'table-cell' } }}
                  >
                    <Typography variant="body2" sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' }, fontWeight: 500 }}>
                      {job.title || `MCQ Set #${job.id}`}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={job.status}
                      size="small"
                      sx={{
                        backgroundColor: getStatusColor(job.status),
                        color: 'white',
                        fontSize: { xs: '0.75rem', sm: '0.8125rem' }
                      }}
                    />
                    {job.status === ProcessingStatus.PROCESSING && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                        <CircularProgress
                          variant="determinate"
                          value={job.progress_percentage}
                          size={16}
                          sx={{ mr: 1 }}
                        />
                        <Typography variant="caption" sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}>
                          {job.progress_percentage}%
                        </Typography>
                      </Box>
                    )}
                  </TableCell>
                  <TableCell>
                    {job.generated_question_ids ? (
                      <Typography variant="body2" sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                        {job.generated_question_ids.length} questions
                      </Typography>
                    ) : (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                      >
                        {job.question_count} (requested)
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell sx={{ display: { xs: 'none', md: 'table-cell' } }}>
                    {job.course_id ? (
                      <Typography variant="body2" sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                        Course ID: {job.course_id}
                      </Typography>
                    ) : (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}
                      >
                        Auto-detected
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}>
                    <Typography variant="body2" sx={{ fontSize: { xs: '0.8rem', sm: '0.875rem' } }}>
                      {formatDate(job.created_at)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{
                      display: 'flex',
                      gap: { xs: 0.5, sm: 1 },
                      flexDirection: { xs: 'column', sm: 'row' },
                      alignItems: { xs: 'stretch', sm: 'center' }
                    }}>
                      {job.status === ProcessingStatus.COMPLETED && (
                        <>
                          <Button
                            size="small"
                            variant="outlined"
                            color="primary"
                            startIcon={<PlayArrowIcon sx={{ fontSize: { xs: 16, sm: 18 } }} />}
                            onClick={() => handlePractice(job)}
                            sx={{
                              fontSize: { xs: '0.75rem', sm: '0.8125rem' },
                              px: { xs: 1, sm: 1.5 },
                              py: { xs: 0.5, sm: 0.75 },
                              minWidth: { xs: 'auto', sm: 'auto' }
                            }}
                          >
                            Practice
                          </Button>
                          <Button
                            size="small"
                            variant="outlined"
                            color="secondary"
                            startIcon={<AssessmentIcon sx={{ fontSize: { xs: 16, sm: 18 } }} />}
                            onClick={() => handleExam(job)}
                            sx={{
                              fontSize: { xs: '0.75rem', sm: '0.8125rem' },
                              px: { xs: 1, sm: 1.5 },
                              py: { xs: 0.5, sm: 0.75 },
                              minWidth: { xs: 'auto', sm: 'auto' }
                            }}
                          >
                            Exam
                          </Button>
                        </>
                      )}
                      <Tooltip title="Delete MCQ Set">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteClick(job)}
                          disabled={deleteMutation.isPending}
                          sx={{
                            fontSize: { xs: 16, sm: 18 }
                          }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={jobs.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Delete MCQ Set
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete this MCQ set? This will permanently delete all {jobToDelete?.generated_question_ids?.length || jobToDelete?.question_count || 0} questions in this set. This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GeneratedMCQsTable;
