import React from 'react';
import { Navigate, useParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface TutorRedirectProps {
  children: React.ReactNode;
  tutorPath: string;
  preserveParams?: boolean;
}

/**
 * Component that redirects tutors to their specific routes
 * while allowing students to access the original route
 */
const TutorRedirect: React.FC<TutorRedirectProps> = ({ children, tutorPath, preserveParams = false }) => {
  const { user } = useAuth();
  const params = useParams();

  // If user is a tutor, redirect to the tutor-specific path
  if (user?.role === 'tutor') {
    let redirectPath = tutorPath;

    // If preserveParams is true and we have an id param, append it to the path
    if (preserveParams && params.id) {
      redirectPath = `${tutorPath}/${params.id}`;
    }

    return <Navigate to={redirectPath} replace />;
  }

  // Otherwise, render the children (for students and admins)
  return <>{children}</>;
};

export default TutorRedirect;
