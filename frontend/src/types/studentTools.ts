export enum ProcessingStatus {
  PENDING = "pending",
  UPLOADING = "uploading",
  PROCESSING = "processing",
  EMBEDDING = "embedding",
  GENERATING = "generating",
  COMPLETED = "completed",
  FAILED = "failed"
}

export interface NoteSummary {
  id: number;
  student_id: number;
  title: string;
  content: string;
  key_concepts?: string[];
  structure?: any;
  course_name?: string;
  topics?: string[];
  job_id?: number;
  created_at: string;
  updated_at: string;
}

export interface SummaryGenerationJob {
  id: number;
  student_id: number;
  note_ids: number[];
  course_name?: string;
  status: ProcessingStatus;
  progress_percentage: number;
  generated_summary_ids?: number[];
  error_message?: string;
  processing_metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface SummaryGenerationResponse {
  job_id: number;
  status: ProcessingStatus;
  progress_percentage: number;
  message: string;
}

export interface SummaryGenerationStatusResponse extends SummaryGenerationJob {}

export interface SummaryGenerationRequest {
  note_ids: number[];
  course_name?: string;
}
