import apiClient from './client';
import {
  ProcessingStatus,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SummaryGenerationResponse,
  SummaryGenerationStatusResponse,
  SummaryGenerationRequest
} from '../types/studentTools';
import { Question } from './questions';

export interface StudentNote {
  id: number;
  student_id: number;
  file_name: string;
  file_path: string;
  file_size: number;
  content_type: string;
  status: ProcessingStatus;
  embedding_id?: string;
  course_name?: string;
  detected_topics?: string[];
  processing_metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface StudentNoteUploadResponse {
  note_id: number;
  file_name: string;
  status: ProcessingStatus;
  message: string;
}

export interface MCQGenerationJob {
  id: number;
  student_id: number;
  note_ids: number[];
  title?: string;
  course_name?: string;
  question_count: number;
  status: ProcessingStatus;
  progress_percentage: number;
  generated_question_ids?: number[];
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface MCQGenerationResponse {
  job_id: number;
  status: ProcessingStatus;
  progress_percentage: number;
  message: string;
}

export interface MCQGenerationStatusResponse extends MCQGenerationJob {}

export interface FlashCard {
  id: number;
  student_id: number;
  front_content: string;
  back_content: string;
  course_name?: string;
  topic?: string;
  job_id?: number;
  created_at: string;
  updated_at: string;
}

export interface FlashCardGenerationJob {
  id: number;
  student_id: number;
  note_ids: number[];
  title?: string;
  course_name?: string;
  card_count: number;
  status: ProcessingStatus;
  progress_percentage: number;
  generated_card_ids?: number[];
  error_message?: string;
  created_at: string;
  updated_at: string;
}



export interface FlashCardGenerationResponse {
  job_id: number;
  status: ProcessingStatus;
  progress_percentage: number;
  message: string;
}

export interface FlashCardGenerationStatusResponse extends FlashCardGenerationJob {}

/**
 * Upload PDF notes
 * @param formData FormData containing files
 * @returns List of upload responses
 */
export const uploadNotes = async (formData: FormData): Promise<StudentNoteUploadResponse[]> => {
  try {
    console.log('Sending upload request to API...');
    const response = await apiClient.post('/student-tools/notes/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      // Increase timeout for file uploads
      timeout: 120000, // 120 seconds (2 minutes) for larger files
    });
    console.log('Upload API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in uploadNotes:', error);
    throw error;
  }
};

/**
 * Get all notes for the current student
 * @param skip Number of items to skip
 * @param limit Maximum number of items to return
 * @returns List of student notes
 */
export const getStudentNotes = async (skip: number = 0, limit: number = 100): Promise<StudentNote[]> => {
  const response = await apiClient.get('/student-tools/notes', {
    params: { skip, limit },
  });
  return response.data;
};

/**
 * Get a specific note by ID
 * @param noteId ID of the note
 * @returns Student note
 */
export const getStudentNote = async (noteId: number): Promise<StudentNote> => {
  const response = await apiClient.get(`/student-tools/notes/${noteId}`);
  return response.data;
};

/**
 * Delete a student note
 * @param noteId ID of the note to delete
 * @returns Promise that resolves when note is deleted
 */
export const deleteNote = async (noteId: number): Promise<{ message: string; note_id: number }> => {
  try {
    console.log('Deleting note:', noteId);

    const response = await apiClient.delete(`/student-tools/notes/${noteId}`);

    console.log('Delete note response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error deleting note:', error);
    throw error;
  }
};

/**
 * Generate MCQs from notes
 * @param noteIds List of note IDs
 * @param courseName Optional course name
 * @param questionCount Number of questions to generate
 * @returns MCQ generation response
 */
export const generateMCQs = async (
  noteIds: number[],
  courseName?: string,
  questionCount: number = 60
): Promise<MCQGenerationResponse> => {
  try {
    console.log('Sending MCQ generation request with:', {
      note_ids: noteIds,
      course_name: courseName,
      question_count: questionCount,
    });

    const response = await apiClient.post('/student-tools/mcq/generate', {
      note_ids: noteIds,
      course_name: courseName,
      question_count: questionCount,
    });

    console.log('MCQ generation response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in generateMCQs:', error);
    throw error;
  }
};

/**
 * Get the status of an MCQ generation job
 * @param jobId ID of the MCQ generation job
 * @returns MCQ generation status
 */
export const getMCQGenerationStatus = async (jobId: number): Promise<MCQGenerationStatusResponse> => {
  const response = await apiClient.get(`/student-tools/mcq/job/${jobId}`);
  return response.data;
};

/**
 * Get all MCQ generation jobs for the current student
 * @param skip Number of items to skip
 * @param limit Maximum number of items to return
 * @returns List of MCQ generation jobs
 */
export const getMCQGenerationJobs = async (skip: number = 0, limit: number = 100): Promise<MCQGenerationJob[]> => {
  const response = await apiClient.get('/student-tools/mcq/jobs', {
    params: { skip, limit },
  });
  return response.data;
};

/**
 * Generate flash cards from notes
 * @param noteIds List of note IDs
 * @param courseName Optional course name
 * @param cardCount Number of flash cards to generate
 * @returns Flash card generation response
 */
export const generateFlashCards = async (
  noteIds: number[],
  courseName?: string,
  cardCount: number = 30
): Promise<FlashCardGenerationResponse> => {
  try {
    console.log('Sending flash card generation request with:', {
      note_ids: noteIds,
      course_name: courseName,
      card_count: cardCount,
    });

    const response = await apiClient.post('/student-tools/flash-cards/generate', {
      note_ids: noteIds,
      course_name: courseName,
      card_count: cardCount,
    });

    console.log('Flash card generation response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in generateFlashCards:', error);
    throw error;
  }
};

/**
 * Get the status of a flash card generation job
 * @param jobId ID of the flash card generation job
 * @returns Flash card generation status
 */
export const getFlashCardGenerationStatus = async (jobId: number): Promise<FlashCardGenerationStatusResponse> => {
  console.log(`Getting flash card generation status for job ID: ${jobId}`);

  try {
    const response = await apiClient.get(`/student-tools/flash-cards/job/${jobId}`);
    console.log(`Flash card generation status response for job ID ${jobId}:`, response.data);
    return response.data;
  } catch (error) {
    console.error(`Error getting flash card generation status for job ID ${jobId}:`, error);
    throw error;
  }
};

/**
 * Get all flash card generation jobs for the current student
 * @param skip Number of items to skip
 * @param limit Maximum number of items to return
 * @returns List of flash card generation jobs
 */
export const getFlashCardGenerationJobs = async (skip: number = 0, limit: number = 100): Promise<FlashCardGenerationJob[]> => {
  const response = await apiClient.get('/student-tools/flash-cards/jobs', {
    params: { skip, limit },
  });
  return response.data;
};

/**
 * Get flash cards for the current student
 * @param options Options for filtering flash cards
 * @param options.skip Number of items to skip
 * @param options.limit Maximum number of items to return
 * @param options.jobId Optional job ID to filter flash cards by generation job
 * @returns List of flash cards
 */
export const getFlashCards = async (options: {
  skip?: number;
  limit?: number;
  jobId?: number;
} = {}): Promise<FlashCard[]> => {
  const { skip = 0, limit = 100, jobId } = options;

  console.log('getFlashCards called with options:', options);

  try {
    // Make sure job_id is properly passed as a parameter
    const params: Record<string, any> = { skip, limit };
    if (jobId !== undefined) {
      params.job_id = jobId;

      // If we're fetching by job_id, also try to get the job status for debugging
      try {
        const jobStatus = await getFlashCardGenerationStatus(jobId);
        console.log(`Job status for ID ${jobId} in getFlashCards:`, jobStatus);
        console.log(`Job has ${jobStatus.generated_card_ids?.length || 0} generated_card_ids:`, jobStatus.generated_card_ids);
      } catch (jobError) {
        console.error(`Error fetching job status for job ${jobId} in getFlashCards:`, jobError);
      }
    }

    console.log('Making API request to /student-tools/flash-cards with params:', params);

    const response = await apiClient.get('/student-tools/flash-cards', { params });

    console.log('Flash cards API response:', response.data);

    if (jobId !== undefined && response.data.length === 0) {
      console.warn(`API returned 0 cards for job ${jobId} in getFlashCards. This might indicate an issue with the job_id association.`);
    }

    return response.data;
  } catch (error) {
    console.error('Error fetching flash cards:', error);
    throw error;
  }
};

/**
 * Generate summary for a specific note
 * @param noteId ID of the note to summarize
 * @returns Summary generation response
 */
export const generateSummaryForNote = async (noteId: number): Promise<SummaryGenerationResponse> => {
  try {
    console.log('Sending summary generation request for note:', noteId);

    const response = await apiClient.post(`/student-tools/notes/${noteId}/summarize`);

    console.log('Summary generation response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in generateSummaryForNote:', error);
    throw error;
  }
};

/**
 * Generate summaries from multiple notes
 * @param noteIds List of note IDs
 * @param courseName Optional course name
 * @returns Summary generation response
 */
export const generateSummaries = async (
  noteIds: number[],
  courseName?: string
): Promise<SummaryGenerationResponse> => {
  try {
    console.log('Sending summary generation request with:', {
      note_ids: noteIds,
      course_name: courseName,
    });

    const response = await apiClient.post('/student-tools/summaries/generate', {
      note_ids: noteIds,
      course_name: courseName,
    });

    console.log('Summary generation response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in generateSummaries:', error);
    throw error;
  }
};

/**
 * Get the status of a summary generation job
 * @param jobId ID of the summary generation job
 * @returns Summary generation status
 */
export const getSummaryGenerationStatus = async (jobId: number): Promise<SummaryGenerationStatusResponse> => {
  console.log(`Getting summary generation status for job ID: ${jobId}`);

  try {
    const response = await apiClient.get(`/student-tools/summaries/status/${jobId}`);
    console.log(`Summary generation status response for job ID ${jobId}:`, response.data);
    return response.data;
  } catch (error) {
    console.error(`Error getting summary generation status for job ID ${jobId}:`, error);
    throw error;
  }
};

/**
 * Get summaries for the current student
 * @param options Options for filtering summaries
 * @param options.skip Number of items to skip
 * @param options.limit Maximum number of items to return
 * @param options.jobId Optional job ID to filter summaries by generation job
 * @param options.courseName Optional course name to filter summaries
 * @returns List of summaries
 */
export const getSummaries = async (options: {
  skip?: number;
  limit?: number;
  jobId?: number;
  courseName?: string;
} = {}): Promise<NoteSummary[]> => {
  const { skip = 0, limit = 100, jobId, courseName } = options;

  console.log('getSummaries called with options:', options);

  try {
    const params: Record<string, any> = { skip, limit };
    if (jobId !== undefined) {
      params.job_id = jobId;
    }
    if (courseName !== undefined) {
      params.course_name = courseName;
    }

    console.log('Making API request to /student-tools/summaries with params:', params);

    const response = await apiClient.get('/student-tools/summaries', { params });

    console.log('Summaries API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching summaries:', error);
    throw error;
  }
};

/**
 * Download a summary as PDF
 * @param summaryId ID of the summary to download
 * @returns Promise that resolves when download starts
 */
export const downloadSummaryAsPdf = async (summaryId: number): Promise<void> => {
  try {
    console.log('Downloading summary as PDF:', summaryId);

    const response = await apiClient.get(`/student-tools/summaries/${summaryId}/download-pdf`, {
      responseType: 'blob', // Important for binary data
    });

    // Create blob URL and trigger download
    const blob = new Blob([response.data], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);

    // Extract filename from Content-Disposition header if available
    const contentDisposition = response.headers['content-disposition'];
    let filename = `summary_${summaryId}.pdf`;

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename=(.+)/);
      if (filenameMatch) {
        filename = filenameMatch[1].replace(/"/g, ''); // Remove quotes
      }
    }

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    console.log('PDF download completed:', filename);
  } catch (error) {
    console.error('Error downloading summary as PDF:', error);
    throw error;
  }
};

/**
 * Get MCQ questions generated from notes
 * @param skip Number of items to skip
 * @param limit Maximum number of items to return
 * @param jobId Optional job ID to filter MCQs by generation job
 * @returns List of MCQ questions
 */
export const getMCQs = async (skip: number = 0, limit: number = 100, jobId?: number): Promise<Question[]> => {
  try {
    console.log('getMCQs called with:', { skip, limit, jobId });

    if (jobId) {
      // If jobId is provided, first get the job status to get the question IDs
      const jobStatus = await getMCQGenerationStatus(jobId);
      console.log('Job status for getMCQs:', jobStatus);

      if (!jobStatus.generated_question_ids || jobStatus.generated_question_ids.length === 0) {
        console.log('No generated question IDs found for job:', jobId);
        return [];
      }

      // Fetch the actual questions using the question IDs
      const questionPromises = jobStatus.generated_question_ids.map(async (questionId) => {
        try {
          const response = await apiClient.get(`/questions/${questionId}`);
          return response.data;
        } catch (error) {
          console.error(`Error fetching question ${questionId}:`, error);
          return null;
        }
      });

      const questions = await Promise.all(questionPromises);
      const validQuestions = questions.filter(q => q !== null);

      console.log(`Fetched ${validQuestions.length} questions for job ${jobId}`);
      return validQuestions;
    } else {
      // If no jobId, get all MCQ generation jobs and fetch their questions
      console.log('getMCQs called without jobId - fetching all user MCQs');

      try {
        // Get all MCQ generation jobs for the user
        const jobs = await getMCQGenerationJobs(0, 100);
        console.log('Found MCQ generation jobs:', jobs.length);

        // Collect all question IDs from all completed jobs
        const allQuestionIds: number[] = [];
        for (const job of jobs) {
          if (job.status === ProcessingStatus.COMPLETED && job.generated_question_ids) {
            allQuestionIds.push(...job.generated_question_ids);
          }
        }

        console.log('Total question IDs found:', allQuestionIds.length);

        if (allQuestionIds.length === 0) {
          return [];
        }

        // Fetch all questions
        const questionPromises = allQuestionIds.map(async (questionId) => {
          try {
            const response = await apiClient.get(`/questions/${questionId}`);
            return response.data;
          } catch (error) {
            console.error(`Error fetching question ${questionId}:`, error);
            return null;
          }
        });

        const questions = await Promise.all(questionPromises);
        const validQuestions = questions.filter(q => q !== null);

        console.log(`Fetched ${validQuestions.length} total MCQ questions`);
        return validQuestions;
      } catch (error) {
        console.error('Error fetching all MCQs:', error);
        return [];
      }
    }
  } catch (error) {
    console.error('Error in getMCQs:', error);
    throw error;
  }
};

/**
 * Delete an MCQ generation job and all its associated questions
 * @param jobId ID of the MCQ generation job to delete
 * @returns Promise that resolves when MCQ generation job is deleted
 */
export const deleteMCQGenerationJob = async (jobId: number): Promise<{ message: string; job_id: number }> => {
  try {
    console.log('Deleting MCQ generation job:', jobId);

    const response = await apiClient.delete(`/student-tools/mcq/job/${jobId}`);

    console.log('Delete MCQ generation job response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error deleting MCQ generation job:', error);
    throw error;
  }
};

/**
 * Delete an MCQ question
 * @param mcqId ID of the MCQ to delete
 * @returns Promise that resolves when MCQ is deleted
 */
export const deleteMCQ = async (mcqId: number): Promise<{ message: string; mcq_id: number }> => {
  try {
    console.log('Deleting MCQ:', mcqId);

    const response = await apiClient.delete(`/student-tools/mcq/${mcqId}`);

    console.log('Delete MCQ response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error deleting MCQ:', error);
    throw error;
  }
};

/**
 * Delete a flash card generation job and all its associated cards
 * @param jobId ID of the flash card generation job to delete
 * @returns Promise that resolves when flash card generation job is deleted
 */
export const deleteFlashCardGenerationJob = async (jobId: number): Promise<{ message: string; job_id: number }> => {
  try {
    console.log('Deleting flash card generation job:', jobId);

    const response = await apiClient.delete(`/student-tools/flash-cards/job/${jobId}`);

    console.log('Delete flash card generation job response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error deleting flash card generation job:', error);
    throw error;
  }
};

/**
 * Delete a flash card
 * @param cardId ID of the flash card to delete
 * @returns Promise that resolves when flash card is deleted
 */
export const deleteFlashCard = async (cardId: number): Promise<{ message: string; card_id: number }> => {
  try {
    console.log('Deleting flash card:', cardId);

    const response = await apiClient.delete(`/student-tools/flash-cards/${cardId}`);

    console.log('Delete flash card response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error deleting flash card:', error);
    throw error;
  }
};

/**
 * Delete a summary
 * @param summaryId ID of the summary to delete
 * @returns Promise that resolves when summary is deleted
 */
export const deleteSummary = async (summaryId: number): Promise<{ message: string; summary_id: number }> => {
  try {
    console.log('Deleting summary:', summaryId);

    const response = await apiClient.delete(`/student-tools/summaries/${summaryId}`);

    console.log('Delete summary response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error deleting summary:', error);
    throw error;
  }
};
