import apiClient from './client';

export interface Course {
  id: number;
  name: string;
  description: string;
  code: string;
  is_active: boolean;
  school_id: number;
  department_id?: number;
  created_at: string;
  updated_at: string;
}

export interface CourseCreate {
  name: string;
  description: string;
  code: string;
  is_active?: boolean;
  school_id: number;
  department_id?: number;
}

export interface CourseUpdate {
  name?: string;
  description?: string;
  code?: string;
  is_active?: boolean;
  school_id?: number;
  department_id?: number;
}

export const getCourses = async (
  schoolId?: number,
  departmentId?: number,
  prioritizeUserDepartment: boolean = false,
  prioritizeEnrolled: boolean = false
): Promise<Course[]> => {
  let url = '/courses';
  const params: Record<string, any> = {};

  if (schoolId) {
    params.school_id = schoolId;
  }

  if (departmentId) {
    params.department_id = departmentId;
  }

  if (prioritizeUserDepartment) {
    params.prioritize_user_department = true;
  }

  if (prioritizeEnrolled) {
    params.prioritize_enrolled = true;
  }

  const response = await apiClient.get<Course[]>(url, { params });
  return response.data;
};

export const getCourse = async (id: number): Promise<Course> => {
  const response = await apiClient.get<Course>(`/courses/${id}`);
  return response.data;
};

export const createCourse = async (data: CourseCreate): Promise<Course> => {
  const response = await apiClient.post<Course>('/courses', data);
  return response.data;
};

export const updateCourse = async (id: number, data: CourseUpdate): Promise<Course> => {
  const response = await apiClient.put<Course>(`/courses/${id}`, data);
  return response.data;
};

export const deleteCourse = async (id: number): Promise<Course> => {
  const response = await apiClient.delete<Course>(`/courses/${id}`);
  return response.data;
};

export const enrollInCourse = async (id: number): Promise<Course> => {
  const response = await apiClient.post<Course>(`/courses/${id}/enroll`, {});
  return response.data;
};
