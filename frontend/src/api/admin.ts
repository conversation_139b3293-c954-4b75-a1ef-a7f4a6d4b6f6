import apiClient from './client';

export interface AdminDashboardStats {
  users: {
    total: number;
    students: number;
    tutors: number;
    admins: number;
    recent: number;
  };
  schools: {
    total: number;
    active: number;
    inactive: number;
  };
  departments: {
    total: number;
    active: number;
    inactive: number;
  };
  courses: {
    total: number;
    active: number;
    inactive: number;
  };
  questions: {
    total: number;
    active: number;
    inactive: number;
  };
}

export interface AdminLoginRequest {
  username: string;
  password: string;
}

export interface AdminAuthResponse {
  access_token: string;
  token_type: string;
}

export interface BulkUpdateRequest {
  user_ids: number[];
  update_data: {
    is_active?: boolean;
    role?: 'student' | 'tutor' | 'admin';
  };
}

export interface BulkUpdateResponse {
  updated_count: number;
  errors: string[];
  success: boolean;
}

// Admin authentication
export const adminLogin = async (data: AdminLoginRequest): Promise<AdminAuthResponse> => {
  try {
    console.log('Admin login attempt with username:', data.username);

    // Create URLSearchParams for x-www-form-urlencoded format
    const formData = new URLSearchParams();
    formData.append('username', data.username);
    formData.append('password', data.password);

    console.log('Sending admin login request to API...');
    const response = await apiClient.post<AdminAuthResponse>('/admin/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      timeout: 5000,
    });

    console.log('Admin login successful');
    return response.data;
  } catch (error: any) {
    console.error('Admin login failed:', error);
    
    if (error.response) {
      console.error('Error response:', error.response.data);
      throw new Error(error.response.data.detail || 'Admin login failed');
    } else if (error.request) {
      console.error('Network error during admin login');
      throw new Error('Network error. Please check your connection and try again.');
    } else {
      console.error('Unexpected error during admin login:', error.message);
      throw new Error('An unexpected error occurred. Please try again.');
    }
  }
};

// Get dashboard statistics
export const getAdminDashboardStats = async (): Promise<AdminDashboardStats> => {
  try {
    const response = await apiClient.get<AdminDashboardStats>('/admin/dashboard/stats');
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch admin dashboard stats:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch dashboard statistics');
  }
};

// Get users for bulk operations
export const getUsersForBulkActions = async (params?: {
  role?: 'student' | 'tutor' | 'admin';
  is_active?: boolean;
  skip?: number;
  limit?: number;
}) => {
  try {
    const response = await apiClient.get('/admin/users/bulk-actions', { params });
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch users for bulk actions:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch users');
  }
};

// Bulk update users
export const bulkUpdateUsers = async (data: BulkUpdateRequest): Promise<BulkUpdateResponse> => {
  try {
    const response = await apiClient.put<BulkUpdateResponse>('/admin/users/bulk-update', data);
    return response.data;
  } catch (error: any) {
    console.error('Failed to bulk update users:', error);
    throw new Error(error.response?.data?.detail || 'Failed to update users');
  }
};
