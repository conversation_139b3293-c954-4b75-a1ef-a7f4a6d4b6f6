import apiClient from './client';

// Question Attempt Types
export interface QuestionAttempt {
  id: number;
  student_id: number;
  question_id: number;
  is_correct: boolean;
  selected_answer: string | null;
  attempt_time: string;
}

export interface QuestionAttemptCreate {
  student_id: number;
  question_id: number;
  is_correct: boolean;
  selected_answer?: string;
}

// Bookmark Types
export interface Bookmark {
  id: number;
  student_id: number;
  question_id: number;
  created_at: string;
  notes: string | null;
}

export interface BookmarkCreate {
  student_id: number;
  question_id: number;
  notes?: string;
}

export interface BookmarkUpdate {
  notes?: string;
}

// Exam Types
export interface Exam {
  id: number;
  student_id: number;
  course_id: number;
  start_time: string;
  end_time: string | null;
  score: number | null;
  total_questions: number;
  correct_answers: number | null;
}

export interface ExamCreate {
  student_id: number;
  course_id: number;
  total_questions: number;
  score?: number;
  correct_answers?: number;
  end_time?: string;
  // Fields for note-generated content
  is_note_generated?: boolean;
  note_job_id?: number;
  course_name?: string;
}

export interface ExamUpdate {
  score?: number;
  correct_answers?: number;
  end_time?: string;
}

export interface ExamAttempt {
  id: number;
  exam_id: number;
  question_id: number;
  is_correct: boolean;
  selected_answer: string | null;
  time_spent_seconds?: number;
}

export interface ExamAttemptCreate {
  exam_id: number;
  question_id: number;
  is_correct: boolean;
  selected_answer?: string;
  time_spent_seconds?: number;
}

export interface ExamWithAttempts extends Exam {
  attempts: ExamAttempt[];
}

// Question Attempt API Functions
export const createQuestionAttempt = async (data: QuestionAttemptCreate): Promise<QuestionAttempt> => {
  const response = await apiClient.post<QuestionAttempt>('/student-progress/attempts', data);
  return response.data;
};

export const getQuestionAttempts = async (
  studentId: number,
  questionId?: number
): Promise<QuestionAttempt[]> => {
  const params: Record<string, any> = { student_id: studentId };
  if (questionId) params.question_id = questionId;

  const response = await apiClient.get<QuestionAttempt[]>('/student-progress/attempts', { params });
  return response.data;
};

export const getCourseAttempts = async (
  studentId: number,
  courseId: number
): Promise<QuestionAttempt[]> => {
  const params: Record<string, any> = { student_id: studentId };

  const response = await apiClient.get<QuestionAttempt[]>(
    `/student-progress/attempts/course/${courseId}`,
    { params }
  );
  return response.data;
};

// Bookmark API Functions
export const createBookmark = async (data: BookmarkCreate): Promise<Bookmark> => {
  const response = await apiClient.post<Bookmark>('/student-progress/bookmarks', data);
  return response.data;
};

export const getBookmarks = async (
  studentId: number,
  courseId?: number
): Promise<Bookmark[]> => {
  const params: Record<string, any> = { student_id: studentId };
  if (courseId) params.course_id = courseId;

  const response = await apiClient.get<Bookmark[]>('/student-progress/bookmarks', { params });
  return response.data;
};

export const updateBookmark = async (
  id: number,
  data: BookmarkUpdate
): Promise<Bookmark> => {
  const response = await apiClient.put<Bookmark>(`/student-progress/bookmarks/${id}`, data);
  return response.data;
};

export const deleteBookmark = async (id: number): Promise<Bookmark> => {
  const response = await apiClient.delete<Bookmark>(`/student-progress/bookmarks/${id}`);
  return response.data;
};

// Exam API Functions
export const createExam = async (data: ExamCreate): Promise<Exam> => {
  const response = await apiClient.post<Exam>('/student-progress/exams', data);
  return response.data;
};

export const getExams = async (
  studentId: number,
  courseId?: number
): Promise<Exam[]> => {
  const params: Record<string, any> = { student_id: studentId };
  if (courseId) params.course_id = courseId;

  const response = await apiClient.get<Exam[]>('/student-progress/exams', { params });
  return response.data;
};

export const getExamWithAttempts = async (id: number): Promise<ExamWithAttempts> => {
  const response = await apiClient.get<ExamWithAttempts>(`/student-progress/exams/${id}`);
  return response.data;
};

export const updateExam = async (
  id: number,
  data: ExamUpdate
): Promise<Exam> => {
  const response = await apiClient.put<Exam>(`/student-progress/exams/${id}`, data);
  return response.data;
};

export const createExamAttempt = async (data: ExamAttemptCreate): Promise<ExamAttempt> => {
  const response = await apiClient.post<ExamAttempt>('/student-progress/exams/attempts', data);
  return response.data;
};

// Topic Analysis Types
export interface TopicAnalysis {
  [topic: string]: {
    correct_count: number;
    total_count: number;
    time_spent_total: number;
    score: number;
    avg_time_spent: number;
    exams_count?: number;
  };
}

// Topic Analysis API Functions
export const getExamTopicAnalysis = async (examId: number): Promise<TopicAnalysis> => {
  const response = await apiClient.get<TopicAnalysis>(`/student-progress/exams/${examId}/topic-analysis`);
  return response.data;
};

export const getCourseTopicAnalysis = async (
  studentId: number,
  courseId: number
): Promise<TopicAnalysis> => {
  const params: Record<string, any> = { student_id: studentId };
  const response = await apiClient.get<TopicAnalysis>(
    `/student-progress/courses/${courseId}/topic-analysis`,
    { params }
  );
  return response.data;
};
