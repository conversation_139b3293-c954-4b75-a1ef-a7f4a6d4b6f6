#!/usr/bin/env python3

import logging
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app import models
from sqlalchemy import text

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def seed_schools_and_departments():
    """Seed database with Nigerian schools and departments"""
    db = SessionLocal()

    try:
        # Test database connection first
        logger.info("Testing database connection...")
        result = db.execute(text("SELECT 1")).fetchone()
        logger.info(f"Database connection successful: {result[0]}")

        # Check if we already have schools
        existing_schools = db.query(models.School).count()
        logger.info(f"Found {existing_schools} existing schools")

        if existing_schools > 0:
            logger.info("Schools already exist, clearing existing data first...")
            # Clear existing data
            db.execute(text("DELETE FROM department"))
            db.execute(text("DELETE FROM school"))
            db.commit()
            logger.info("Cleared existing data")
        
        logger.info("Starting to seed Nigerian schools and departments...")
        
        # Nigerian universities data
        nigerian_universities = [
            {
                "name": "University of Lagos",
                "description": "UNILAG",
                "location": "Lagos",
                "departments": [
                    {"name": "Computer Science", "description": "Department of Computer Science"},
                    {"name": "Mathematics", "description": "Department of Mathematics"},
                    {"name": "Physics", "description": "Department of Physics"},
                    {"name": "Chemistry", "description": "Department of Chemistry"},
                    {"name": "Economics", "description": "Department of Economics"},
                ]
            },
            {
                "name": "University of Ibadan",
                "description": "UI",
                "location": "Ibadan",
                "departments": [
                    {"name": "Computer Science", "description": "Department of Computer Science"},
                    {"name": "Mathematics", "description": "Department of Mathematics"},
                    {"name": "Physics", "description": "Department of Physics"},
                    {"name": "Chemistry", "description": "Department of Chemistry"},
                    {"name": "Economics", "description": "Department of Economics"},
                ]
            },
            {
                "name": "Obafemi Awolowo University",
                "description": "OAU",
                "location": "Ile-Ife",
                "departments": [
                    {"name": "Computer Science", "description": "Department of Computer Science"},
                    {"name": "Mathematics", "description": "Department of Mathematics"},
                    {"name": "Physics", "description": "Department of Physics"},
                    {"name": "Chemistry", "description": "Department of Chemistry"},
                    {"name": "Economics", "description": "Department of Economics"},
                ]
            },
            {
                "name": "Ahmadu Bello University",
                "description": "ABU",
                "location": "Zaria",
                "departments": [
                    {"name": "Computer Science", "description": "Department of Computer Science"},
                    {"name": "Mathematics", "description": "Department of Mathematics"},
                    {"name": "Physics", "description": "Department of Physics"},
                    {"name": "Chemistry", "description": "Department of Chemistry"},
                    {"name": "Economics", "description": "Department of Economics"},
                ]
            },
            {
                "name": "University of Nigeria",
                "description": "UNN",
                "location": "Nsukka",
                "departments": [
                    {"name": "Computer Science", "description": "Department of Computer Science"},
                    {"name": "Mathematics", "description": "Department of Mathematics"},
                    {"name": "Physics", "description": "Department of Physics"},
                    {"name": "Chemistry", "description": "Department of Chemistry"},
                    {"name": "Economics", "description": "Department of Economics"},
                ]
            }
        ]
        
        # Create schools and departments
        for uni_data in nigerian_universities:
            # Create school
            school = models.School(
                name=uni_data["name"],
                description=uni_data["description"],
                location=uni_data["location"],
                is_active=True
            )
            db.add(school)
            db.flush()  # Flush to get the ID
            
            logger.info(f"Created school: {school.name}")
            
            # Create departments for this school
            for dept_data in uni_data["departments"]:
                department = models.Department(
                    name=dept_data["name"],
                    description=dept_data["description"],
                    school_id=school.id,
                    is_active=True
                )
                db.add(department)
                logger.info(f"  Created department: {department.name}")
        
        db.commit()
        logger.info("Successfully seeded Nigerian schools and departments!")
        
        # Print summary
        schools_count = db.query(models.School).count()
        departments_count = db.query(models.Department).count()
        logger.info(f"Total schools: {schools_count}")
        logger.info(f"Total departments: {departments_count}")
        
    except Exception as e:
        logger.error(f"Error during seeding: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    seed_schools_and_departments()
