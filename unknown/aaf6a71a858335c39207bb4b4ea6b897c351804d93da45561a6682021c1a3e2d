#!/usr/bin/env python3

import sqlite3
import os
from datetime import datetime

def seed_database():
    """Directly seed the database with SQL"""
    db_path = os.path.join(os.path.dirname(__file__), 'campuspq.db')
    print(f"Database path: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check existing tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"Existing tables: {[t[0] for t in tables]}")
        
        # Clear existing data
        cursor.execute("DELETE FROM department")
        cursor.execute("DELETE FROM school")
        
        # Insert schools
        schools_data = [
            ("University of Lagos", "UNILAG", "Lagos", 1),
            ("University of Ibadan", "UI", "Ibadan", 1),
            ("Obafemi Awolowo University", "OAU", "Ile-Ife", 1),
            ("Ahmadu Bello University", "ABU", "Zaria", 1),
            ("University of Nigeria", "UNN", "Nsukka", 1),
        ]
        
        now = datetime.now().isoformat()
        
        for name, desc, location, is_active in schools_data:
            cursor.execute("""
                INSERT INTO school (name, description, location, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (name, desc, location, is_active, now, now))
            print(f"Inserted school: {name}")
        
        # Get school IDs and insert departments
        cursor.execute("SELECT id, name FROM school")
        schools = cursor.fetchall()
        
        departments = ["Computer Science", "Mathematics", "Physics", "Chemistry", "Economics"]
        
        for school_id, school_name in schools:
            for dept_name in departments:
                cursor.execute("""
                    INSERT INTO department (name, description, school_id, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (dept_name, f"Department of {dept_name}", school_id, 1, now, now))
                print(f"Inserted department: {dept_name} for {school_name}")
        
        conn.commit()
        
        # Verify data
        cursor.execute("SELECT COUNT(*) FROM school")
        school_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM department")
        dept_count = cursor.fetchone()[0]
        
        print(f"\nSeeding completed!")
        print(f"Total schools: {school_count}")
        print(f"Total departments: {dept_count}")
        
    except Exception as e:
        print(f"Error: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    seed_database()
