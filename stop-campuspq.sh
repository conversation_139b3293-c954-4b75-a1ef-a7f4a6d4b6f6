#!/bin/bash

# CampusPQ Stop Script
# This script stops all CampusPQ services

echo "🛑 Stopping CampusPQ Application Stack..."
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Stop frontend processes
print_status "Stopping frontend processes..."
pkill -f "npm run dev" || true
pkill -f "vite" || true
print_success "Frontend processes stopped"

# Stop backend services
print_status "Stopping backend services..."
cd backend
docker compose down
print_success "Backend services stopped"

cd ..

print_success "All CampusPQ services have been stopped"
echo
print_status "To start again, run: ./start-campuspq.sh"
